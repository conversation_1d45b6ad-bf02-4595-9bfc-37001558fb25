# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Android SIP VoIP intercom application (`softphone1`) built for communication systems, door access control, and security features. The app integrates with FreePBX/Asterisk SIP servers and includes hardware control via i2c driver for door locks and security systems.

## Build Commands

### Environment Requirements
- Android Studio Koala Feature Drop | 2024.1.2
- Gradle JDK 1.8.0_361 (must use JDK 1.8, not JDK 11)
- Target SDK: Android 28 (API level 28)

### Building
```bash
cd SIPSample_AndroidStudio
./gradlew build              # Build the project
./gradlew assembleDebug      # Build debug APK
./gradlew assembleRelease    # Build release APK
```

### Android Studio Setup
- Open `SIPSample_AndroidStudio/SIPSample` in Android Studio
- Settings -> Build Tools -> Gradle -> Change JDK from 11 to 1.8
- Connect target device (e.g., rockchip rk312x)
- Run 'SipSample' configuration

## Architecture Overview

### Core Service Architecture
The app follows a service-oriented architecture with three main SIP services:

- **PortSipService**: Primary SIP registration and call handling
- **PortSipService2/3**: Extended SIP functionality for multi-line support
- **WaService**: Hardware abstraction layer for door control and security features via i2c driver

### Key Application Components
- **MyApplication**: Global application state manager, initializes all services
- **MainActivity**: Primary UI coordinator with fragment-based navigation
- **ConsoleActivity**: Main dashboard interface (console.xml layout)

### Hardware Integration Layer
- **i2cDriver**: Native interface to hardware control (door locks, sensors)
- Native libraries for multiple architectures in `libs/` folder
- Hardware-specific UI: door control, alarm management, security features

### Data Flow
1. SIP events handled by PortSipService implementations
2. Hardware events processed through WaService and i2cDriver
3. UI state coordinated through MainActivity and Application layer
4. Settings persisted via SettingConfig utility class

## Key Technical Details

### Dependencies
- PortSIP SDK (portsipvoipsdk.jar): Core VoIP functionality
- Firebase Messaging: Push notifications for incoming calls
- VLC libraries (pedrovlc module): Video streaming
- RxJava 2: Reactive programming patterns

### Build Configuration
- Application ID: `weema.Android.sip`
- Version managed in `SIPSample/build.gradle`
- Custom keystore: `android_self.keystore`
- APK output format: `softphone1-{buildType}-{versionName}.apk`

### Hardware Permissions
The app requires extensive hardware access:
- Camera/microphone for video calls
- Network state monitoring
- System-level permissions for door control
- Boot receiver for auto-start capability

## File Structure Notes
- Main source: `SIPSample_AndroidStudio/SIPSample/src/main/java/com/weema/sipsample/`
- UI layouts: `SIPSample_AndroidStudio/SIPSample/src/main/res/layout/`
- Key layouts: `console.xml` (main), `setting_new.xml` (config), `activity_alarm.xml` (security)
- Native libraries: `SIPSample_AndroidStudio/SIPSample/libs/`