
# Softphone1

`softphone1` 是一個基於 Android 平台開發的 SIP VoIP 對講 App，支援音視頻通話、門鈴通知、錄影與多分機管理等功能。專案使用 PortSIP SDK，並包含對門禁與對講需求的擴充。

## 功能特色

- SIP 註冊撥打網路電話
- 視訊通話與門鈴影像
- 門禁遠端開門
- SOS 警報與緊急撥號
- 通話錄影與影像存檔
- 可註冊至 FreePBX / Asterisk Sip Server

## 專案結構

```
SIPSample_AndroidStudio/SIPSample/src/main/
├─ java/com/weema/sipsample/
│   ├─ service/                # SIP 與保全門禁服務
│   ├─ service/WaService       # ptool 設定、保全觸發功能
│   ├─ service/PortSipService  # sip 註冊、撥號功能
│   ├─ service/MyApplication   # 上帝模組
│   ├─ util/                   # 工具類
│   ├─ log/                    # ptool 讀取 Log 模組
│   ├─ ui/                     # UI 與 Application
│   └─ ...                     # 其他模組
├─ res/layout
│   ├─ console.xml             # 首頁畫面
│   ├─ activity_alarm.xml      # 警報畫面
│   ├─ setting_new.xml         # 主要設定畫面
│   ├─ volume.xml              # 音量設定畫面

SIPSample_AndroidStudio/SIPSample/build.gradle # 設定版本名稱
```

## 測試環境

- Android Studio Koala Feature Drop | 2024.1.2
- Gradle JDK 1.8.0_361
- FreePBX **********

## 快速開始

1.Clone 專案
```bash
git clone https://github.com/weema/softphone1.git
cd softphone1
git checkout for-freepbx-airport-city
````

2.在 Android Studio 中開啟 SIPSample_AndroidStudio/SIPSample

3.Android Studio -> Settings ->Build, Execution, Deployment -> Build Tools -> Gradle -> 從 JDK 11 改為 JDK 1.8

4.將對講機與電腦連結後，選擇裝置如: rockchip rk312x

5.Android Studio -> Run -> Run 'SipSample'
