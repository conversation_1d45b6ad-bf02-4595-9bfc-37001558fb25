<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":SIPSample" external.linked.project.path="$MODULE_DIR$/SIPSample" external.root.project.path="$MODULE_DIR$" external.system.id="GRADLE" external.system.module.group="SIPSample_AndroidStudio" external.system.module.version="unspecified" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android-gradle" name="Android-Gradle">
      <configuration>
        <option name="GRADLE_PROJECT_PATH" value=":SIPSample" />
      </configuration>
    </facet>
    <facet type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="debug" />
        <option name="SELECTED_TEST_ARTIFACT" value="_android_test_" />
        <option name="ASSEMBLE_TASK_NAME" value="assembleDebug" />
        <option name="COMPILE_JAVA_TASK_NAME" value="compileDebugSources" />
        <option name="ASSEMBLE_TEST_TASK_NAME" value="assembleDebugAndroidTest" />
        <option name="COMPILE_JAVA_TEST_TASK_NAME" value="compileDebugAndroidTestSources" />
        <afterSyncTasks>
          <task>generateDebugAndroidTestSources</task>
          <task>generateDebugSources</task>
        </afterSyncTasks>

        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/SIPSample/src/main/res" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_6" inherit-compiler-output="false">
    <output url="file://$MODULE_DIR$/SIPSample/build/intermediates/classes/debug" />
    <output-test url="file://$MODULE_DIR$/SIPSample/build/intermediates/classes/androidTest/debug" />
    <exclude-output />
    <content url="file://$MODULE_DIR$/SIPSample">
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/source/r/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/source/aidl/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/source/buildConfig/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/source/rs/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/res/rs/debug" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/res/resValues/debug" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/source/r/androidTest/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/source/aidl/androidTest/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/source/buildConfig/androidTest/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/source/rs/androidTest/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/res/rs/androidTest/debug" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/build/generated/res/resValues/androidTest/debug" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/debug/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/debug/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/debug/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/debug/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/debug/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/debug/jni" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/debug/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/main/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/main/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/main/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/main/jni" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/main/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/androidTest/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/androidTest/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/androidTest/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/androidTest/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/androidTest/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/androidTest/jni" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/SIPSample/src/androidTest/rs" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/assets" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/bundles" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/classes" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/coverage-instrumented-classes" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/dependency-cache" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/dex" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/dex-cache" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/exploded-aar/com.android.support/appcompat-v7/19.1.0/jars" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/incremental" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/jacoco" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/javaResources" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/libs" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/lint" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/manifests" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/ndk" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/pre-dexed" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/proguard" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/res" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/rs" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/intermediates/symbols" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/outputs" />
      <excludeFolder url="file://$MODULE_DIR$/SIPSample/build/tmp" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 19 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" exported="" name="support-v4-19.1.0" level="project" />
    <orderEntry type="library" exported="" name="appcompat-v7-19.1.0" level="project" />
    <orderEntry type="library" exported="" name="portsipvoipsdk" level="project" />
  </component>
</module>
