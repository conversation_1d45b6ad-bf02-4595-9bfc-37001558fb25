<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":SIPSample" external.linked.project.path="$MODULE_DIR$" external.root.project.path="$MODULE_DIR$/.." external.system.id="GRADLE" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android-gradle" name="Android-Gradle">
      <configuration>
        <option name="GRADLE_PROJECT_PATH" value=":SIPSample" />
      </configuration>
    </facet>
    <facet type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="debug" />
        <option name="ASSEMBLE_TASK_NAME" value="assembleDebug" />
        <option name="COMPILE_JAVA_TASK_NAME" value="compileDebugSources" />
        <afterSyncTasks>
          <task>generateDebugSources</task>
        </afterSyncTasks>
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/src/main/res" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes" />
    <output-test url="file://$MODULE_DIR$/build/intermediates/javac/debugUnitTest/compileDebugUnitTestJavaWithJavac/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/apt/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/not_namespaced_r_class_sources/debug/processDebugResources/r" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/aidl/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/buildConfig/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/rs/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/res/google-services/debug" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/res/rs/debug" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/res/resValues/debug" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/apt/androidTest/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/not_namespaced_r_class_sources/debugAndroidTest/processDebugAndroidTestResources/r" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/aidl/androidTest/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/buildConfig/androidTest/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/rs/androidTest/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/res/rs/androidTest/debug" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/res/resValues/androidTest/debug" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/build/generated/source/apt/test/debug" isTestSource="true" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/debug/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTestDebug/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/testDebug/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/assets" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/aidl" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/rs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/shaders" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/androidTest/shaders" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/res" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/assets" type="java-test-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/aidl" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/rs" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/shaders" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/annotation_processor_list" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/apk_list" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/blame" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/build-info" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/builds" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/check-libraries" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/check-manifest" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/checkDebugClasspath" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/checkReleaseClasspath" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/compatible_screen_manifest" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/incremental" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/incremental-classes" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/incremental-runtime-classes" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/incremental-verifier" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/instant-run-apk" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/instant-run-resources" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/instant_run_merged_manifests" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/instant_run_split_apk_resources" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/javac" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/jniLibs" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/manifest-checker" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/merged_assets" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/merged_manifests" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/prebuild" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/processed_res" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/reload-dex" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/res" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/resources" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/rs" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/shader_assets" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/shaders" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/split-apk" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/split_list" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/symbols" />
      <excludeFolder url="file://$MODULE_DIR$/build/intermediates/transforms" />
      <excludeFolder url="file://$MODULE_DIR$/build/outputs" />
      <excludeFolder url="file://$MODULE_DIR$/build/reports" />
      <excludeFolder url="file://$MODULE_DIR$/build/tmp" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 28 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="com.android.support:support-core-utils-25.2.0" level="project" />
    <orderEntry type="library" name="com.google.firebase:firebase-core-11.0.0" level="project" />
    <orderEntry type="library" name="com.google.firebase:firebase-analytics-impl-11.0.0" level="project" />
    <orderEntry type="library" name="com.android.support:support-core-ui-25.2.0" level="project" />
    <orderEntry type="library" name="__local_aars__:/Users/<USER>/Documents/sourcetree/softphone1-16.3/SIPSample_AndroidStudio/SIPSample/libs/portsipvoipsdk.jar:unspecified@jar" level="project" />
    <orderEntry type="library" name="com.google.gms:google-services:3.0.0@jar" level="project" />
    <orderEntry type="library" name="com.android.support:support-media-compat-25.2.0" level="project" />
    <orderEntry type="library" name="com.google.android.gms:play-services-basement-11.0.0" level="project" />
    <orderEntry type="library" name="com.android.support:support-compat-25.2.0" level="project" />
    <orderEntry type="library" name="com.google.firebase:firebase-analytics-11.0.0" level="project" />
    <orderEntry type="library" name="com.google.firebase:firebase-common-11.0.0" level="project" />
    <orderEntry type="library" name="com.android.support:support-annotations:25.2.0@jar" level="project" />
    <orderEntry type="library" name="com.google.android.gms:play-services-tasks-11.0.0" level="project" />
    <orderEntry type="library" name="com.google.firebase:firebase-messaging-11.0.0" level="project" />
    <orderEntry type="library" name="com.google.firebase:firebase-iid-11.0.0" level="project" />
    <orderEntry type="library" name="com.android.support:support-v4-25.2.0" level="project" />
    <orderEntry type="library" name="com.android.support:support-fragment-25.2.0" level="project" />
  </component>
</module>