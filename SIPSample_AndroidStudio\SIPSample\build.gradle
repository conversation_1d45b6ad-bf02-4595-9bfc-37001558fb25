apply plugin: 'com.android.application'

android {
    compileSdkVersion 28

    defaultConfig {
        applicationId "weema.Android.sip"  // this is the id that your app has

        minSdkVersion 16
        targetSdkVersion 28
        versionCode 16
        versionName "2025-06-24 16_50"

        buildConfigField "boolean", "SUPPORTPUSH", "true"

        buildConfigField "boolean", "HASVIDEO", "true"//false
        buildConfigField "boolean", "HASIM", "true"//false
        buildConfigField "boolean", "HASSIPTAILER", "true"
        buildConfigField "boolean", "HASPRESENCE", "true"

        buildConfigField "boolean", "ENABLEVIDEO", "true"
        buildConfigField "boolean", "ENABLEIM", "true"

        buildConfigField "String", "PROVIDER_DATA", "\"com.portsip.portgo.data\""
        buildConfigField "String", "PROVIDER_CONTACT", "\"com.portsip.portgo.contacts\""
        buildConfigField "String", "PORT_ACTION_CALL", "\"com.portsip.portgo.action.CALL\""
        buildConfigField "String", "PORT_ACTION_REJECT", "\"com.portsip.portgo.action.REJECT\""
        buildConfigField "String", "PORT_ACTION_ACCEPT", "\"com.portsip.portgo.action.ACCEPT\""
        buildConfigField "String", "PORT_ACTION_REGIEST", "\"com.portsip.portgo.action.REGIEST\""
        buildConfigField "String", "PORT_ACTION_UNREGIEST", "\"com.portsip.portgo.action.UNREGIEST\""
        buildConfigField "String", "PORT_ACTION_TOKEN", "\"com.portsip.portgo.action.TOKEN\""
        buildConfigField "String", "PORT_ACTION_AUDIODEVICE", "\"com.portsip.portgo.action.AUDIODEVICE\""
        buildConfigField "String", "PORT_ACTION_AUTOLOGIN", "\"com.portsip.portgo.action.AUTOLOGIN\""
        buildConfigField "String", "PORT_ACTION_DIALERVIEW", "\"com.portsip.portgo.action.DIALERVIEW\""
        buildConfigField "String", "PORT_ACTION_ALTERVIEW", "\"com.portsip.portgo.action.ALTERVIEW\""
       
    }
    
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def appName = "softphone1" // 自定義名稱
            def versionName = variant.versionName
            def buildType = variant.buildType.name
            outputFileName = "${appName}-${buildType}-${versionName}.apk"
        }
    }

    signingConfigs {
        debug {
            storeFile file("../../android_self.keystore") // 自定義 Keystore 文件的路徑
            storePassword "19831226"                      // Keystore 密碼
            keyAlias "android_self"                       // 密鑰別名
            keyPassword "19831226"                        // 密鑰密碼
        }
    }

    buildTypes {
        sourceSets {
            main {
                jniLibs.srcDirs = ['libs']
            }
        }
        release {
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

   lintOptions {
        
        checkReleaseBuilds false
    }        
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.google.gms:google-services:3.0.0'
    implementation 'com.google.firebase:firebase-messaging:11.0.0'
    implementation project(':pedrovlc')

    // RxJava
    implementation 'io.reactivex.rxjava2:rxjava:2.1.7'
    implementation 'io.reactivex.rxjava2:rxandroid:2.0.1'
    implementation 'com.jakewharton.rxbinding2:rxbinding:2.0.0'

}

apply plugin: 'com.google.gms.google-services'
