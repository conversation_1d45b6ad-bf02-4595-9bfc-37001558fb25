## Project-wide Gradle settings.
#
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#Mon Feb 01 10:09:25 CST 2016
#systemProp.http.proxyHost=http://mirrors.neusoft.edu.cn/
#systemProp.http.proxyPort=80

#org.gradle.jvmargs=-DsocksProxyHost=127.0.0.1 -DsocksProxyPort=1080
#systemProp.http.proxyHost=127.0.0.1
#systemProp.http.proxyPort=1080
#systemProp.http.proxyUser=userid
#systemProp.http.proxyPassword=password
#systemProp.http.nonProxyHosts=*.nonproxyrepos.com|localhost
#
#systemProp.https.proxyHost=127.0.0.1
#systemProp.https.proxyPort=1080
#systemProp.https.proxyUser=userid
#systemProp.https.proxyPassword=password
#systemProp.https.nonProxyHosts=*.nonproxyrepos.com|localhost
android.useAndroidX=true
android.enableJetifier=true