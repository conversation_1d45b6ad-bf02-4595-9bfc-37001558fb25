package com.weema.sipsample.log;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.InputStreamReader;

public class LogCapture {
    private static final String TAG = "LogCapture";

    public static File capture(Context context, String fileName) {
        try {
            Process process = Runtime.getRuntime().exec("logcat -d");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            File logFile = new File(context.getFilesDir(), fileName);
            FileOutputStream fos = new FileOutputStream(logFile);

            String line;
            while ((line = reader.readLine()) != null) {
                fos.write((line + "\n").getBytes());
            }

            fos.close();
            reader.close();
            return logFile;

        } catch (Exception e) {
            Log.e(TAG, "capture failed: " + e.getMessage());
            return null;
        }
    }
}
