package com.weema.sipsample.log;

import android.content.Context;
import android.os.Environment;
import android.util.Log;
import org.json.JSONArray;
import org.json.JSONObject;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

public class LogFileExecutor {
    private static final String TAG = "LogFileExecutor";

    public static String getSystemLog(Context context) {
        try {
            String fileName = "system_log.txt";
            File logFile = LogCapture.capture(context, fileName);

            if (logFile == null || !logFile.exists()) {
                return "{\"status\":\"error\", \"message\":\"log file create failed\"}";
            }

            // 再讀檔
            FileInputStream fis = new FileInputStream(logFile);
            byte[] bytes = new byte[(int) logFile.length()];
            fis.read(bytes);
            fis.close();
            String logContent = new String(bytes);

            // 回傳 JSON
            JSONObject result = new JSONObject();
            result.put("status", "success");
            result.put("target", "system");
            result.put("content", logContent);
            result.put("timestamp", System.currentTimeMillis());

            return result.toString();

        } catch (Exception e) {
            Log.e(TAG, "getSystemLog failed: " + e.getMessage());
            return "{\"status\":\"error\"}";
        }
    }

    /**
     * 使用 CrashLogCatch 規則找 crash log 檔案
     */
    public static String getCrashFileNames(Context context) {
        try {
            // 使用與 CrashLogCatch 相同路徑
            File dir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            
            if (dir == null) dir = context.getFilesDir();

            File[] files = dir.listFiles();
            List<String> crashFiles = new ArrayList<>();

            if (files != null) {
                for (File f : files) {
                    // CrashLogCatch 預設檔名前綴是 "strace-"
//                    if (f.getName().startsWith("strace-") && f.getName().endsWith(".txt")) {
//                        crashFiles.add(f.getName());
//                    }
                    crashFiles.add(f.getName());
                }
            }

            JSONObject result = new JSONObject();
            result.put("status", "success");
            result.put("target", "crashfilenames");
            result.put("filenames", new JSONArray(crashFiles));
            return result.toString();

        } catch (Exception e) {
            Log.e(TAG, "getCrashFileNames failed: " + e.getMessage());
            return "{\"status\":\"error\"}";
        }
    }


    public static String getCrashFile(Context context, String filename) {
        try {
            
            // 與 CrashLogCatch 相同
            File dir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            File file = new File(dir, filename);

            if (!file.exists()) {
                return "{\"status\":\"error\", \"message\":\"file not found\"}";
            }
            FileInputStream fis = new FileInputStream(file);
            byte[] bytes = new byte[(int) file.length()];
            fis.read(bytes);
            fis.close();
            String content = new String(bytes);

            JSONObject result = new JSONObject();
            result.put("status", "success");
            result.put("target", "crashfile");
            result.put("filename", filename);
            result.put("content", content);
            return result.toString();
        } catch (Exception e) {
            Log.e(TAG, "getCrashFile failed: " + e.getMessage());
            return "{\"status\":\"error\"}";
        }
    }
}
