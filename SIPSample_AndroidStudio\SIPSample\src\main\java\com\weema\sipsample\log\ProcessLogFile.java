package com.weema.sipsample.log;
import android.content.Context;

// 解析 ptool 指令並回傳 log 資料

/*
// 取得系統 log
CONFIG,GETLOGFILE,system
{
    "status": "success",
    "target": "system",
    "content": "Log file retrieved successfully.",
    "timestamp": "2025-01-01T12:00:00Z",
}

// 取得崩潰 log 檔案列表
CONFIG,GETLOGFILE,crashfilenames
{
    "status": "success",
    "target": "crashfilenames",
    "filenames": [
        "crashfile_2025-01-01_12-00-00.txt",
        "crashfile_2025-01-02_12-00-00.txt"
    ]
}

// 取得指定崩潰 log 檔案
CONFIG,GETLOGFILE,crashfile,2025-01-01_12-00-00.txt
{
    "status": "success",
    "target": "crashfile",
    "filename": "crashfile_2025-01-01_12-00-00.txt",
    "content": "Log content here..."
}
 */

public class ProcessLogFile {

    /**
     * 解析指令，丟給執行器執行
     * @param context Context
     * @param command 格式：CONFIG,GETLOGFILE,target[,filename]
     * @return 執行結果 JSON
     */
    public static String handleCommand(Context context, String command) {
        if (command == null || command.isEmpty()) {
            return "{\"status\":\"error\", \"message\":\"empty command\"}";
        }

        String[] parts = command.split(",");
        if (parts.length < 3) {
            return "{\"status\":\"error\", \"message\":\"invalid command format\"}";
        }

        String target = parts[2];
        switch (target) {
            case "system":
                return LogFileExecutor.getSystemLog(context);
            case "crashfilenames":
                return LogFileExecutor.getCrashFileNames(context);
            case "crashfile":
                if (parts.length >= 4) {
                    String filename = parts[3];
                    return LogFileExecutor.getCrashFile(context, filename);
                } else {
                    return "{\"status\":\"error\", \"message\":\"missing filename\"}";
                }
            default:
                return "{\"status\":\"error\", \"message\":\"unknown target\"}";
        }
    }
}
