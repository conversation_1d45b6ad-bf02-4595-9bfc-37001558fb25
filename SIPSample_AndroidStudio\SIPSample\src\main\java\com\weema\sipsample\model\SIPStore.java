package com.weema.sipsample.model;


import io.reactivex.Observable;
import io.reactivex.subjects.PublishSubject;

public class SIPStore {
 
    private PublishSubject<Boolean> subjectRinging =
            PublishSubject.create();

    public SIPStore(){
        
    }
    public void putRinging(Boolean value) {
       
        subjectRinging.onNext(value);
    }

    public Observable<Boolean> getStream() {
        return subjectRinging.hide();
    }
}
