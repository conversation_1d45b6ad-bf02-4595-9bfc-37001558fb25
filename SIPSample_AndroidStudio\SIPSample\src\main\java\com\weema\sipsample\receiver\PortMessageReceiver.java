package com.weema.sipsample.receiver;

import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.IBinder;
import android.os.PowerManager;
import android.preference.PreferenceManager;
import android.text.TextUtils;

import com.portsip.OnPortSIPEvent;
import com.portsip.PortSipEnumDefine;
import com.portsip.PortSipSdk;
import com.weema.R;
import com.weema.sipsample.ui.MainActivity;
import com.weema.sipsample.ui.MyApplication;
import com.weema.sipsample.util.CallManager;
import com.weema.sipsample.util.Contact;
import com.weema.sipsample.util.ContactManager;
import com.weema.sipsample.util.Ring;
import com.weema.sipsample.util.Session;

import java.util.Random;
import java.util.UUID;

public class PortMessageReceiver extends BroadcastReceiver
{
    public interface BroadcastListener {int OnBroadcastReceiver(Intent intent);}
    @Override
    public void onReceive(Context context, Intent intent) {
        int ret;
   
        ret = -1;
        if(broadcastReceiver!=null) {
            ret = broadcastReceiver.OnBroadcastReceiver(intent);
            if(ret >= 0)
                return;

        }

        if(mainBroadcastReceiver!=null) {
            mainBroadcastReceiver.OnBroadcastReceiver(intent);
        }
    }
    public BroadcastListener broadcastReceiver;
    public BroadcastListener mainBroadcastReceiver;
}
