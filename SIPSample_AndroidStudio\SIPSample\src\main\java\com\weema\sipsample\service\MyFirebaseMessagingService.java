package com.weema.sipsample.service;
import com.weema.sipsample.ui.MainActivity;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
//import android.support.v4.app.NotificationCompat;
import android.util.Log;
import com.weema.R;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import static com.weema.sipsample.service.PortSipService.ACTION_PUSH_MESSAGE;

public class MyFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "MyFirebaseMsgService";
    private int notifyID = 0;

    @Override
    public void handleIntent(Intent intent){
        String action = intent.getAction();
        if(action.equals("com.google.android.c2dm.intent.RECEIVE")&&intent.getStringExtra("message_type")==null) {
            Bundle bundle = intent.getExtras();
            if ("call".equals(bundle.getString("msg_type")))
            {
                Intent srvIntent = new Intent(this, PortSipService.class);
                srvIntent.setAction(ACTION_PUSH_MESSAGE);
                startService(srvIntent);
            }
            if ("im".equals(bundle.getString("msg_type")))
            {
                String content = bundle.getString("msg_content");
                String from = bundle.getString("send_from");
                String to = bundle.getString("send_to");
                String pushid = bundle.getString("portsip-push-id");
                Intent srvIntent = new Intent(this, PortSipService.class);
                srvIntent.setAction(ACTION_PUSH_MESSAGE);
                startService(srvIntent);
            }

        }
    }
}
