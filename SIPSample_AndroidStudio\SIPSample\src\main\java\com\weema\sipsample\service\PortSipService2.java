package com.weema.sipsample.service;

import com.google.firebase.iid.FirebaseInstanceId;
import com.portsip.PortSipEnumDefine;
import com.portsip.PortSipErrorcode;
import com.portsip.PortSipSdk;
import com.portsip.OnPortSIPEvent;
import com.weema.R;

//import com.weema.sipsample.ui.IncomingActivity;
import com.weema.sipsample.ui.BaseMainActivity;
import com.weema.sipsample.ui.MainActivity;
import com.weema.sipsample.ui.MyApplication;
import com.weema.sipsample.util.CallManager;
import com.weema.sipsample.util.Contact;
import com.weema.sipsample.util.ContactManager;
import com.weema.sipsample.util.Ring;
import com.weema.sipsample.util.Session;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.IBinder;
import android.os.PowerManager;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.AndroidException;
//import android.widget.Toast;

import java.util.Random;
import java.util.UUID;

import android.util.Log;

public class PortSipService2 extends Service implements OnPortSIPEvent {
    private final int LINE_ID = 2;
    public static final String TAG = "PortSipService";

    public static final String RECEIVER_I2C = "PortSip.AndroidSample.I2C";

    public static final String ACTION_SIP_REGIEST = "PortSip.AndroidSample.Test.REGIEST";
    public static final String ACTION_SIP_UNREGIEST = "PortSip.AndroidSample.Test.UNREGIEST";
    public static final String ACTION_PUSH_MESSAGE = "PortSip.AndroidSample.Test.PushMessageIncoming";

    public static final String INSTANCE_ID = "instanceid";

    public static final String SIP_ENABLE = "sipenable";

    public static final String USER_NAME = "user name";
    public static final String USER_PWD = "user pwd";
    public static final String SVR_HOST = "svr host";
    public static final String SVR_PORT = "svr port";

    public static final String USER_DOMAIN = "user domain";
    public static final String USER_DISPALYNAME = "user dispalay";
    public static final String USER_AUTHNAME = "user authname";
    public static final String STUN_HOST = "stun host";
    public static final String STUN_PORT = "stun port";

    public static final String OUTBOUND_HOST = "outbound host";
    public static final String OUTBOUND_PORT = "outbound port";

    public static final String SIP_ENABLE2 = "sipenable2";

    public static final String USER_NAME2 = "user name2";
    public static final String USER_PWD2 = "user pwd2";
    public static final String SVR_HOST2 = "svr host2";
    public static final String SVR_PORT2 = "svr port2";

    public static final String USER_DOMAIN2 = "user domain2";
    public static final String USER_DISPALYNAME2 = "user dispalay2";
    public static final String USER_AUTHNAME2 = "user authname2";
    public static final String STUN_HOST2 = "stun host2";
    public static final String STUN_PORT2 = "stun port2";

    public static final String OUTBOUND_HOST2 = "outbound host2";
    public static final String OUTBOUND_PORT2 = "outbound port2";

    public static final String SIP_ENABLE3 = "sipenable3";

    public static final String USER_NAME3 = "user name3";
    public static final String USER_PWD3 = "user pwd3";
    public static final String SVR_HOST3 = "svr host3";
    public static final String SVR_PORT3 = "svr port3";

    public static final String USER_DOMAIN3 = "user domain3";
    public static final String USER_DISPALYNAME3 = "user dispalay3";
    public static final String USER_AUTHNAME3 = "user authname3";
    public static final String STUN_HOST3 = "stun host3";
    public static final String STUN_PORT3 = "stun port3";

    public static final String OUTBOUND_HOST3 = "outbound host3";
    public static final String OUTBOUND_PORT3 = "outbound port3";

    public static final String USER_NAME4 = "user name4";
    public static final String USER_PWD4 = "user pwd4";
    public static final String SVR_HOST4 = "svr host4";
    public static final String SVR_PORT4 = "svr port4";

    public static final String USER_DOMAIN4 = "user domain4";
    public static final String USER_DISPALYNAME4 = "user dispalay4";
    public static final String USER_AUTHNAME4 = "user authname4";
    public static final String STUN_HOST4 = "stun host4";
    public static final String STUN_PORT4 = "stun port4";

    public static final String TRANS = "trans type";
    public static final String SRTP = "srtp type";

    protected PowerManager.WakeLock mCpuLock;
    public static final String REGISTER_CHANGE_ACTION = "PortSip.AndroidSample.Test.RegisterStatusChagnge";
    public static final String CALL_CHANGE_ACTION = "PortSip.AndroidSample.Test.CallStatusChagnge";
    public static final String PRESENCE_CHANGE_ACTION = "PortSip.AndroidSample.Test.PRESENCEStatusChagnge";

    public static String EXTRA_REGISTER_LINE = "RegisterLine";
    public static String EXTRA_REGISTER_STATE = "RegisterStatus";
    public static String EXTRA_CALL_SEESIONID = "SessionID";
    public static String EXTRA_CALL_DESCRIPTION = "Description";

    private final String APPID = "weema.Android.sip";
    private PortSipSdk mEngine;
    private MyApplication application;
    private final int SERVICE_NOTIFICATION  = 31414;
    private String ChannelID = "PortSipService";

    public static final String ONHOOK_ACTION = "PortSip.AndroidSample.Test.Onhook";
    public static final String ANSWER_ACTION = "PortSip.AndroidSample.Test.Answer";
    public static final String AUTO_ONHOOK_ACTION = "PortSip.AndroidSample.Test.AutoOnhook";

    public static String EXTRA_ONHOOK = "Onhook";


    @Override
    public void onCreate() {
      
        super.onCreate();
        
        application = (MyApplication) getApplicationContext();

        application.setSdkService(LINE_ID,this);
        mEngine = application.getVoipLine(LINE_ID);

        showServiceNotifiCation();
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            NotificationChannel channel = new NotificationChannel(ChannelID, getString(R.string.app_name), NotificationManager.IMPORTANCE_DEFAULT);
            channel.enableLights(true);
            notificationManager.createNotificationChannel(channel);
        }

    }

    private void showServiceNotifiCation(){
        Intent intent = new Intent(this, BaseMainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        PendingIntent contentIntent = PendingIntent.getActivity(this, 0/*requestCode*/, intent, PendingIntent.FLAG_UPDATE_CURRENT);

        Notification.Builder builder;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            builder = new Notification.Builder(this,ChannelID);
        }else{
            builder = new Notification.Builder(this);
        }
        builder.setSmallIcon(R.drawable.icon)
                .setContentTitle(getString(R.string.app_name))
                .setContentText("Service Running")
                .setContentIntent(contentIntent)
                .build();// getNotification()
        startForeground(SERVICE_NOTIFICATION,builder.build());
    }
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        int result = super.onStartCommand(intent, flags, startId);
        if (intent != null) {
            if (ACTION_PUSH_MESSAGE.equals(intent.getAction()) && !CallManager.Instance().getregist(1)) {
                registerToServer();
            } else if (ACTION_SIP_REGIEST.equals(intent.getAction())) {
                registerToServer();
            } else if (ACTION_SIP_UNREGIEST.equals(intent.getAction())) {
                unregisterToServer();
            }

        }
        return result;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mEngine.destroyConference();

        if (mCpuLock != null) {
            mCpuLock.release();
        }
        NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        notificationManager.cancelAll();
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            notificationManager.deleteNotificationChannel(ChannelID);
        }

    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void registerToServer0() {
        SharedPreferences prefences = PreferenceManager.getDefaultSharedPreferences(this);
        String port = "5060";//prefences.getString(SVR_PORT, "5060");

        int srtptype = prefences.getInt(SRTP, 0);

        String registerTips = "initialize failed";
        int result = 0;
        mEngine.DeleteCallManager();
        mEngine.CreateCallManager(application);
        mEngine.setOnPortSIPEvent(this);
        String dataPath = getExternalFilesDir("p2p").getAbsolutePath();
        int localport = Integer.parseInt(port);
        result = mEngine.initialize(PortSipEnumDefine.ENUM_TRANSPORT_UDP, "0.0.0.0", localport,
                PortSipEnumDefine.ENUM_LOG_LEVEL_NONE, dataPath,
                8, "softphone1 "+MyApplication.VERSION, 0, 0, dataPath, "", false, null);
        if (result == PortSipErrorcode.ECoreErrorNone) {
            //init failed
            registerTips = "ECoreWrongLicenseKey";
            result = mEngine.setLicenseKey(MyApplication.myLicenseKey);

            if (result != PortSipErrorcode.ECoreWrongLicenseKey) {

                mEngine.getAudioDevices();
                mEngine.setVideoDeviceId(1);
                mEngine.setSrtpPolicy(srtptype);
                ConfigPresence(this, prefences, mEngine);

                mEngine.enable3GppTags(false);

                String name = prefences.getString(USER_NAME, "");
                String displayname = prefences.getString(USER_DISPALYNAME, "");
                String pwd = prefences.getString(USER_PWD, "");

                registerTips = "invalidate user info";
                result = -1;
                //if (!TextUtils.isEmpty(name) && !TextUtils.isEmpty(pwd))
                {


                    result = mEngine.setUser("100", "", null, "1234",
                           null, null, 0, null, 0, null, 0);
/*
                    result = mEngine.setUser(name, displayname, null, pwd,
                            null, null, 0, null, 0, null, 0);
*/
                    registerTips = "setUser failed";
                    if (result == PortSipErrorcode.ECoreErrorNone) {

                        mEngine.setInstanceId(getInstanceID());
                        //onRegisterSuccess("online", result, "");

                    }
                }
            }
        }

        if (result != PortSipErrorcode.ECoreErrorNone) {
            onRegisterFailure(registerTips, result, "");
        }
    }

    public void registerToServer() {
        if(LINE_ID == 0)
        {
          registerToServer0();
          return;
        }
        else if(LINE_ID == 2)
        {
          registerToServer2();
          return;
        }
        else if(LINE_ID == 3)
        {
          registerToServer3();
          return;
        }

        SharedPreferences prefences = PreferenceManager.getDefaultSharedPreferences(this);
        Random rm = new Random();
        int localport = 5060 + rm.nextInt(60000);

        int transtype = prefences.getInt(TRANS, 0);
        int srtptype = prefences.getInt(SRTP, 0);

        String registerTips = "initialize failed";
        int result = 0;
        mEngine.DeleteCallManager();
        mEngine.CreateCallManager(application);
        mEngine.setOnPortSIPEvent(this);
        String dataPath = getExternalFilesDir("line1").getAbsolutePath();

        result = mEngine.initialize(getTransType(transtype), "0.0.0.0", localport,
                PortSipEnumDefine.ENUM_LOG_LEVEL_NONE, dataPath,
                8, "softphone1 "+MyApplication.VERSION, 0, 0, dataPath, "", false, null);
        if(result == PortSipErrorcode.ECoreErrorNone) {
            //init failed
            registerTips = "ECoreWrongLicenseKey";
            result = mEngine.setLicenseKey(MyApplication.myLicenseKey);
            application.keyRet = result;

            if (result != PortSipErrorcode.ECoreWrongLicenseKey) {

                mEngine.setAudioDevice(PortSipEnumDefine.AudioDevice.SPEAKER_PHONE);
                mEngine.setVideoDeviceId(1);
                mEngine.setSrtpPolicy(srtptype);
                ConfigPresence(this, prefences, mEngine);

                mEngine.enable3GppTags(false);

                String name = prefences.getString(USER_NAME, "");
                String displayname = prefences.getString(USER_DISPALYNAME, "");
                String authname = prefences.getString(USER_AUTHNAME, "");
                String domain = prefences.getString(USER_DOMAIN, "");

                String pwd = prefences.getString(USER_PWD, "");
                String host = prefences.getString(SVR_HOST, "");
                String port = prefences.getString(SVR_PORT, "5060");
                String stunhost = prefences.getString(STUN_HOST, "");
                String stunport = prefences.getString(STUN_PORT, "3478");
                String outboundhost = prefences.getString(OUTBOUND_HOST, "");
                String outboundport = prefences.getString(OUTBOUND_PORT, "5060");

                registerTips = "invalidate user info";
                result = -1;
                if (!TextUtils.isEmpty(name) && !TextUtils.isEmpty(pwd)
                        && !TextUtils.isEmpty(host) && !TextUtils.isEmpty(port)) {

                    int sipServerPort;
                    int stunServerPort;
                    int outboundServerPort;
                    sipServerPort = Integer.parseInt(port);
                    stunServerPort = Integer.parseInt(stunport);
                    outboundServerPort = Integer.parseInt(outboundport);
                    result = mEngine.setUser(name, displayname, authname, pwd,
                            domain, host, sipServerPort, stunhost, stunServerPort, null, 5060);
                    registerTips = "setUser failed";
                    if (result == PortSipErrorcode.ECoreErrorNone) {
                        if (!TextUtils.isEmpty(FirebaseInstanceId.getInstance().getToken())) {
                            String pushMessage = "device-os=android;device-uid=" + FirebaseInstanceId.getInstance().getToken() + ";allow-call-push=true;allow-message-push=true;app-id=" + APPID;
                            mEngine.addSipMessageHeader(-1, "REGISTER", 1, "portsip-push", pushMessage);
                        }
                        mEngine.setInstanceId(getInstanceID());
                        registerTips = "registerServer failed";
                        result = mEngine.registerServer(90, 0);
                    }
                }
            }
        }

        if(result!=PortSipErrorcode.ECoreErrorNone){
            Intent broadIntent = new Intent(REGISTER_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_REGISTER_STATE,registerTips);
            broadIntent.putExtra(EXTRA_REGISTER_LINE,LINE_ID);
            sendPortSipMessage(registerTips + result, broadIntent);
            CallManager.Instance().myregist(LINE_ID,false);
            CallManager.Instance().myResetAll(LINE_ID);

            ///keepCpuRun(false);
        }
    }

    private void registerToServer2() {

        SharedPreferences prefences = PreferenceManager.getDefaultSharedPreferences(this);
        Random rm = new Random();
        int localport = 5060 + rm.nextInt(60000);

        int transtype = prefences.getInt(TRANS, 0);
        int srtptype = prefences.getInt(SRTP, 0);

        String registerTips = "initialize failed";
        int result = 0;
        mEngine.DeleteCallManager();
        mEngine.CreateCallManager(application);
        mEngine.setOnPortSIPEvent(this);
        String dataPath = getExternalFilesDir("line1").getAbsolutePath();

        result = mEngine.initialize(getTransType(transtype), "0.0.0.0", localport,
                PortSipEnumDefine.ENUM_LOG_LEVEL_NONE, dataPath,
                8, "softphone1 "+MyApplication.VERSION, 0, 0, dataPath, "", false, null);
        if(result == PortSipErrorcode.ECoreErrorNone) {
            //init failed
            registerTips = "ECoreWrongLicenseKey";
            result = mEngine.setLicenseKey(MyApplication.myLicenseKey);

            if (result != PortSipErrorcode.ECoreWrongLicenseKey) {

                mEngine.setAudioDevice(PortSipEnumDefine.AudioDevice.SPEAKER_PHONE);
                mEngine.setVideoDeviceId(1);
                mEngine.setSrtpPolicy(srtptype);
                ConfigPresence(this, prefences, mEngine);

                mEngine.enable3GppTags(false);

                String name = prefences.getString(USER_NAME2, "");
                String displayname = prefences.getString(USER_DISPALYNAME2, "");
                String authname = prefences.getString(USER_AUTHNAME2, "");
                String domain = prefences.getString(USER_DOMAIN2, "");

                String pwd = prefences.getString(USER_PWD2, "");
                String host = prefences.getString(SVR_HOST2, "");
                String port = prefences.getString(SVR_PORT2, "5060");
                String stunhost = prefences.getString(STUN_HOST, "");
                String stunport = prefences.getString(STUN_PORT, "3478");
                String outboundhost = prefences.getString(OUTBOUND_HOST2, "");
                String outboundport = prefences.getString(OUTBOUND_PORT2, "5060");

                registerTips = "invalidate user info";
                result = -1;
                if (!TextUtils.isEmpty(name) && !TextUtils.isEmpty(pwd)
                        && !TextUtils.isEmpty(host) && !TextUtils.isEmpty(port)) {

                    int sipServerPort;
                    int stunServerPort;
                    int outboundServerPort;
                    sipServerPort = Integer.parseInt(port);
                    stunServerPort = Integer.parseInt(stunport);
                    outboundServerPort = Integer.parseInt(outboundport);
                    result = mEngine.setUser(name, displayname, authname, pwd,
                            domain, host, sipServerPort, stunhost, stunServerPort, null, 5060);
                    registerTips = "setUser failed";
                    if (result == PortSipErrorcode.ECoreErrorNone) {
                        if (!TextUtils.isEmpty(FirebaseInstanceId.getInstance().getToken())) {
                            String pushMessage = "device-os=android;device-uid=" + FirebaseInstanceId.getInstance().getToken() + ";allow-call-push=true;allow-message-push=true;app-id=" + APPID;
                            mEngine.addSipMessageHeader(-1, "REGISTER", 1, "portsip-push", pushMessage);
                        }
                        mEngine.setInstanceId(getInstanceID());
                        registerTips = "registerServer failed";
                        result = mEngine.registerServer(90, 0);
                    }
                }
            }
        }

        if(result!=PortSipErrorcode.ECoreErrorNone){
            Intent broadIntent = new Intent(REGISTER_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_REGISTER_STATE,registerTips);
            broadIntent.putExtra(EXTRA_REGISTER_LINE,LINE_ID);
            sendPortSipMessage(registerTips + result, broadIntent);
            CallManager.Instance().myregist(LINE_ID,false);
            CallManager.Instance().myResetAll(LINE_ID);

            ///keepCpuRun(false);
        }
    }

    private void registerToServer3() {

        SharedPreferences prefences = PreferenceManager.getDefaultSharedPreferences(this);
        Random rm = new Random();
        int localport = 5060 + rm.nextInt(60000);

        int transtype = prefences.getInt(TRANS, 0);
        int srtptype = prefences.getInt(SRTP, 0);

        String registerTips = "initialize failed";
        int result = 0;
        mEngine.DeleteCallManager();
        mEngine.CreateCallManager(application);
        mEngine.setOnPortSIPEvent(this);
        String dataPath = getExternalFilesDir("line1").getAbsolutePath();

        result = mEngine.initialize(getTransType(transtype), "0.0.0.0", localport,
                PortSipEnumDefine.ENUM_LOG_LEVEL_NONE, dataPath,
                8, "softphone1 "+MyApplication.VERSION, 0, 0, dataPath, "", false, null);
        if(result == PortSipErrorcode.ECoreErrorNone) {
            //init failed
            registerTips = "ECoreWrongLicenseKey";
            result = mEngine.setLicenseKey(MyApplication.myLicenseKey);

            if (result != PortSipErrorcode.ECoreWrongLicenseKey) {

                mEngine.setAudioDevice(PortSipEnumDefine.AudioDevice.SPEAKER_PHONE);
                mEngine.setVideoDeviceId(1);
                mEngine.setSrtpPolicy(srtptype);
                ConfigPresence(this, prefences, mEngine);

                mEngine.enable3GppTags(false);

                String name = prefences.getString(USER_NAME3, "");
                String displayname = prefences.getString(USER_DISPALYNAME3, "");
                String authname = prefences.getString(USER_AUTHNAME3, "");
                String domain = prefences.getString(USER_DOMAIN3, "");

                String pwd = prefences.getString(USER_PWD3, "");
                String host = prefences.getString(SVR_HOST3, "");
                String port = prefences.getString(SVR_PORT3, "5060");
                String stunhost = prefences.getString(STUN_HOST, "");
                String stunport = prefences.getString(STUN_PORT, "3478");
                String outboundhost = prefences.getString(OUTBOUND_HOST3, "");
                String outboundport = prefences.getString(OUTBOUND_PORT3, "5060");

                registerTips = "invalidate user info";
                result = -1;
                if (!TextUtils.isEmpty(name) && !TextUtils.isEmpty(pwd)
                        && !TextUtils.isEmpty(host) && !TextUtils.isEmpty(port)) {

                    int sipServerPort;
                    int stunServerPort;
                    int outboundServerPort;
                    sipServerPort = Integer.parseInt(port);
                    stunServerPort = Integer.parseInt(stunport);
                    outboundServerPort = Integer.parseInt(outboundport);
                    result = mEngine.setUser(name, displayname, authname, pwd,
                            domain, host, sipServerPort, stunhost, stunServerPort, null, 5060);
                    registerTips = "setUser failed";
                    if (result == PortSipErrorcode.ECoreErrorNone) {
                        if (!TextUtils.isEmpty(FirebaseInstanceId.getInstance().getToken())) {
                            String pushMessage = "device-os=android;device-uid=" + FirebaseInstanceId.getInstance().getToken() + ";allow-call-push=true;allow-message-push=true;app-id=" + APPID;
                            mEngine.addSipMessageHeader(-1, "REGISTER", 1, "portsip-push", pushMessage);
                        }
                        mEngine.setInstanceId(getInstanceID());
                        registerTips = "registerServer failed";
                        result = mEngine.registerServer(90, 0);
                    }
                }
            }
        }

        if(result!=PortSipErrorcode.ECoreErrorNone){
            Intent broadIntent = new Intent(REGISTER_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_REGISTER_STATE,registerTips);
            broadIntent.putExtra(EXTRA_REGISTER_LINE,LINE_ID);
            sendPortSipMessage(registerTips + result, broadIntent);
            CallManager.Instance().myregist(LINE_ID,false);
            CallManager.Instance().myResetAll(LINE_ID);

            ///keepCpuRun(false);
        }
    }

    public static void ConfigPresence(Context context, SharedPreferences prefences, PortSipSdk sdk) {
        sdk.clearAudioCodec();
        if (prefences.getBoolean(context.getString(R.string.MEDIA_G722), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_G722);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_PCMA), true)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_PCMA);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_PCMU), true)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_PCMU);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_G729), true)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_G729);
        }

        if (prefences.getBoolean(context.getString(R.string.MEDIA_GSM), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_GSM);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_ILBC), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_ILBC);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_AMR), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_AMR);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_AMRWB), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_AMRWB);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_SPEEX), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_SPEEX);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_SPEEXWB), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_SPEEXWB);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_ISACWB), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_ISACWB);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_ISACSWB), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_ISACSWB);
        }
//        if (prefences.getBoolean(context.getString(R.string.MEDIA_G7221), false)) {
//            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_G7221);
//        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_OPUS), false)) {
            sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_OPUS);
        }

        sdk.clearVideoCodec();
        if (prefences.getBoolean(context.getString(R.string.MEDIA_H264), true)) {
            sdk.addVideoCodec(PortSipEnumDefine.ENUM_VIDEOCODEC_H264);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_VP8), true)) {
            sdk.addVideoCodec(PortSipEnumDefine.ENUM_VIDEOCODEC_VP8);
        }
        if (prefences.getBoolean(context.getString(R.string.MEDIA_VP9), true)) {
            sdk.addVideoCodec(PortSipEnumDefine.ENUM_VIDEOCODEC_VP9);
        }

		sdk.enableAEC(prefences.getBoolean(context.getString(R.string.MEDIA_AEC), true));
        sdk.enableAGC(prefences.getBoolean(context.getString(R.string.MEDIA_AGC), false));
        sdk.enableCNG(prefences.getBoolean(context.getString(R.string.MEDIA_CNG), true));
        sdk.enableVAD(prefences.getBoolean(context.getString(R.string.MEDIA_VAD), true));
        sdk.enableANS(prefences.getBoolean(context.getString(R.string.MEDIA_ANS), false));

        boolean foward = prefences.getBoolean(context.getString(R.string.str_fwopenkey), false);
        boolean fowardBusy = prefences.getBoolean(context.getString(R.string.str_fwbusykey), false);
        String fowardto = prefences.getString(context.getString(R.string.str_fwtokey), null);
        if (foward && !TextUtils.isEmpty(fowardto)) {
            sdk.enableCallForward(fowardBusy, fowardto);
        }

        sdk.enableReliableProvisional(prefences.getBoolean(context.getString(R.string.str_pracktitle), false));

        String resolution = prefences.getString((context.getString(R.string.str_resolution)), "CIF");
        int width = 352;
        int height = 288;
        if (resolution.equals("QCIF")) {
            width = 176;
            height = 144;
        } else if (resolution.equals("CIF")) {
            width = 352;
            height = 288;
        } else if (resolution.equals("VGA")) {
            width = 640;
            height = 480;
        } else if (resolution.equals("720P")) {
            width = 1280;
            height = 720;
        } else if (resolution.equals("1080P")) {
            width = 1920;
            height = 1080;
        }

        sdk.setVideoResolution(width, height);
    }

    private int getTransType(int select) {
        switch (select) {
            case 0:
                return PortSipEnumDefine.ENUM_TRANSPORT_UDP;
            case 1:
                return PortSipEnumDefine.ENUM_TRANSPORT_TLS;
            case 2:
                return PortSipEnumDefine.ENUM_TRANSPORT_TCP;
            case 3:
                return PortSipEnumDefine.ENUM_TRANSPORT_PERS_UDP;
            case 4:
                return PortSipEnumDefine.ENUM_TRANSPORT_PERS_TCP;
        }
        return PortSipEnumDefine.ENUM_TRANSPORT_UDP;
    }

    String getInstanceID() {
        SharedPreferences prefences = PreferenceManager.getDefaultSharedPreferences(this);

        String insanceid = prefences.getString(INSTANCE_ID, "");
        if (TextUtils.isEmpty(insanceid)) {
            insanceid = UUID.randomUUID().toString();
            prefences.edit().putString(INSTANCE_ID, insanceid).commit();
        }
        return insanceid;
    }

    public void UnregisterToServerWithoutPush() {
        if (!TextUtils.isEmpty(FirebaseInstanceId.getInstance().getToken())) {
            String pushMessage = "device-os=android;device-uid=" + FirebaseInstanceId.getInstance().getToken() + ";allow-call-push=false;allow-message-push=false;app-id=" + APPID;
            mEngine.addSipMessageHeader(-1, "REGISTER", 1, "portsip-push", pushMessage);
        }

        mEngine.unRegisterServer();
        mEngine.DeleteCallManager();
        CallManager.Instance().myregist(LINE_ID,false);
    }

    public void unregisterToServer() {

        mEngine.unRegisterServer();
        mEngine.DeleteCallManager();
        CallManager.Instance().myregist(LINE_ID,false);
    }

    //--------------------
    @Override
    public void onRegisterSuccess(String statusText, int statusCode, String sipMessage) {
        CallManager.Instance().myregist(LINE_ID,true);
        Intent broadIntent = new Intent(REGISTER_CHANGE_ACTION);
        broadIntent.putExtra(EXTRA_REGISTER_STATE,statusText);
        broadIntent.putExtra(EXTRA_REGISTER_LINE,LINE_ID);
        sendPortSipMessage("onRegisterSuccess", broadIntent);
        ///keepCpuRun(true);
    }

    @Override
    public void onRegisterFailure(String statusText, int statusCode, String sipMessage) {
        Intent broadIntent = new Intent(REGISTER_CHANGE_ACTION);
        broadIntent.putExtra(EXTRA_REGISTER_STATE, statusText);
        broadIntent.putExtra(EXTRA_REGISTER_LINE,LINE_ID);
        sendPortSipMessage("onRegisterFailure" + statusCode, broadIntent);
        CallManager.Instance().myregist(LINE_ID,false);
        CallManager.Instance().myResetAll(LINE_ID);

        ///keepCpuRun(false);
    }

    @Override
    public void onInviteIncoming(long sessionId,
                                 String callerDisplayName,
                                 String caller,
                                 String calleeDisplayName,
                                 String callee,
                                 String audioCodecNames,
                                 String videoCodecNames,
                                 boolean existsAudio,
                                 boolean existsVideo,
                                 String sipMessage) {

        application.onInviteIncoming(sessionId,callerDisplayName,caller,
        calleeDisplayName,callee,audioCodecNames,videoCodecNames,existsAudio,existsVideo,sipMessage,LINE_ID);
                        
    }

    @Override
    public void onInviteTrying(long sessionId) {
    }

    @Override
    public void onInviteSessionProgress(
            long sessionId,
            String audioCodecNames,
            String videoCodecNames,
            boolean existsEarlyMedia,
            boolean existsAudio,
            boolean existsVideo,
            String sipMessage) {
    }

    @Override
    public void onInviteRinging(long sessionId, String statusText, int statusCode, String sipMessage) {
    }

    @Override
    public void onInviteAnswered(long sessionId,
                                 String callerDisplayName,
                                 String caller,
                                 String calleeDisplayName,
                                 String callee,
                                 String audioCodecNames,
                                 String videoCodecNames,
                                 boolean existsAudio,
                                 boolean existsVideo,
                                 String sipMessage) {
        Session session = CallManager.Instance().findSessionBySessionID(sessionId);


        if (session != null) {
            application.setScale(session);
            session.state = Session.CALL_STATE_FLAG.CONNECTED;
            session.HasVideo = existsVideo;

            session.Remote = callee;
            Intent broadIntent = new Intent(CALL_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_CALL_SEESIONID, sessionId);

            String description = session.LineName + " onInviteAnswered";
            broadIntent.putExtra(EXTRA_CALL_DESCRIPTION, description);

            sendPortSipMessage(description, broadIntent);
        }

        Ring.getInstance(this).stopRingBackTone();
    }

    @Override
    public void onInviteFailure(long sessionId, String reason, int code, String sipMessage) {
        Session session = CallManager.Instance().findSessionBySessionID(sessionId);
        if (session != null) {
            session.state = Session.CALL_STATE_FLAG.FAILED;
            session.SessionID = sessionId;

            Intent broadIntent = new Intent(CALL_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_CALL_SEESIONID, sessionId);
            String description = session.LineName + " onInviteFailure";
            broadIntent.putExtra(EXTRA_CALL_DESCRIPTION, description);

            sendPortSipMessage(description, broadIntent);
        }

        Ring.getInstance(this).stopRingBackTone();
    }

    @Override
    public void onInviteUpdated(
            long sessionId,
            String audioCodecNames,
            String videoCodecNames,
            boolean existsAudio,
            boolean existsVideo,
            String sipMessage) {
        Session session = CallManager.Instance().findSessionBySessionID(sessionId);

        if (session != null) {
            application.setScale(session);
            session.state = Session.CALL_STATE_FLAG.CONNECTED;
            session.HasVideo = existsVideo;

            Intent broadIntent = new Intent(CALL_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_CALL_SEESIONID, sessionId);
            String description = session.LineName + " OnInviteUpdated";
            broadIntent.putExtra(EXTRA_CALL_DESCRIPTION, description);

            sendPortSipMessage(description, broadIntent);
        }
    }

    @Override
    public void onInviteConnected(long sessionId) {
        Session session = CallManager.Instance().findSessionBySessionID(sessionId);
        if (session != null) {

            session.SessionID = sessionId;

            if (application.mConference)
            {
                application.mEngine.joinToConference(session.SessionID);
                application.mEngine.sendVideo(session.SessionID, true);
            }
            if(session.state != Session.CALL_STATE_FLAG.CONNECTED)
            {
            Intent broadIntent = new Intent(CALL_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_CALL_SEESIONID, sessionId);
            String description = session.LineName + " OnInviteConnected";
            broadIntent.putExtra(EXTRA_CALL_DESCRIPTION, description);

            sendPortSipMessage(description, broadIntent);
           }
           session.state = Session.CALL_STATE_FLAG.CONNECTED;
        }

        CallManager.Instance().setSpeakerOn(application.mEngine,CallManager.Instance().isSpeakerOn());
    }

    @Override
    public void onInviteBeginingForward(String forwardTo) {
    }

    @Override
    public void onInviteClosed(long sessionId) {
        Session session = CallManager.Instance().findSessionBySessionID(sessionId);
        if (session != null) {
            session.state = Session.CALL_STATE_FLAG.CLOSED;
            session.SessionID = sessionId;

            Intent broadIntent = new Intent(CALL_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_CALL_SEESIONID, sessionId);
            String description = session.LineName + " OnInviteClosed";
            broadIntent.putExtra(EXTRA_CALL_DESCRIPTION, description);

            sendPortSipMessage(description, broadIntent);
        }
        Ring.getInstance(this).stopRingTone();
    }

    @Override
    public void onDialogStateUpdated(String BLFMonitoredUri,
                                     String BLFDialogState,
                                     String BLFDialogId,
                                     String BLFDialogDirection) {
        String text = "The user ";
        text += BLFMonitoredUri;
        text += " dialog state is updated: ";
        text += BLFDialogState;
        text += ", dialog id: ";
        text += BLFDialogId;
        text += ", direction: ";
        text += BLFDialogDirection;
    }

    @Override
    public void onRemoteUnHold(
            long sessionId,
            String audioCodecNames,
            String videoCodecNames,
            boolean existsAudio,
            boolean existsVideo) {

       Ring.getInstance(this).stopHoldTone();
    }

    @Override
    public void onRemoteHold(long sessionId) {
      Ring.getInstance(this).startHoldTone();
    }

    public void MyAcceptRefer(Session tempSession,final Session referSession,final long referId,final String referSipMessage)
  	{
  		Log.i(TAG,"MyAcceptRefer");
  		mEngine.hold(tempSession.getSessionId());// hold current session
  		tempSession.setHoldState(true);

      Ring.getInstance(this).stopRingTone();
      Ring.getInstance(this).stopRingBackTone();
  		tempSession
  				.setDescriptionString("Place currently call on hold on line: ");

  		long referSessionId = mEngine.acceptRefer(referId,
  				referSipMessage);
  		if (referSessionId <= 0) {
  			referSession
  					.setDescriptionString("Failed to accept REFER on line");

  			referSession.Reset();

  			// Take off hold
  			mEngine.unHold(tempSession.getSessionId());
  			tempSession.setHoldState(false);
  		} else {
  			//Toast.makeText(getApplicationContext(), "acceptRefer", Toast.LENGTH_LONG).show();

  			referSession.setSessionId(referSessionId);
  			//referSession.setReferCall(true,
  			//		tempSession.getSessionId());

  			referSession.voip_line = LINE_ID;
  			referSession
  					.setDescriptionString("Accepted the refer, new call is trying on line ");

        CallManager.Instance().setCurrentLine(referSession);

  			tempSession
  					.setDescriptionString("Now current line is set to: "
  							+ referSession.getLineName());

  			mEngine.hangUp(tempSession.getSessionId());
  			tempSession.Reset();
  			//updateSessionVideo();
  		}

  	}

    @Override
    public void onReceivedRefer(
            long sessionId,
            long referId,
            String to,
            String referFrom,
            String referSipMessage) {

        final Session tempSession = CallManager.Instance().findSessionBySessionID(sessionId);

          		Session currentLine = CallManager.Instance().getCurrentSession();

        if (tempSession == null) {
          		mEngine.rejectRefer(referId);
          		return;
        }

        final Session referSession =  CallManager.Instance().findIdleSession();

        if (referSession == null)// all sessions busy
        {
          	mEngine.rejectRefer(referId);
            return;
        } else {
          	referSession.state = Session.CALL_STATE_FLAG.CONNECTED;

            if(true)
            {
          	    MyAcceptRefer(currentLine,referSession,referId,referSipMessage);
          	    return;
            }
       }

    }

    @Override
    public void onReferAccepted(long sessionId) {
        Session session = CallManager.Instance().findSessionBySessionID(sessionId);
        if (session != null) {
            session.state = Session.CALL_STATE_FLAG.CLOSED;
            session.SessionID = sessionId;

            Intent broadIntent = new Intent(CALL_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_CALL_SEESIONID, sessionId);
            String description = session.LineName + " onReferAccepted";
            broadIntent.putExtra(EXTRA_CALL_DESCRIPTION, description);

            sendPortSipMessage(description, broadIntent);
        }
        Ring.getInstance(this).stopRingTone();
    }

    @Override
    public void onReferRejected(long sessionId, String reason, int code) {
    }

    @Override
    public void onTransferTrying(long sessionId) {
    }

    @Override
    public void onTransferRinging(long sessionId) {
    }

    @Override
    public void onACTVTransferSuccess(long sessionId) {
        Session session = CallManager.Instance().findSessionBySessionID(sessionId);
        if (session != null) {
            session.state = Session.CALL_STATE_FLAG.CLOSED;
            session.SessionID = sessionId;

            Intent broadIntent = new Intent(CALL_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_CALL_SEESIONID, sessionId);
            String description = session.LineName + " Transfer succeeded, call closed";
            broadIntent.putExtra(EXTRA_CALL_DESCRIPTION, description);

            sendPortSipMessage(description, broadIntent);
            // Close the call after succeeded transfer the call
            mEngine.hangUp(sessionId);
            application.isTransfer = true;
        }
    }

    @Override
    public void onACTVTransferFailure(long sessionId, String reason, int code) {
        Session session = CallManager.Instance().findSessionBySessionID(sessionId);
        if (session != null) {
            Intent broadIntent = new Intent(CALL_CHANGE_ACTION);
            broadIntent.putExtra(EXTRA_CALL_SEESIONID, sessionId);
            String description = session.LineName + " Transfer failure!";
            broadIntent.putExtra(EXTRA_CALL_DESCRIPTION, description);

            sendPortSipMessage(description, broadIntent);

        }
    }

    @Override
    public void onReceivedSignaling(long sessionId, String signaling) {
    }

    @Override
    public void onSendingSignaling(long sessionId, String signaling) {
    }

    @Override
    public void onWaitingVoiceMessage(
            String messageAccount,
            int urgentNewMessageCount,
            int urgentOldMessageCount,
            int newMessageCount,
            int oldMessageCount) {
    }

    @Override
    public void onWaitingFaxMessage(
            String messageAccount,
            int urgentNewMessageCount,
            int urgentOldMessageCount,
            int newMessageCount,
            int oldMessageCount) {
    }

    @Override
    public void onRecvDtmfTone(long sessionId, int tone) {

      application.onRecvDtmfTone(LINE_ID,sessionId,tone);
    }

    @Override
    public void onRecvOptions(String optionsMessage) {
    }

    @Override
    public void onRecvInfo(String infoMessage) {
    }

    @Override
    public void onRecvNotifyOfSubscription(long sessionId, String notifyMessage, byte[] messageData, int messageDataLength) {
    }

    //Receive a new subscribe
    @Override
    public void onPresenceRecvSubscribe(
            long subscribeId,
            String fromDisplayName,
            String from,
            String subject) {
        Contact contact = ContactManager.Instance().FindContactBySipAddr(from);
        if (contact == null) {
            contact = new Contact();
            contact.SipAddr = from;
            ContactManager.Instance().AddContact(contact);
        }

        contact.SubRequestDescription = subject;
        contact.SubId = subscribeId;
        switch (contact.state) {
            case ACCEPTED://This subscribe has accepted
                application.mEngine.presenceAcceptSubscribe(subscribeId);
                break;
            case REJECTED://This subscribe has rejected
                application.mEngine.presenceRejectSubscribe(subscribeId);
                break;
            case UNSETTLLED:
                break;
            case UNSUBSCRIBE:
                contact.state = Contact.SUBSCRIBE_STATE_FLAG.UNSETTLLED;
                break;
        }
        Intent broadIntent = new Intent(PRESENCE_CHANGE_ACTION);
        sendPortSipMessage("OnPresenceRecvSubscribe", broadIntent);
    }

    //update online status
    @Override
    public void onPresenceOnline(String fromDisplayName, String from, String stateText) {
        Contact contact = ContactManager.Instance().FindContactBySipAddr(from);
        if (contact == null) {

        } else {
            contact.SubDescription = stateText;
        }

        Intent broadIntent = new Intent(PRESENCE_CHANGE_ACTION);
        sendPortSipMessage("OnPresenceRecvSubscribe", broadIntent);
    }

    //update offline status
    @Override
    public void onPresenceOffline(String fromDisplayName, String from) {
        Contact contact = ContactManager.Instance().FindContactBySipAddr(from);
        if (contact == null) {

        } else {
            contact.SubDescription = "Offline";
        }

        Intent broadIntent = new Intent(PRESENCE_CHANGE_ACTION);
        sendPortSipMessage("OnPresenceRecvSubscribe", broadIntent);
    }

    @Override
    public void onRecvMessage(
            long sessionId,
            String mimeType,
            String subMimeType,
            byte[] messageData,
            int messageDataLength) {

        if ("text".equals(mimeType) && "plain".equals(subMimeType)) {
                  //Toast.makeText(this,"you have a mesaage from: "+from+ "  "+new String(messageData),Toast.LENGTH_SHORT).show();
            application.recvMessage(LINE_ID,"","",new String(messageData));
        }else{
        }
    }

    @Override
    public void onRecvOutOfDialogMessage(
            String fromDisplayName,
            String from,
            String toDisplayName,
            String to,
            String mimeType,
            String subMimeType,
            byte[] messageData,
            int messageDataLengthsipMessage,
            String sipMessage) {
        if ("text".equals(mimeType) && "plain".equals(subMimeType)) {
            //Toast.makeText(this,"you have a mesaage from: "+from+ "  "+new String(messageData),Toast.LENGTH_SHORT).show();
            application.recvMessage(LINE_ID,from,to,new String(messageData));
        }else{
        }
    }

    @Override
    public void onSendMessageSuccess(long sessionId, long messageId) {
      application.showTips("onSendMessageSuccess "+String.valueOf(sessionId));
    }

    @Override
    public void onSendMessageFailure(long sessionId, long messageId, String reason, int code) {
      application.showTips("onSendMessageFailure");
    }

    @Override
    public void onSendOutOfDialogMessageSuccess(long messageId,
                                                String fromDisplayName,
                                                String from,
                                                String toDisplayName,
                                                String to) {

          application.onSendOutOfDialogMessageSuccess(LINE_ID,to);
    }

    @Override
    public void onSendOutOfDialogMessageFailure(
            long messageId,
            String fromDisplayName,
            String from,
            String toDisplayName,
            String to,
            String reason,
            int code) {

            //application.showTips("onSendOutOfDialogMessageFailure "+from+" "+to);
            application.onSendOutOfDialogMessageFailure(LINE_ID,to);
    }

    @Override
    public void onSubscriptionFailure(long subscribeId, int statusCode) {
    }

    @Override
    public void onSubscriptionTerminated(long subscribeId) {
    }

    @Override
    public void onPlayAudioFileFinished(long sessionId, String fileName) {
      application.onPlayAudioFileFinished(LINE_ID,sessionId,fileName);
    }

    @Override
    public void onPlayVideoFileFinished(long sessionId) {
    }

    @Override
    public void onReceivedRTPPacket(
            long sessionId,
            boolean isAudio,
            byte[] RTPPacket,
            int packetSize) {
    }

    @Override
    public void onSendingRTPPacket(long l, boolean b, byte[] bytes, int i) {

    }

    @Override
    public void onAudioRawCallback(
            long sessionId,
            int callbackType,
            byte[] data,
            int dataLength,
            int samplingFreqHz) {
    }

    @Override
    public void onVideoRawCallback(long l, int i, int i1, int i2, byte[] bytes, int i3) {

    }


    //--------------------
    public void sendPortSipMessage(String message, Intent broadIntent) {
        /*
        NotificationManager mNotifyMgr = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        Intent intent = new Intent(this, BaseMainActivity.class);
        PendingIntent contentIntent = PendingIntent.getActivity(this, 0, intent, 0);

        Notification.Builder builder;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            builder = new Notification.Builder(this,ChannelID);
        }else{
            builder = new Notification.Builder(this);
        }
        builder.setSmallIcon(R.drawable.icon)
                .setContentTitle("Sip Notify")
                .setContentText(message)
                .setContentIntent(contentIntent)
                .build();// getNotification()

        mNotifyMgr.notify(1, builder.build());
*/
        sendBroadcast(broadIntent);
    }

    public int outOfDialogRefer(int replaceSessionId, String replaceMethod, String target, String referTo) {
        return 0;
    }

    public void keepCpuRun(boolean keepRun) {
        PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
        if (keepRun == true) { //open
            if (mCpuLock == null) {
                if ((mCpuLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "SipSample:CpuLock.")) == null) {
                    return;
                }
                mCpuLock.setReferenceCounted(false);
            }

            synchronized (mCpuLock) {
                if (!mCpuLock.isHeld()) {
                    mCpuLock.acquire();
                }
            }
        } else {//close
            if (mCpuLock != null) {
                synchronized (mCpuLock) {
                    if (mCpuLock.isHeld()) {
                        mCpuLock.release();
                    }
                }
            }
        }
    }

    public static String getSipName(Context context)
    {
      SharedPreferences prefences = PreferenceManager.getDefaultSharedPreferences(context);

      String name = prefences.getString(USER_NAME, "");

      return name;
    }
}
