/*
 * Copyright 2009 <PERSON><PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package com.weema.sipsample.service;


import android.content.Intent;

import android.app.Service;
import android.util.Log;
import android.os.IBinder;
import 	android.content.ComponentName;
import android.content.ServiceConnection;
import android.os.Binder;
import android.content.Context;
import 	android.widget.Toast;


import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.content.BroadcastReceiver;
import android.content.IntentFilter;

public class RemoteCastielService extends Service {
    private static final String TAG="RemoteCastielService";
    public  static final String ALIVE_BROADCAST = "com.weema.softphone1.alive.broadcast";
    public  static final String EXTRA_MYPID = "ALIVE_MYPID";
    
    private static final String SOFTPHONE1_RESTART_APP="SOFTPHONE1_RESTART_APP";
    private static final int SHOW_CNT = 1;
    private static final int SHOW_NOTIFY = 2;
    private static final int RESTART_WASERVICE = 3;    
    private int m_alive_cnt;
    //private int m_check_alive_time = 1000*60*10;

    private int m_check_alive_time = 1000*15;

    private LoaclServiceConnection mLoaclServiceConnection;
    private MyBinder myBinder;
    private int mypid=-1;
    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "onCreate");
    
        IntentFilter filter = new IntentFilter();
        filter.addAction(ALIVE_BROADCAST);
       
        registerReceiver(receiver, filter);

        mHandler.postDelayed(restartApp, m_check_alive_time);

        if (myBinder == null) {
            myBinder = new MyBinder();
        }
        mLoaclServiceConnection = new LoaclServiceConnection();
        //Toast.makeText(this, " RemoteCastielService onCreate", Toast.LENGTH_LONG).show();
        //this.startActivity(new Intent(RemoteCastielService.this, MainActivity.class));
 
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "onStartCommand");
        this.bindService(new Intent(this,WaService.class), mLoaclServiceConnection, Context.BIND_IMPORTANT);
        //Notification notification = new Notification(R.drawable.ic_launcher, "猴子服务启动中", System.currentTimeMillis());
        //pintent = PendingIntent.getService(this, 0, intent, 0);
        //notification.setLatestEventInfo(this, "猴子服务", "防止被杀掉！", pintent);

        // 设置service为前台进程，避免手机休眠时系统自动杀掉该服务
        //startForeground(startId, notification);
        return START_STICKY;
    }

    private final BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
           
           String action = intent == null ? "" : intent.getAction();
           if(action.equals(ALIVE_BROADCAST)){
             //action for sms received

             int pid = intent.getIntExtra(EXTRA_MYPID,-1);
             mypid = pid;

             m_alive_cnt++;
             postMessage(SHOW_CNT);

           }
         
        }
     };

     private void show_notify()
     {
        //Toast.makeText(this, "notify ", Toast.LENGTH_LONG).show();
     }

     private void show_cnt()
     {
        //Toast.makeText(this, "show "+String.valueOf(m_alive_cnt), Toast.LENGTH_LONG).show();
     }
     public final StaticHandler mHandler = new StaticHandler(this);
     private  static class StaticHandler extends Handler{
       private final WeakReference<RemoteCastielService> mActivity;
       public StaticHandler(RemoteCastielService activity)
       {
         mActivity = new WeakReference<RemoteCastielService>(activity);
       }
       @Override
       public void handleMessage(Message msg)
       {
        RemoteCastielService activity = mActivity.get();
         if(activity == null) return;
   
         switch(msg.what)
         {
           case SHOW_CNT:
           activity.show_cnt();
   
           break;
        
            case SHOW_NOTIFY:
            activity.show_notify();
            break;    
            case RESTART_WASERVICE:
            activity.startService(new Intent(activity, WaService.class));
            break;            
         }
   
       }
     }
   private void postMessage(int id)
   {
     Message message=new Message();
     message.what = id;
     mHandler.sendMessage(message);
   }


    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }
    public String getServiceName(){
        return RemoteCastielService.class.getSimpleName();
    }

    public IBinder onBind(Intent intent) {
    
        return myBinder;
    }

    public class MyBinder extends Binder {
        public RemoteCastielService getService(){
            return RemoteCastielService.this;
        }
    }

	protected void raise_notification() {
        
        postMessage(SHOW_NOTIFY);
        //if(false)
        if(m_alive_cnt > 0)    
        {
            m_alive_cnt = 0;
            
            return;
        }

        if(mypid > 0)
            android.os.Process.killProcess(mypid);  //如果是service直接kill掉
        
	}

    private Runnable restartApp = new Runnable() {
		@Override
		public void run() {
			raise_notification();
            mHandler.postDelayed(restartApp, m_check_alive_time);
            
		}
	};        

    private Runnable restartService = new Runnable() {
		@Override
		public void run() {
            postMessage(RESTART_WASERVICE);
			
           
		}
	};        
  
   private class LoaclServiceConnection implements ServiceConnection {

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            //透過Binder調用Service內的方法
            Log.i(TAG, "远程服务连接成功");
            
        }
        @Override
        public void onServiceDisconnected(ComponentName name) {
            //Toast.makeText(RemoteCastielService.this, "進程服務Remote被幹掉", Toast.LENGTH_SHORT).show();
            mHandler.postDelayed(restartService, 2000);
        }
    }

}