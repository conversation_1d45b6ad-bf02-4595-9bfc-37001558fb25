package com.weema.sipsample.service;

import com.weema.sipsample.model.SettingModel;
import com.weema.sipsample.util.Utility;

public class ServiceWork{
    SettingModel settingModel;
    public ServiceWork(SettingModel settingModel){
        this.settingModel = settingModel;
    }
    public boolean can_answer(String phone,String to){

        int number_to = Utility.getPhoneNumber(to);

        int number = Utility.getPhoneNumber(phone);

        boolean ret = true;
      
        if((settingModel.m_can_force_mgr == false) && 
              number >= 120 && 
              number < 200){
            ret = false;
        }

        if(number_to < 2000) ret = true;
        return ret;
    }
}