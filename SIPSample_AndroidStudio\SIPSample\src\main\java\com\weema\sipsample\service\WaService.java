/*
 * Copyright 2009 <PERSON><PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

package com.weema.sipsample.service;

import java.security.InvalidParameterException;

import android.content.Intent;

import android.app.Service;
import android.util.Log;
import java.io.IOException;
import java.util.Arrays;
import android.os.IBinder;
import android.os.Binder;

import com.weema.sipsample.log.ProcessLogFile;
import com.weema.sipsample.service.i2cDriver;
import java.io.File;
import android.util.Log;
import com.weema.sipsample.ui.MyApplication;
import com.weema.sipsample.ui.MainActivity;

import java.net.Socket;
import java.net.InetAddress;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import 	java.net.ServerSocket;
import 	java.net.SocketTimeoutException;
import 	java.util.ArrayList;
import com.weema.sipsample.util.SettingConfig;
import  java.net.DatagramPacket ;
import  java.net.DatagramSocket ;
import  java.util.concurrent.atomic.AtomicBoolean ;
import java.util.List;
import  	java.net.NetworkInterface ;
import  	java.util.Collections ;

import 	java.net.InetSocketAddress;
import android.text.TextUtils;
import java.net.SocketAddress;

import 	java.io.InterruptedIOException;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;
import com.weema.sipsample.util.CrashLogCatch;
public class WaService extends Service {

  private final String TAG = "WaService";
  private static final int SAVE_REG = 1;
  private static final int SAVE_SOS = 2;
  private static final int SAVE_ZONE = 3;
  private static final int SAVE_DOOR = 4;
  private static final int SAVE_TRANSFER = 5; 
  private static final int SAVE_OTHER = 6; 
  private static final int SHOW_DIALOG = 7;    
  private static final int SAVE_MAIL = 8;    
  private static final int SAVE_ETHERNET = 9;    
  private static final int SHOW_DOOR = 10;    

  private int m_alive_time = 1000*5;

  private Thread thread1=null;
  private Thread UDPThread1=null;
  private Thread sendMsgThread;
  private MyApplication myApp;
  public byte[] m_i2c_ret;
  public static final int UNKNOW_STATE = 2;
  public static final int OPEN_STATE = 1;
  public static final int CLOSE_STATE = 0;


  private i2cThread mi2cThread=null;
  private ShowThread mshowThread=null;

  private i2cDriver m_i2c=null;
    
  private boolean m_open_gas=false;
  private boolean m_close_gas=false;

  private boolean m_open_bz=false;
  private boolean m_close_bz=false;
  private boolean m_open_led=false;
  private boolean m_close_led=false;

  private boolean m_ctrl_bz = true;
  private boolean m_open_bz_long=false;
  private boolean m_open_bz_short=false;
  private boolean m_open_door=false;

	private int m_lock_pin=OPEN_STATE;
  private int m_door_pin=UNKNOW_STATE;
  private int m_door_ring_pin=OPEN_STATE;
  private int m_notify_pin=OPEN_STATE;

  private static final int BZ_CMD=1;
  private static final int LED_CMD=2;
  private static final int BZ_LONG_CMD = 3;
  private static final int BZ_SHORT_CMD = 4;
  private static final int OPEN_DOOR_CMD = 5;
  private static final int CTRL_GAS_CMD = 6;
  private static final int CTRL_BZ_CMD = 7;

  public static final int DOOR_RING_PIN=8;
  public static final int LOCK_PIN=9;
  public static final int LED_PIN=10;
  public static final int CTRL_GAS_PIN=11;
  public static final int CTRL_BZ_PIN=12;
  public static final int DOOR_PIN=13;
  public static final int OPEN_DOOR_PIN=14;
  public static final int BZ_PIN=15;

  public static final int VOIP_LINE=1;

  public String m_send_msg;
  public String m_remote_ip;

  private static final int UDP_SERVER_PORT=20000;

  private AtomicBoolean isRunning=new AtomicBoolean(false);

  private int m_serial_number=1;

  private String[] m_msg_arr;
  private int[] m_keyboard_state;
  private int mypid;

  private Runnable alive_app = new Runnable() {
  @Override
  public void run() {
    alive_broadcast();
    mHandler.postDelayed(alive_app, m_alive_time);

  }
};        
  public void i2c_reset(boolean is_sos_mode)
  {
    if(m_i2c != null)
      m_i2c.do_init(is_sos_mode);
  }
  public void ctrlGasAction(boolean val)
  {
		if(val)
		{
			m_open_gas = true;
		}
		else 
		{
			m_close_gas = true;
		}
  }
  private void lockPinAction()
  {
    myApp.show_lock_pingAction();
    
    if(myApp.m_is_sos_mode == false)    return;

    if(myApp.m_ring_enable)
    {
      m_remote_ip = myApp.m_ring_phone_ip;
      send_msg("TOGGLE");
    }
    else 
    {
      m_remote_ip = myApp.m_door_ip;

      myApp.lockPinAction();
    }
  }
  private boolean sendDoorAction(int state)
  {
    return myApp.mdoorAction(state);

  }

  private class i2cThread extends Thread {
    public static final int MAX_INIT_CNT = 60;
    public static final int MAX_GPIO_PORT = 16;
    private int m_test_timer=0;
    private int m_init_cnt=0;

    private String byteArrayToArrayString(byte[] array) {

           
      String arrayString = Arrays.toString(array);
      //System.out.println(arrayString);
      return arrayString;
    }
    @Override
    public void run() {
      // int lenMax = 0;
      super.run();
      try{

        m_i2c = new i2cDriver("/dev/i2c-1");
        int total = m_i2c.do_init(myApp.m_is_sos_mode);

      }

      catch(IOException ex)
      {
        Log.e(TAG, "IOException");
      }
      catch(SecurityException ex)
      {
        Log.e(TAG, "SecurityException");

      }         

      while(!isInterrupted())
      {
        if(myApp.m_is_sos_mode)
          do_sos_mode_run();
        else 
          do_keyboard_mode_run();
      }

    }  
        
    private void check_lock_pin(int index)
    {
      if(m_i2c_ret[index] == CLOSE_STATE)
      {
        if(m_lock_pin == OPEN_STATE)
        {
          m_lock_pin = CLOSE_STATE;
          lockPinAction();
        }
      }
      else
      {
        m_lock_pin = OPEN_STATE;
      }
    }

    private void door_ringAction()
    {
      myApp.door_ringAction();
      postMessage(SHOW_DOOR);
    }
    private void check_door_ring_pin(int index)
    {
      if(m_i2c_ret[index] == CLOSE_STATE)
      {
        if(m_door_ring_pin != CLOSE_STATE)
        {
          m_door_ring_pin = CLOSE_STATE;

          door_ringAction();
        }
      }
      else
      {
        if(m_door_ring_pin != OPEN_STATE)
        {
          m_door_ring_pin = OPEN_STATE;

        }
      }

    }

    private void check_door_pin(int index)
    {
      if(m_i2c_ret[index] == CLOSE_STATE)
      {
        if(m_door_pin != CLOSE_STATE)
        {
          if(sendDoorAction(CLOSE_STATE))
            m_door_pin = CLOSE_STATE;

        }
      }
      else
      {
        if(m_door_pin != OPEN_STATE)
        {
          if(sendDoorAction(OPEN_STATE))
            m_door_pin = OPEN_STATE;

        }
      }

    }

    private void dataReceiver(int index)
    {
      if(myApp.m_is_sos_mode == false)    return;

      myApp.dataReceiver(index);

    }
    private void check_sos_pin(int index)
    {
      if(m_i2c_ret[index] == CLOSE_STATE)
      {
        if(index>=5 && index<8)
        {
          dataReceiver(index);
        }

      }
      else
      {
        if(index<5)
        {
          dataReceiver(index);
        }
      }

    }

    private void check_notify_pin(int index)
    {
      if(m_i2c_ret[index] == CLOSE_STATE)
      {
        if(m_notify_pin != CLOSE_STATE)
        {   
          m_notify_pin = CLOSE_STATE;

        }
      }
      else
      {
        if(m_notify_pin != OPEN_STATE)
        {
                
          m_notify_pin = OPEN_STATE;
          door_notify();

        }
      }

    }
    private void door_notify()
    {
      if(myApp.m_is_sos_mode == false)    return;

      myApp.door_notify();
    }
    private void i2c_setting()
    {
      byte[] arr;
      arr = new byte[5];

      if(m_ctrl_bz)
      {
        m_ctrl_bz = false;

        arr[0] = 0;
        arr[1] = CTRL_BZ_CMD;
        arr[2] = CTRL_BZ_PIN;
        arr[3] = (byte)myApp.m_ctrl_bz;

        m_i2c.write(arr);
      }

      if(m_open_bz_long)
      {
        m_open_bz_long = false;

        arr[0] = 0;
        arr[1] = BZ_LONG_CMD;
        arr[2] = BZ_PIN;
        arr[3] = 0;
        m_i2c.write(arr);
      }

      if(m_open_bz_short)
      {
        m_open_bz_short = false;

        arr[0] = 0;
        arr[1] = BZ_SHORT_CMD;
        arr[2] = BZ_PIN;
        arr[3] = 0;
        m_i2c.write(arr);
      }

      if(m_open_door)
      {
        m_open_door = false;

        arr[0] = 0;
        arr[1] = OPEN_DOOR_CMD;
        arr[2] = OPEN_DOOR_PIN;
        arr[3] = 0;
        m_i2c.write(arr);
      }
      if(m_open_gas)
      {
        m_open_gas = false;

        arr[0] = 0;
        arr[1] = CTRL_GAS_CMD;
        arr[2] = CTRL_GAS_PIN;
        arr[3] = 0;
        m_i2c.write(arr);
      }    

      if(m_close_gas)
      {
        m_close_gas = false;

        arr[0] = 0;
        arr[1] = CTRL_GAS_CMD;
        arr[2] = CTRL_GAS_PIN;
        arr[3] = 1;
        m_i2c.write(arr);
      }      
      if(m_open_bz)
      {
        m_open_bz = false;

        arr[0] = 0;
        arr[1] = BZ_CMD;
        arr[2] = BZ_PIN;
        arr[3] = 1;

        m_i2c.write(arr);
      }
      if(m_close_bz)
      {
        m_close_bz = false;

        arr[0] = 0;
        arr[1] = BZ_CMD;
        arr[2] = BZ_PIN;
        arr[3] = 0;

         m_i2c.write(arr);
      }

      if(m_open_led)
      {
        m_open_led = false;

        arr[0] = 0;
        arr[1] = LED_CMD;
        arr[2] = LED_PIN;
        arr[3] = 1;
        m_i2c.write(arr);
      }
      if(m_close_led)
      {
        m_close_led = false;

        arr[0] = 0;
        arr[1] = LED_CMD;
        arr[2] = LED_PIN;
        arr[3] = 0;
        m_i2c.write(arr);
      }

      arr[0] = 0;

      m_i2c_ret = m_i2c.read(arr);

    }
    private void presskeyReceiver(int key)
    {
      if(myApp.m_is_sos_mode)    return;

      myApp.presskeyReceiver(key);
    }
    private void do_row()
    {
      for(int i=MAX_GPIO_PORT;i<MAX_GPIO_PORT*2;i++)
      {
        int state_i = i-MAX_GPIO_PORT;
        if(m_i2c_ret[i] > 0)
        {
          if(m_keyboard_state[state_i] != CLOSE_STATE)
          {
            m_keyboard_state[state_i] = CLOSE_STATE;

          }
          presskeyReceiver(state_i);

        }
        else 
        {
          if(m_keyboard_state[state_i] != OPEN_STATE)
          {
            m_keyboard_state[state_i] = OPEN_STATE;
           
          }
        }
      } 
    }
   
  
    private void do_keyboard_mode_run() {
      byte[] arr;
      arr = new byte[5];
      
      m_keyboard_state = new int[MAX_GPIO_PORT];
      for(int i=0;i<MAX_GPIO_PORT;i++)
      {
        m_keyboard_state[i] = UNKNOW_STATE;
      }

      while (!isInterrupted()) {

        try {
          Thread.sleep(100);
        } catch (InterruptedException e) {
                  //Log.d("error");
        }
        
        i2c_setting();

        do_row();
        
        for(int i=0;i<MAX_GPIO_PORT;i++)
        {
          check_input_pin(i);
         
        }

      } 
    } 
    private void check_input_pin(int i)
    {
      if(i==LOCK_PIN)
      {
        check_lock_pin(i);
      }
      else if(i==DOOR_RING_PIN)
      {
        check_door_ring_pin(i);
      }
      else if(i==DOOR_PIN)
      {
        check_door_pin(i);
      }
      else if(myApp.m_is_sos_mode && myApp.get_sos_state(i) == MyApplication.SOS_IDLE_STATE)
      {
        check_sos_pin(i);
      }
  
    }

    private void do_sos_mode_run() {
      
      while (!isInterrupted()) {

        try {
          Thread.sleep(500);
        } catch (InterruptedException e) {
                  //Log.d("error");
        }

      i2c_setting();
    
      //for(int i=0;i<m_i2c_ret.length;i++)
      for(int i=0;i<MAX_GPIO_PORT;i++)
      {
        if(i<5 && (myApp.m_is_lock == false || myApp.m_zone[i] == false))
        {
          myApp.set_sos_state(i,MyApplication.SOS_IDLE_STATE);
                    
          if(i==0 && myApp.m_sd_timer == 0)
          {
            check_notify_pin(i);
          }

          continue;
        }

        check_input_pin(i);

      }

    }
  }
  }

 
  private class ShowThread extends Thread {
    private int m_show_timer=1;
    private static final int MAX_SHOW_TIME = 5*2;
  
    private void do_run()
    {
      try {
        Thread.sleep(1000*30);
      } catch (InterruptedException e) {
        // TODO Auto-generated catch block
        e.printStackTrace();
      }

    
      if(m_show_timer > 0)
      {
        m_show_timer--;
        if(m_show_timer <= 0)
        {
          m_show_timer = MAX_SHOW_TIME;
          if(!myApp.m_is_show)
          {
            postMessage(SHOW_DIALOG);
          }
          myApp.checkConnect();
        }
      }else 
      {
        m_show_timer = MAX_SHOW_TIME;
      }
    
    }
    @Override
    public void run() {
      // int lenMax = 0;
      super.run();
   
      while(!isInterrupted())
      {
        do_run();
      }

    }  

  }    
  public static String getMacAddr() {
    try {
      List<NetworkInterface> all = Collections.list(NetworkInterface.getNetworkInterfaces());
      for (NetworkInterface nif : all) {
        if (!nif.getName().equalsIgnoreCase("wlan0")) continue;

          byte[] macBytes = nif.getHardwareAddress();
          if (macBytes == null) {
            return "00:11:22:33:44:55";
          }

          StringBuilder res1 = new StringBuilder();
          for (byte b : macBytes) {
            res1.append(Integer.toHexString(b & 0xFF) + ":");
          }

          if (res1.length() > 0) {
            res1.deleteCharAt(res1.length() - 1);
          }
        return res1.toString();
      }
    } catch (Exception ex) {
            //handle exception
    }
    return "";
  }
    private void send_info(String str)
    {
      Log.i(TAG,str);
      sendBroadcast(str);
      m_serial_number++;

    }
    private void udp_process(String msg)
    {

      String[] str_arr = msg.split("\n");

      if(str_arr.length >= 3)
      {
        String ip = myApp.getLocalIP(false);
        String str;
        if(str_arr[2].equals("WEEMA"))
        {

          str = "VERSION=1.0\nSERIAL NUMBER="+String.valueOf(m_serial_number)+"\n"+"DEVICE NAME=phone\nMACADDR="+getMacAddr()+"\nVERSION="+MyApplication.VERSION+"\nIPADDR="+myApp.m_local_ip+"\nINTERFACE=LAN\n";

          send_info(str);
        }

      }

    }

    public void sendBroadcast(String messageStr) {
        // Hack Prevent crash (sending should be done using an async task)

        try {
          if(myApp.skip_network)    return;
          //Open a random port to send the package
          DatagramSocket socket = new DatagramSocket();
          socket.setBroadcast(true);
          byte[] sendData = messageStr.getBytes();
          DatagramPacket sendPacket = new DatagramPacket(sendData, sendData.length, getBroadcastAddress(),UDP_SERVER_PORT);
          socket.send(sendPacket);
          //System.out.println(getClass().getName() + "Broadcast packet sent to: " + getBroadcastAddress().getHostAddress());
        } catch (IOException e) {
          Log.e(TAG, "IOException: " + e.getMessage());
        }
      }

      InetAddress getBroadcastAddress() throws IOException {
        byte[] quads = new byte[4];
        for (int k = 0; k < 4; k++)
          quads[k] =  (byte)0xFF;
        return InetAddress.getByAddress(quads);
      }


    class UDPThread implements Runnable {
       @Override
       public void run() {
          while(isRunning.get())
          {
            do_run();

            try {
              Thread.sleep(500);
            } catch (InterruptedException e) {
                  //Log.d("error");
            }

          }
       }

       private void do_run()
       {
        DatagramSocket ds=null;
        try {
          if(myApp.skip_network)    return;
          String data;
          byte[] recevieData = new byte[1024];
          DatagramPacket dp = new DatagramPacket(recevieData, recevieData.length);

          ds = new DatagramSocket(UDP_SERVER_PORT);

           for (;isRunning.get();) {
              Thread.sleep(1000);
              ds.receive(dp);
              if(dp.getLength() <= 0)    continue;
          
              //if(true)
              //throw new NullPointerException();
              data = new String(recevieData, 0, dp.getLength());
              Log.i(TAG,data);
              udp_process(data);
              //handler.sendMessage(handler.obtainMessage(1,data));
          }

        }
       
        catch (Throwable t) {
            // just end the background thread
            if(ds != null)
              ds.close();
            CrashLogCatch.logit(t);
        }
       }
    }

  private void responseClient(Socket client) throws IOException{
    // 獲取輸入流 並 寫入讀Buffer 中
    BufferedReader input = new BufferedReader(new InputStreamReader(client.getInputStream()));
    // 獲取輸出流 並 放到寫Buffer 中
    PrintWriter output = new PrintWriter(client.getOutputStream());
  
    while(isRunning.get())
    {
      final String message = input.readLine();
      Log.i(TAG,"message "+message);
      if (!TextUtils.isEmpty(message))  {
              
        output_msg="";
        doMsg(client,message);
        Log.i(TAG,output_msg);
        if (!TextUtils.isEmpty(output_msg)) 
        {
          output.println(output_msg);
          output.flush();
        }
      
      }
      else
        break;
    }
    // 關閉
    output.close();
    input.close();
    client.close();

  } 

    private String output_msg="";
    class Thread1 implements Runnable {
      ServerSocket serverSocket = null;
      private void do_run()
      {
        try{   
          if(myApp.skip_network)    return;
          // 監聽連線請求
          Socket socket = serverSocket.accept();
          new Thread() {
             @Override
             public void run() {
               try {
               
                 responseClient(socket);
               
               }catch (IOException e)
               {
                e.printStackTrace();
               }
               catch (Throwable t) {
                // just end the background thread
                CrashLogCatch.logit(t);
                }
             }
          }.start();
         
        }
       catch (IOException e)
        {
          e.printStackTrace();
        }
    }
    
    @Override
    public void run() {

      while(isRunning.get())
      {
        try {
              Thread.sleep(500);
        } catch (InterruptedException e) {
                  //Log.d("error");
        }

        try {
            if(myApp.skip_network)    return;
             serverSocket = new ServerSocket(MyApplication.SERVER_PORT);

             while(isRunning.get())
             {
               do_run();
             }
        }
        catch(IOException e)
        {
          
          continue;
        }
        catch (Throwable t) {
          // just end the background thread
          CrashLogCatch.logit(t);
        }


      }

    }
  
    }
    private boolean do_ringset(String[] str_arr)
    {
      boolean ret = true;

      String ip = myApp.m_local_ip;
      //if(str_arr[1].equals("127.0.0.1") || str_arr[1].equals(ip))
      if(true)
      {
        output_msg = "OK";
  
        myApp.ringSetAction(str_arr);
      }
      else
      {
          ret = false;
          output_msg = "ERROR 1 "+str_arr[1]+" "+ip+"\n";
      }

      return ret;
    }

    private boolean do_ringget()
    {
      boolean ret = true;
      String ip = myApp.m_local_ip;
      //if(str_arr[1].equals("127.0.0.1") || str_arr[1].equals(ip))
      if(true)
      {
        String str = "OK "+Boolean.toString(myApp.m_ring_enable)+" "+myApp.m_ring_number;

        str = str+" "+String.valueOf(myApp.mCurMicVol)+" "+String.valueOf(myApp.getVolume());
        str = str+" "+myApp.m_ring_phone_ip;
        
        output_msg = str;
      }
      else
      {
        ret = false;
        output_msg = "ERROR 2";
        
      }  

      return ret; 
    }
  private boolean do_getstate()
  {
    String str;
    boolean ret = true;

    str = "STATE,";
    str = str + do_getstateBase();
    
    str = str +myApp.get_sos_output();
   
    output_msg = str;

    return ret; 
  }    
  public String do_getstate1()
  {
    String str;

    str = "STATE1,";
    str = str + do_getstateBase();
    str = str +myApp.get_sos_output();
    
    if(myApp.m_ctrl_gas)
      str = str+",1";
    else     
      str = str+",0";

    return str;
  }      
  private String do_getstateBase()
  {
    boolean ret = true;
    
    String str_lock="0";
        
    if(myApp.m_is_lock)
    {
      str_lock = "1";
    }

    String str_dnd="0";

    if(myApp.getDND())
    {
      str_dnd="1";
    }
    String str_alarm_delay_time=String.valueOf(myApp.m_sd_timer);

    String str = str_dnd+","+str_lock+","+str_alarm_delay_time+",";

    return str; 
  }    

  public void send_update()
  {
    m_remote_ip = myApp.m_door_ip;
    send_msg("UPDATE");
  }
  private boolean do_toggle()
  {
    myApp.lockPinAction();
    send_update();
    return do_getstate();
 
  }    

    private boolean do_lock()
    {
      String str;

      str = "LOCKFAIL";
      if(myApp.m_is_lock)    
      {
          str = "LOCKOK";
      }
      else 
      {
        boolean ret = myApp.toggleLock();

        if(ret)
        {
          str = "LOCKOK";
          myApp.update_lock();
          

        }
        else 
        {
          str = str+"="+myApp.m_message;
        }
      }

      output_msg = str;    

      return true; 
    }     
    private boolean do_delay(boolean val)
    {
      String str;

      str = "SDOFFOK";

      if(val)    
      {
          str = "SDONOK";
      }

      myApp.m_sd = val;
    
      output_msg = str;     

      return true; 
    }  
    private boolean do_unlock(String key)
    {
      String str;

      str = "UNLOCKFAIL";
  
      if (key.equals(myApp.m_str_passwd)) // CORRECT
      {
        str = "UNLOCKOK";
       
        myApp.setLockFromRemote(false);
        
      }
      
      output_msg = str;

      return true; 
    }                 

    private boolean do_setGas(String key)
    {
      String str;

      str = "SETGASOK";
      if(key.equals("1"))
      {
        myApp.m_ctrl_gas = true;
        
      }
      else
      {
        myApp.m_ctrl_gas = false;
        
      }

      ctrlGasAction(myApp.m_ctrl_gas);
      output_msg = str;

      return true; 
    }    

  private void do_saveRegInfo()
  {
    myApp.saveUserInfo();
  }
  private void do_saveSOSInfo()
  {
    myApp.do_setSOS();
  }

  private void do_saveZoneInfo()
  {
    myApp.do_setZone();
  }
  private void do_saveDoorInfo()
  {
    myApp.do_setDoor();
  }  
  private void do_saveTransferInfo()
  {
    myApp.do_setTransfer();
  }    
  private void do_saveOtherInfo()
  {
    myApp.do_setOther();
  }   
  private void do_saveMailInfo()
  {
    myApp.do_setMail();
  }      
  private void do_saveEthernetInfo()
  {
    myApp.do_setEthernet();
  }      

  private void do_show_dialog()
  {
    Log.i(TAG,"do_show_dialog");
		Intent activityIntent = new Intent(this, MainActivity.class);
		//activityIntent.putExtra("incomingSession",sessionId);
		activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		//activityIntent.putExtra(MyApplication.EXTRA_SOS_ALARM, -1);
		startActivity(activityIntent);    

  
  }    
  private void do_show_door()
  {
    myApp.showTips_ex("door ring");
  }    

  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<WaService> mActivity;
    public StaticHandler(WaService activity)
    {
      mActivity = new WeakReference<WaService>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      WaService activity = mActivity.get();
      if(activity == null) return;
  
      switch(msg.what)
      {
        case SAVE_REG:
        
        activity.do_saveRegInfo();
  
        break;
        case SAVE_SOS:
        
        activity.do_saveSOSInfo();
  
        break;       
        case SAVE_ZONE:
        
        activity.do_saveZoneInfo();
  
        break;        
        case SAVE_DOOR:
        
        activity.do_saveDoorInfo();
  
        break;        
        case SAVE_TRANSFER:
        
        activity.do_saveTransferInfo();
  
        break;                                              
        case SAVE_OTHER:
        
        activity.do_saveOtherInfo();
  
        break;  
        case SAVE_MAIL:
        
        activity.do_saveMailInfo();
  
        break;                                                      
        case SAVE_ETHERNET:
        
        activity.do_saveEthernetInfo();
  
        break;                                                      

        case SHOW_DIALOG:
        
        activity.do_show_dialog();
  
        break;      
        case SHOW_DOOR:
        
        activity.do_show_door();
  
        break;      

      }
  
    }
  }    
  private void postMessage(int id)
  {
    Message message=new Message();
    message.what = id;
    
    mHandler.sendMessage(message);
  } 
  private void sendRegInfo()
  {
    String info = myApp.getUserInfo();
    output_msg = "GETREG,"+info;
  }      
  private void sendOtherInfo()
  {
    String info = myApp.getOtherInfo();
    output_msg = "GETOTHER,"+info;
  }
  private void sendEthernetInfo()
  {
    String info = myApp.getEthernetInfo();
    output_msg = "GETETHERNET,"+info;
  }

  private void sendDoorInfo()
  {
    String info = myApp.getDoorInfo();
    output_msg = "GETDOOR,"+info;
  }
  private void sendTransferInfo()
  {
    String info = myApp.getTransferInfo();
    output_msg = "GETTRANSFER,"+info;
  }  
  private void sendZoneInfo()
  {
    String info = myApp.getZoneInfo();
    output_msg = "GETZONE,"+info;
  }
  private void sendSOSInfo()
  {
    String info = myApp.getSOSInfo();
    output_msg = "GETSOS,"+info;
  }
  private void saveDoorInfo(String[] msg)
  {
    if(msg.length >= 11)
    {
      myApp.saveDoorInfo_arr(msg);
      postMessage(SAVE_DOOR);
    }
    output_msg = "DOORSEND,OK,"+String.valueOf(msg.length);
  }          
  private void saveTransferInfo(String[] msg)
  {
    if(msg.length >= 6)
    {
      myApp.saveTransferInfo_arr(msg);
      postMessage(SAVE_TRANSFER);
    }
    output_msg = "TRANSFERSEND,OK,"+String.valueOf(msg.length);
  } 
  private void saveMailInfo(String[] msg)
  {
      myApp.saveMailInfo_arr(msg);
      postMessage(SAVE_MAIL);
      output_msg = "MAILSEND,OK,"+String.valueOf(msg.length);
  }        
  private void saveEthernetInfo(String[] msg)
  {
      myApp.saveEthernetInfo_arr(msg);
      postMessage(SAVE_ETHERNET);
      output_msg = "ETHERNETSEND,OK,"+String.valueOf(msg.length);
  }                         
  private void saveOtherInfo(String[] msg)
  {
    if(msg.length >= 17)
    {
      myApp.saveOtherInfo_arr(msg);
      postMessage(SAVE_OTHER);
      output_msg = "OTHERSEND,OK,"+String.valueOf(msg.length);
    }
    else 
    {
      output_msg = "OTHERSEND,ERROR,"+String.valueOf(msg.length);
    }
  }              

  private void saveRegInfo(String[] msg)
  {
    if(msg.length >= 20)
    {
      myApp.saveUserInfo_arr(msg);
      postMessage(SAVE_REG);
    }
    output_msg = "REGSEND,OK,"+String.valueOf(msg.length);
  }          
  private void saveSOSInfo(String[] msg)
  {
    if(msg.length >= 18)
    {
      myApp.saveSOSInfo_arr(msg);
      postMessage(SAVE_SOS);
    }
    output_msg = "SOSSEND,OK,"+String.valueOf(msg.length);
  }          

  private void saveZoneInfo(String[] msg)
  {
    if(msg.length >= 7)
    {
      myApp.saveZoneInfo_arr(msg);
      postMessage(SAVE_ZONE);
    }
    output_msg = "ZONESEND,OK,"+String.valueOf(msg.length);
  }          
  private boolean do_config(String msg)
  {
    String[] str_arr1 = msg.split("\n");
    if(str_arr1.length >= 1)
    {
      String[] str_arr = str_arr1[0].split(",");
      if(str_arr.length >= 2)
      {
        if(str_arr[1].equals("GETREG"))
        {
          sendRegInfo();
        }
        else if(str_arr[1].equals("GETSOS"))
        {
          sendSOSInfo();
        }
        else if(str_arr[1].equals("GETZONE"))
        {
          sendZoneInfo();
        }        
        else if(str_arr[1].equals("GETDOOR"))
        {
          sendDoorInfo();
        }        
        else if(str_arr[1].equals("GETTRANSFER"))
        {
          sendTransferInfo();
        }    
        else if(str_arr[1].equals("GETOTHER"))
        {
          sendOtherInfo();
        }                                
        else if(str_arr[1].equals("GETETHERNET"))
        {
          sendEthernetInfo();
        }                                
        else if(str_arr[1].equals("SETREG"))
        {
          saveRegInfo(str_arr);
        }
        else if(str_arr[1].equals("SETSOS"))
        {
          saveSOSInfo(str_arr);
        }       
        else if(str_arr[1].equals("SETZONE"))
        {
          saveZoneInfo(str_arr);
        }                
        else if(str_arr[1].equals("SETDOOR"))
        {
          // ptool-門鈴設定
          saveDoorInfo(str_arr);
        }                
        else if(str_arr[1].equals("SETTRANSFER"))
        {
          saveTransferInfo(str_arr);
        }  
        else if(str_arr[1].equals("SETOTHER"))
        {
          saveOtherInfo(str_arr);
        }  
        else if(str_arr[1].equals("SETMAIL"))
        {
          saveMailInfo(str_arr);
        }   
        else if(str_arr[1].equals("SETETHERNET"))
        {
          saveEthernetInfo(str_arr);
        }
        else if(str_arr[1].equals("GETVERSION"))
        {
          output_msg = "GETVERSION," + MyApplication.VERSION;
        }
        // 呼叫 ProcessLogFile 處理 GETLOGFILE
        else if (str_arr[1].equals("GETLOGFILE")) {
            // 直接把收到的 msg 給 ProcessLogFile 解析
            String output_json = ProcessLogFile.handleCommand(getApplicationContext(), msg);
            output_msg = output_json;
        }
      }
      else 
      {
        output_msg = "FAIL";
      }
    }
    else 
    {
      //int index = msg.indexOf("CONFIG,");
      output_msg = "FAIL";
    }
    return true;
  }
    private boolean doMsg(Socket socket,String msg)
    {
      boolean ret;

      Log.i(TAG,"doMsg "+msg);
      ret = true;
      if(msg.startsWith("CONFIG,"))
      {
        return do_config(msg);
        
      }
      String[] str_arr = msg.split(" ");

      if(str_arr.length == 1)
      {
        if(str_arr[0].equals("GETSTATE"))
        {
            ret = do_getstate();
        }
        else if(str_arr[0].equals("APPGETSTATE"))
        {
           output_msg = do_getstate1();
            ret = true;
        }

        else if(str_arr[0].equals("GETSTATE1"))
        {
          String ipaddr = socket.getInetAddress().getHostAddress();
          ipaddr = ipaddr.replace("\n","");
          myApp.setDoorIP(ipaddr);
          ret = do_getstate();
        }        
        else if(str_arr[0].equals("UPDATE"))
        {
            output_msg = "OK";
            myApp.re_getstate();
        }                
        else if(str_arr[0].equals("TOGGLE"))
        {
            ret = do_toggle();
        }        
        else if(str_arr[0].equals("LOCK"))
        {
            ret = do_lock();
        }        
        else if(str_arr[0].equals("SDON"))
        {
            ret = do_delay(true);
        }
        else if(str_arr[0].equals("SDOFF"))
        {
            ret = do_delay(false);
        }        
        else if(str_arr[0].equals("GASCLOSE"))
        {
          output_msg = "CLOSEOK";
          ret = true;
        }      
      }
      else if(str_arr.length >= 2)
      {
 
        if(str_arr[0].equals("RINGSET"))
        {
            ret = do_ringset(str_arr);

        }
        else if(str_arr[0].equals("RINGGET"))
        {
            ret = do_ringget();

        }
        else if(str_arr[0].equals("UNLOCK"))
        {
            ret = do_unlock(str_arr[1]);
        }       
        else if(str_arr[0].equals("SETGAS"))
        {
            ret = do_setGas(str_arr[1]);
        }                   
      }
      else {
        ret = false;
        output_msg = msg;
        
      }

      return ret;

    }
   
    private void wa_set_ring(String msg)
    {
      Log.i(TAG,"wa_set_ring "+msg);
      String[] str_arr = msg.split(",");

      if(str_arr.length < 3)    return;

      if(str_arr[0].equals("STATE"))
      {
        myApp.m_ring_first = false;

        if(str_arr[2].equals("0"))
          myApp.m_is_lock = false;
        else 
          myApp.m_is_lock = true;

        if(myApp.m_is_lock)
        {
          openLED();
        }
        else
        {
          closeLED();
        }

        myApp.m_ring_first = false;

        myApp.update_lock();
      }

    }
    class SendMsgThread implements Runnable {
      public void run() {

         try {
            if(myApp.skip_network)    return;
           
            if(TextUtils.isEmpty(m_remote_ip))    return;
            Socket socket=new Socket();
            Log.i(TAG,"ringhread "+m_remote_ip);
            InetSocketAddress isa = new InetSocketAddress(m_remote_ip, MyApplication.SERVER_PORT);
            socket.connect(isa, MyApplication.MAX_CONNECT_TIME);
            //socket = new Socket(SERVER_IP, MyApplication.SERVER_PORT);
            PrintWriter output = new PrintWriter(socket.getOutputStream());

            BufferedReader input = new BufferedReader(new InputStreamReader(socket.getInputStream()));
            while(socket.isConnected())
            {
                Log.i(TAG,"Connected "+m_send_msg);
                socket.setSoTimeout(MyApplication.MAX_SOCKET_TIMEOUT);
                
                output.write(m_send_msg);
                output.flush();

                final String message = input.readLine();
                if (message != null) {
                   Log.i(TAG,message);
                   
                    wa_set_ring(message);

                } else {

                }

                socket.close();

            }
         }
         catch (SocketTimeoutException e) {
            //e.printStackTrace();
            Log.i(TAG,"SocketTimeoutException1");
            //ringSocket.close();

         }
         catch (IOException e) {
            e.printStackTrace();

         }
         catch (Throwable t) {
          // just end the background thread
          CrashLogCatch.logit(t);
          }
      }
   }

    public void get_phone_sos()
    {
      send_msg("GETSTATE1");
    }
    private void DisplayError(int resourceId) {


    }
    public void ctrlBZ() {
      m_ctrl_bz = true;
      Log.i(TAG,"m_ctrl_bz");

    }    
    public void openBZLong() {
        m_open_bz_long = true;
        Log.i(TAG,"openBZLong");

    }

    public void openBZShort() {
        m_open_bz_short = true;
        Log.i(TAG,"openBZShort");

    }

    public void open_door() {
        m_open_door = true;
        Log.i(TAG,"open_door");

    }
    public void openLED() {
        m_open_led = true;
        Log.i(TAG,"openLED");

    }
    public void closeLED() {
        m_close_led = true;
        Log.i(TAG,"closeLED");

    }
    public void openBZ() {
        m_open_bz = true;
        Log.i(TAG,"openBZ");

    }
    public void closeBZ() {
        m_close_bz = true;
        Log.i(TAG,"closeBZ");

    }

    private void send_msg(String msg)
    {
      m_send_msg = msg+"\n";
      
      sendMsgThread = new Thread(new SendMsgThread());

      sendMsgThread.start();
      
    }    

    private void alive_broadcast()
    {
      //Log.i(TAG,"alive_broadcast");
      Intent intent = new Intent(RemoteCastielService.ALIVE_BROADCAST);
           
      intent.putExtra(RemoteCastielService.EXTRA_MYPID,mypid);
      sendBroadcast(intent);
    }
  
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG,"onStartCommand");

        //mHandler.postDelayed(alive_app, 1000);

        return START_STICKY;
        //return super.onStartCommand(intent,flags,startId);
    }

    private MyBinder myBinder;
    @Override
    public boolean onUnbind(Intent intent) {
        return super.onUnbind(intent);
    }
    public String getServiceName(){
        return WaService.class.getSimpleName();
    }    
    public class MyBinder extends Binder {
      public WaService getService(){
          return WaService.this;
      }
    }    
    @Override
    public IBinder onBind(Intent intent) {
        return myBinder;
    }
    @Override
    public void onCreate() {
        super.onCreate();
        
        Log.i(TAG,"onCreate "+getPackageName());
        mypid = android.os.Process.myPid();
        if (myBinder == null) {
          myBinder = new MyBinder();
        }        
        myApp = (MyApplication) getApplicationContext();
        myApp.setWaService(this);
        
        if(myApp.m_ctrl_gas)
          m_close_gas = true;
        else 
          m_open_gas = true;

        m_remote_ip = myApp.m_ring_phone_ip;
        try {
          /* Create a receiving thread */
          
            mshowThread = new ShowThread();
            mshowThread.start();

            mi2cThread = new i2cThread();
            mi2cThread.start();

            thread1 = new Thread(new Thread1());
            thread1.start();

            UDPThread1 = new Thread(new UDPThread());
            isRunning.set(true);
            UDPThread1.start();
        } catch (SecurityException e) {
            //DisplayError(R.string.error_security);
        } catch (InvalidParameterException e) {
            //DisplayError(R.string.error_configuration);
        }

        mHandler.postDelayed(alive_app, 1000);

        Intent intent1 = new Intent(this , MainActivity.class);
        intent1.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent1);            
    }

    @Override
    public void onDestroy() {
         Log.i(TAG,"onDestroy");
         if (mshowThread != null)
         {
            mshowThread.interrupt();
             //mi2cThread = null;
         }

        if (mi2cThread != null)
        {
            mi2cThread.interrupt();
            //mi2cThread = null;
        }

        if(m_i2c != null)
        {
          m_i2c.close();
        }

        if (thread1 != null)
        {
            thread1.interrupt();
            //mi2cThread = null;
        }

        if (UDPThread1 != null)
        {
            isRunning.set(false);
            //mi2cThread = null;
        }

        super.onDestroy();
    }

}
