/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.weema.sipsample.service;

import android.app.Application;
import android.util.Log;
import android.widget.Toast;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import android.util.Log;

public class i2cDriver {

  private static final String TAG = "i2cDriver";
  private static final int IO_NUMBER = 16;
  private static final int MAX_COUNT = 10;
  private static final int MAX_KEYBOARD_COUNT = 1;
  public boolean m_is_init=false;
  private String m_file = null;

	/*
	 * Do not remove or rename the field mFd: it is used by native method close();
	 */

	public i2cDriver(String file) throws SecurityException, IOException {

    m_file = file;


		//mFileInputStream = new FileInputStream(mFd);
		//mFileOutputStream = new FileOutputStream(mFd);
	}
  public void medicine()
  {
    for(int i=1;i<8;i++)
    {
      do_medicine(i);
    }
  }
  public void do_medicine(int index)
  {
    byte[] arr_index = new byte[1];

    arr_index[0] = (byte)index;
    byte[] arr_dir = new byte[IO_NUMBER];

    for(int i=0;i<8;i++)
    {
      arr_dir[i] = 1;
    }

    for(int i=8;i<IO_NUMBER;i++)
    {
      arr_dir[i] = 0;
    }

    byte[] arr_init = new byte[IO_NUMBER];

    for(int i=0;i<arr_init.length;i++)
    {
      arr_init[i] = 0;
    }

    byte[] arr_count = new byte[IO_NUMBER];

    for(int i=0;i<arr_count.length;i++)
    {
      arr_count[i] = MAX_COUNT;
    }

    byte[] arr_mode = new byte[1];

    arr_mode[0] = 0;
  
    //arr_init[15] = 1;
    init(arr_index,arr_dir,arr_init,arr_count,arr_mode);

  }
    public int do_init(boolean is_sos_mode)
    {
        if(m_is_init != false)    return 0;

        int total=0;
        File device = new File(m_file);

      /* Check access permission */
  		if (!device.canRead() || !device.canWrite()) {
  			try {
  				/* Missing read/write permission, trying to chmod the file */
          Log.e(TAG, "!device.canRead() || !device.canWrite()");
  				Process su;
  				su = Runtime.getRuntime().exec("su");

  				String cmd = "chmod 0777 " + device.getAbsolutePath() + "\n"
  						+ "exit\n";
  				su.getOutputStream().write(cmd.getBytes());
  				if ((su.waitFor() != 0) || !device.canRead()
  						|| !device.canWrite()) {
            //init();
  					//throw new SecurityException();

  				}
          else
          {


          }

  			} catch (Exception e) {
          //init();
  				e.printStackTrace();
  				//throw new SecurityException();
  			}
  		}
      else
      {


      }

      m_is_init = true;

      byte[] arr_index = new byte[1];

      arr_index[0] = 0;
      byte[] arr_dir = new byte[IO_NUMBER];

      for(int i=0;i<arr_dir.length;i++)
      {
        arr_dir[i] = 1;
      }

      if(is_sos_mode == false)
      {
        for(int i=0;i<4;i++)
        {
          arr_dir[i] = 0;
        }
      }

      arr_dir[WaService.LED_PIN] = 0;
      arr_dir[WaService.CTRL_GAS_PIN] = 0;
      arr_dir[WaService.OPEN_DOOR_PIN] = 0;
      arr_dir[WaService.BZ_PIN] = 0;
      arr_dir[WaService.CTRL_BZ_PIN] = 1;

      byte[] arr_init = new byte[IO_NUMBER];

      for(int i=0;i<arr_init.length;i++)
      {
        arr_init[i] = 0;
      }

      if(is_sos_mode == false)
      {
        for(int i=0;i<4;i++)
        {
          arr_init[i] = 1;
        }
      }

      if(is_sos_mode == false)
      {
        arr_init[4] = 1;
      }

      arr_init[5] = 1;
      arr_init[6] = 1;
      arr_init[7] = 1;

      arr_init[WaService.CTRL_GAS_PIN] = 1;
      arr_init[WaService.DOOR_RING_PIN] = 1;
      arr_init[WaService.LOCK_PIN] = 1;
      arr_init[WaService.DOOR_PIN] = 1;
      arr_init[WaService.CTRL_BZ_PIN] = 1;

      byte[] arr_count = new byte[IO_NUMBER];

      byte count = MAX_COUNT;

      if(is_sos_mode == false)
      {
        count = MAX_KEYBOARD_COUNT;
      }

      for(int i=0;i<arr_count.length;i++)
      {
        arr_count[i] = count;
      }
    
      arr_count[WaService.DOOR_RING_PIN] = 80;//AX_COUNT/2;
      arr_count[WaService.LOCK_PIN] = MAX_COUNT/2;
      
      //arr_init[15] = 1;
      byte[] arr_mode = new byte[1];

      arr_mode[0] = 0;
      if(is_sos_mode)
        arr_mode[0] = 1;

      total = init(arr_index,arr_dir,arr_init,arr_count,arr_mode);
      //medicine();
      return total;
    }
    public native String  stringFromJNI();

	// JNI
    public native int init(byte[] arr_index,byte[] arr_dir,byte[] arr_init,byte[] arr_count,byte[] arr_mode);
		public native void close();
    public native byte[] read(byte[] by);
    public native byte[] write(byte[] by);

	static {
		System.loadLibrary("i2c-driver");
	}
}
