package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Button;

import android.widget.TextView;
import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;
import com.weema.sipsample.util.Ring;
public class AlarmActivity extends Fragment {

    private MainActivity mainActivity;

    private final String TAG = "AlarmActivity";

    private int m_sos_call_timer = 0;

    private MyApplication myApp;
    
    private static final int MAX_SOS_CALL_TIME = 30;
    private static final int MAX_FINISH_TIME = 1;

    private EditText editText;
    private TextView textView;

    private String m_str_title;
    private String m_str;
    private String m_str_passwd;
    private int m_id;
    private int m_delay_timer=0;
    private static final int MAX_DELAY_TIME=30;
    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  		Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        Log.i(TAG,"onCreateView");
        super.onCreateView(inflater, container, savedInstanceState);

        View rootView = inflater.inflate(R.layout.activity_alarm, null);
        //initView(rootView);
        mainActivity = (MainActivity) getActivity();
        myApp = (MyApplication) mainActivity.getApplicationContext();
        return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;

       if (!hidden){

            m_delay_timer = 0;
           m_str = "";
           editText.setText(m_str_title+m_str);
           mainActivity = (MainActivity) getActivity();
           m_str_passwd = myApp.m_str_passwd;
           show_alarm_picture();
           mainActivity.receiver.broadcastReceiver = null;

           
           if(myApp.get_alarm_id() == 5 && myApp.m_sostalk_enable)
           {
                m_sos_call_timer = 1;
           }
           else
                m_sos_call_timer = MAX_SOS_CALL_TIME;
           
            if(is_need_delay())
            {
                m_sos_call_timer = 0;
            }
          
           Log.i(TAG,"onHiddenChanged");
        
       }
       else {
           mainActivity.stopSOSAlarm();
           
           m_sos_call_timer = 0;

       }
   }

   private void initView(View view) {

        m_str = "";

   	    m_str_title = mainActivity.getResources().getString(R.string.str_alarm_passwd);

        editText = (EditText)view.findViewById(R.id.alarm_editText);

        textView = (TextView)view.findViewById(R.id.alarm_text);

        initOnClickListener(view);

   }

   private void initOnClickListener(View view) {

       int my_ids[] = {
           R.id.alarm_dialButton_0,R.id.alarm_dialButton_1,R.id.alarm_dialButton_2,
           R.id.alarm_dialButton_3,R.id.alarm_dialButton_4,R.id.alarm_dialButton_5,
           R.id.alarm_dialButton_6,R.id.alarm_dialButton_7,R.id.alarm_dialButton_8,
           R.id.alarm_dialButton_9,R.id.alarm_dialButton_esc,R.id.alarm_dialButton_ok
       };


       ImageButton b = null;
       for( int i=0 ; i< my_ids.length ; ++i )
               if( ( b = (ImageButton)view.findViewById( my_ids[i]) ) != null )
                       b.setOnClickListener(btnDoListener);
   }

   private boolean is_need_delay()
   {
       boolean ret = false;

        if(myApp.get_alarm_id() < 5 && myApp.m_sd)
        {
            ret = true;
            
        }

        return ret;
   }
   private void show_alarm_picture()
   {
       m_id = myApp.get_alarm_id();
       String msg;

       switch(m_id)
       {
         case 0:
          msg = "2";
          textView.setBackgroundResource(R.drawable.win1);
         break;
         case 1:
         msg = "2";
          textView.setBackgroundResource(R.drawable.win2);
         break;
         case 2:
         msg = "2";
          textView.setBackgroundResource(R.drawable.win3);
         break;
         case 3:
         msg = "2";
          textView.setBackgroundResource(R.drawable.win4);
         break;
         case 4:
         msg = "2";
          textView.setBackgroundResource(R.drawable.win5);
         break;
         case 5:
         msg = "1";
          textView.setBackgroundResource(R.drawable.help);
         break;
         case 6:
         msg = "3";
          textView.setBackgroundResource(R.drawable.gas);
         break;
         case 7:
         msg = "4";
          textView.setBackgroundResource(R.drawable.mog);
         break;
         default:
         msg = "2";
         textView.setBackgroundResource(R.drawable.win1);
         break;
       }
       
       if(is_need_delay())
       {
           m_delay_timer = MAX_DELAY_TIME;
           return;
       }
       if(m_id == 5 && myApp.m_sostalk_enable)
       {

       }
       else
            mainActivity.playSOSAlarm(m_id);

        myApp.alarm_msg_send("3",m_id);
   }


   private Button.OnClickListener btnDoListener=new Button.OnClickListener(){
       @Override
       public void onClick(View v)
       {
           String str="";
           switch(v.getId())
           {
               case R.id.alarm_dialButton_0:

               str = "0";
               break;

               case R.id.alarm_dialButton_1:
               str = "1";
               break;
               case R.id.alarm_dialButton_2:
               str = "2";
               break;
               case R.id.alarm_dialButton_3:
               str = "3";
               break;
               case R.id.alarm_dialButton_4:
               str = "4";
               break;
               case R.id.alarm_dialButton_5:
               str = "5";
               break;
               case R.id.alarm_dialButton_6:
               str = "6";
               break;
               case R.id.alarm_dialButton_7:
               str = "7";
               break;
               case R.id.alarm_dialButton_8:
               str = "8";
               break;
               case R.id.alarm_dialButton_9:
               str = "9";
               break;
               case R.id.alarm_dialButton_esc:
               str ="";
               m_str = "";
               break;
               case R.id.alarm_dialButton_ok:
               break;
           }

           if(v.getId() == R.id.alarm_dialButton_ok)
           {
               if(m_str.length() <= 0)
               {
                   mainActivity.showTips(R.string.str_1_word);

               }
               else
               {
                   if (m_str.equals(m_str_passwd))
                   {
                       myApp.alarm_msg_send("1",m_id);
                       m_id = myApp.unLockAlarm();

                       if(m_id >= 0)
                       {
                           m_str = "";
                           editText.setText(m_str_title+m_str);
                           show_alarm_picture();
                           m_sos_call_timer = MAX_SOS_CALL_TIME;
                       }
                       else
                       {
                        myApp.send_update();
                        postMessage(MyApplication.MAIN_MENU);
                           
                       }
                   }
                   else
                   {
                       mainActivity.showTips(R.string.str_passwd_error);
                       m_str = "";
                       editText.setText(m_str_title+m_str);

                   }

               }

           }
           else
           {
                m_str += str;
                editText.setText(m_str_title+m_str);
           }
       }
   };

 

public void timeout() {
    // TODO Auto-generated method stub

    if(m_is_active == false)    return;
    Log.i(TAG,"timertask");

    if(m_delay_timer > 0)
    {
        m_delay_timer--;

        if(m_delay_timer <= 0)
        {
            postMessage(MyApplication.ALARM_DELAY);
 
        }
    }
    if(m_sos_call_timer > 0)
    {
        if(myApp.get_alarm_id() < 0)
        {
            m_sos_call_timer = 0;
            postMessage(MyApplication.GOTO_BACK);
            return;
        }

        m_sos_call_timer--;
        if(m_sos_call_timer == 0)
        {
            postMessage(MyApplication.SOS_CALL);
            

        }
    }
}
private void postMessage(int id)
{
  Message message=new Message();
  message.what = id;
  mHandler.sendMessage(message);
}  
private void alarm_delay()
{
    mainActivity.showTips("alarm delay");
    mainActivity.playSOSAlarm(m_id);

    myApp.alarm_msg_send("3",m_id);    
    m_sos_call_timer = MAX_SOS_CALL_TIME;   
}
  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<AlarmActivity> mActivity;
    public StaticHandler(AlarmActivity activity)
    {
      mActivity = new WeakReference<AlarmActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
        AlarmActivity myactivity = mActivity.get();
        if(myactivity == null) return;

        switch(msg.what)
        {
            case MyApplication.SOS_CALL:
            myactivity.outgoingCall();
            break;
            case MyApplication.GOTO_BACK:
            myactivity.mainActivity.loadConsoleActivity();
            break;            
            case MyApplication.MAIN_MENU:
            myactivity.mainActivity.showTips(R.string.str_setting_unlock_ok);
            myactivity.mainActivity.loadConsoleActivity();            
            break;
            case MyApplication.ALARM_DELAY:
            myactivity.alarm_delay();
           
            break;
        }

    }
  }
 

    private void outgoingCall() {

        myApp.m_sos_index = 0;
        for(int i=0;i<5;i++)
        {
            String number = myApp.getNextSOSCall();
            if(number == null)
            {
              mainActivity.showTips("outgoing call end");
              break;
            }
            long ret = myApp.outgoingCall(true,number);
            if(ret > 0)
            {
                Ring.getInstance(getActivity()).startRingBackTone();
                myApp.closeBZ();
                mainActivity.loadVideoFragment();
                break;
            }
        }
    }

}
