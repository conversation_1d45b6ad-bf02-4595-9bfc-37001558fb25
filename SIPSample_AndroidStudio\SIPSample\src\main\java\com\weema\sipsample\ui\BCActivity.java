package com.weema.sipsample.ui;


import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.content.Context;

import android.os.Bundle;
import android.view.View;

import android.widget.ImageButton;
import android.widget.Button;

import android.widget.TextView;
import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;
import android.content.Intent;
import com.weema.sipsample.util.Ring;
import android.text.TextUtils;
public class BCActivity extends Fragment {

    MainActivity mainActivity;
    //private MyReceiver myReceiver;
    private static final String TAG = "BCActivity";

    private MyApplication myApp;
    
    private int m_finish_timer;
    private static final int MAX_FINISH_TIME = 60;

    private TextView titleView;
    private TextView textView;

    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  		Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        Log.i(TAG,"onCreateView");
        super.onCreateView(inflater, container, savedInstanceState);

        View rootView = inflater.inflate(R.layout.activity_bc, null);
        //initView(rootView);
        mainActivity = (MainActivity) getActivity();
        myApp = (MyApplication) mainActivity.getApplicationContext();
        return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;

       if (!hidden){

           mainActivity.receiver.broadcastReceiver = null;
           show_bc();
           Log.i(TAG,"onHiddenChanged");
  
           m_finish_timer = MAX_FINISH_TIME;
       }
       else {
            Ring.getInstance(getActivity()).stopRingTone();
            m_finish_timer = 0;
       }
   }

   private void show_bc()
   {
        String content = myApp.bc_content;
        if(TextUtils.isEmpty(content))   return;

        String[] str_arr = content.split("&");

        String title;
        String msg;
        if(str_arr[1].equals("1"))
        {
            //title = "環保鈴聲";
            msg = "環保鈴聲";
            Ring.getInstance(getActivity()).startBCTone();
        }
        else 
        {
           //title = "文字訊息";
           msg = str_arr[2];

        }

        //titleView.setText(title);
        textView.setText(msg);

    }

   
   private void initView(View view) {

        textView = (TextView)view.findViewById(R.id.bc_text);
        titleView = (TextView)view.findViewById(R.id.bc_title);

        initOnClickListener(view);

   }

   
   private void initOnClickListener(View view) {

       int my_ids[] = {
           R.id.bc_ok,
       };

       ImageButton b = null;
       for( int i=0 ; i< my_ids.length ; ++i )
            if( ( b = (ImageButton)view.findViewById( my_ids[i]) ) != null )
                b.setOnClickListener(btnDoListener);
   }

   private Button.OnClickListener btnDoListener=new Button.OnClickListener(){
       @Override
       public void onClick(View v)
       {
           if(v.getId() == R.id.bc_ok)
           {
              m_finish_timer = 1;
           }
         
       }
   };

  

    public void timeout() {
         // TODO Auto-generated method stub

        if(m_is_active == false)    return;
         
         if(m_finish_timer > 0)
         {
            Log.i(TAG,"timertask");
           m_finish_timer--;
           if(m_finish_timer == 0)
           {
               postMessage(MyApplication.GOTO_BACK);

           }
         }
    }
    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }    
  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<BCActivity> mActivity;
    public StaticHandler(BCActivity activity)
    {
      mActivity = new WeakReference<BCActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
        BCActivity activity = mActivity.get();
        if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
            activity.mainActivity.loadConsoleActivity();
             
            //Toast.makeText(OptSOSActivity.this, R.string.str_no_respose, Toast.LENGTH_LONG).show();
            break;

        }
    }
  }    

}
