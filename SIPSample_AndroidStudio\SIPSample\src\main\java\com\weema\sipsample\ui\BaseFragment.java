package com.weema.sipsample.ui;

import android.app.Fragment;
import android.content.Intent;
import android.os.Bundle;
//import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Toast;

import com.portsip.PortSipSdk;
import com.weema.R;
import com.weema.sipsample.adapter.ContactAdapter;
import com.weema.sipsample.receiver.PortMessageReceiver;
import com.weema.sipsample.service.PortSipService;
import com.weema.sipsample.util.CallManager;
import com.weema.sipsample.util.Contact;
import com.weema.sipsample.util.ContactManager;

import java.util.List;

public class BaseFragment extends Fragment implements View.OnTouchListener{
    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        view.setOnTouchListener(this);
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        return true;
    }
}
