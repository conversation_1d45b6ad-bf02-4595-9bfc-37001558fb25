package com.weema.sipsample.ui;

import android.Manifest;
import android.app.Activity;
import android.app.Fragment;
import android.app.FragmentTransaction;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
//import android.support.annotation.IdRes;
import androidx.annotation.IdRes;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.os.Bundle;
//import android.support.v4.app.ActivityCompat;
import 	androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.widget.RadioGroup;
import android.widget.Toast;

import com.weema.R;
import com.weema.sipsample.receiver.PortMessageReceiver;
import com.weema.sipsample.service.PortSipService;
import com.weema.sipsample.service.PortSipService2;
import com.weema.sipsample.service.PortSipService3;

import com.weema.sipsample.service.PortSipServiceP2P;

import android.content.SharedPreferences;
import android.preference.PreferenceManager;

import android.view.View;

import android.util.Log;

import com.weema.sipsample.util.Session;
import com.weema.sipsample.util.CallManager;

import android.content.Context;

import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import java.util.Timer;
import java.util.TimerTask;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.PowerManager;
import android.app.KeyguardManager;
import android.app.KeyguardManager.KeyguardLock;

import android.util.DisplayMetrics;
import static com.weema.sipsample.service.PortSipService.EXTRA_REGISTER_STATE;
import static com.weema.sipsample.service.PortSipService.EXTRA_REGISTER_LINE;
import com.weema.sipsample.service.WaService;
import android.media.MediaPlayer;
import java.io.File;
import com.portsip.PortSipSdk;
import java.io.InputStream;
import java.io.OutputStream;
import 	java.io.FileOutputStream;
import 	java.io.IOException;
import 	java.io.FileInputStream;
import 	android.os.Environment;

import java.net.Socket;
import java.net.InetAddress;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import 	java.net.SocketTimeoutException;
import 	java.net.InetSocketAddress;
import android.text.TextUtils;
import org.json.JSONObject;
import org.json.JSONException; 
import android.os.AsyncTask;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import android.util.Log;

import 	java.util.Calendar;
import java.text.SimpleDateFormat;
import 	java.text.ParseException;

import android.view.KeyEvent;
import org.json.JSONArray;
import com.weema.sipsample.util.CrashLogCatch;
import com.weema.sipsample.util.Ring;
public class BaseMainActivity extends Activity implements RadioGroup.OnCheckedChangeListener,PortMessageReceiver.BroadcastListener {

  private final String TAG="BaseMainActivity";
  private String ACTION_SOS_EVENT = "android.intent.action.SOS_EVENT";
  private String EXTRA_SOS_STATE = "android.intent.extra.SOS_STATE";

	private final static String REPLY_INFO_ACTION = "REPLY_INFO_ACTION";
	private final static String EXTRA_ACTIVITY = "EXTRA_ACTIVITY";
	private final static String EXTRA_NAME = "EXTRA_NAME";
	private final static String EXTRA_VER = "EXTRA_VER";
	private final static String GET_APP_MSG = "GET_SOFTPHONE1_MSG";
    
  private MediaPlayer m_SOSPlayer=null;
  public PortMessageReceiver receiver = null;

  private static final int NEED_LOGIN = 1;
  private static final int TRYLOGIN = 2;
  private static final int NEED_LOCK = 3;
  private static final int GET_MDOOR = 4;
  private static final int GET_CONFIG = 5;    
  private static final int RECORD_START = 6;    
  private static final int GET_IPCAM = 7;  
  private static final int SOS_START = 8; 
  private static final int LOCK_ACTION = 9; 
  private static final int LOADVIDEOFRAGMENT = 10;
  private static final int LOADCONSOLEACTIVITY = 11;
  private static final int SOS_KEY_UP = 12;
  private static final int SOS_KEY_DOWN = 13;
  private static final int SHOW_DOOR_RINGACTION = 14;
  private static final int SHOW_LOCK_PINACTION = 15;
  private static final int SHOW_MDOOR_ACTION = 16;

  private final int REQ_DANGERS_PERMISSION = 2;
  private final int REQ_ACTION_MANAGE_WRITE_SETTINGS =6;

  private VolumeActivity volumeActivity=null;
  private EditActivity editActivity=null;
  private TxlActivity txlActivity=null;
  private TranSetActivity tranSetActivity=null;
  private ConsoleActivity consoleActivity=null;
  private MySettingActivity mySettingActivity=null;
  private LoginActivity loginActivity=null;
  private LoginFragment loginFragment=null;
  private MessageFragment messageFragment=null;
  private VideoFragment videoFragment=null;
  
  private NumpadFragment numpadFragment=null;
  private SettingFragment settingFragment=null;
  private OptSOSActivity optSOSActivity=null;
  private UnLockActivity unlockActivity=null;
  private SetUnLockActivity setunlockActivity=null;
  private SOSActivity sosActivity=null;
  private ZoneActivity zoneActivity=null;
  private MdoorActivity mdoorActivity=null;
  private VideoCallActivity videoCallActivity=null;
  private AlarmActivity alarmActivity=null;
  private BCActivity bcActivity=null; 
  private MailActivity mailActivity=null; 
  private RingSettingActivity ringSettingActivity=null;
  private SettingOtherActivity settingOtherActivity=null;   
  private SettingNetworkActivity settingNetworkActivity=null;   
  private AlarmMsgActivity alarmMsgActivity=null;
  private CenterIPActivity centerIPActivity=null;   
  private VideoActivity videoActivity=null; 

  private MyApplication myApp;
  private Boolean misP2PLogin;

  private Timer timer=null;
  private TimerTask timerTask=null;

  public boolean[] m_alarm = new boolean[8];

  private static int m_first_timer = 1;
  private static int m_need_register;
  private static int [] m_registers;
  private int m_check_register=0;
  private int m_get_config=0;
  private int m_get_ipcam=0;
  private final int MAX_CHECK_REGISTER = 600;
  private final int MAX_GET_CONFIG = 600;
  private final int MAX_GET_IPCAM = 60*60;
    
  public int m_lock_timer;
  
  private static int m_register_line;
  private int m_alarm_timer = 0;
  private static final int MAX_ALARM_TIME=16;

  private boolean m_is_show;

  private Thread ringThread=null;

  private int m_ring_timer=0;

  private String m_ring_ip;
  private String m_ring_number;
  private String m_ring_enable;
  private String m_ring_mic;
  private String m_ring_volume;
  private String m_ring_phone_ip;

  private boolean m_is_get;
  private int m_mdoor_timer;
  private static final int MAX_MDOOR_TIME=600;
  private String phoneNumber;
  private String center_ip;

  private int record_timer;
  private int mail_timer;  
  private final int RECORD_TIME=30;
  private final int SHORT_RECORD_TIME=2;    

  private boolean m_is_sos_press = false;

	 private int m_key_sos_release=0;
  private final int MAX_SOS_RELEASE=2;
  public static String m_ver;
  

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        String archType = Build.CPU_ABI;
        Log.i(TAG,"onCreate ");

        m_is_show = false;

        m_mdoor_timer=MAX_MDOOR_TIME/100;

        String path = Environment.getExternalStorageDirectory().getPath();
        Log.i(TAG,path);
        for(int i=0;i<m_alarm.length;i++)
        {
            m_alarm[i] = false;
        }

        
        receiver = new PortMessageReceiver();
        receiver.broadcastReceiver = null;
        setContentView(R.layout.main);
        
        myApp = (MyApplication) getApplicationContext();

        myApp.setMyContext(this);
        IntentFilter filter = new IntentFilter();
        filter.addAction(PortSipService.REGISTER_CHANGE_ACTION);
        filter.addAction(PortSipService.CALL_CHANGE_ACTION);
        filter.addAction(PortSipService.PRESENCE_CHANGE_ACTION);

        filter.addAction(MyApplication.NETWORK_CHANGE_ACTION);
        filter.addAction(MyApplication.FWD_CHANGE_ACTION);
        filter.addAction(PortSipService.ANSWER_ACTION);
        filter.addAction(PortSipService.AUTO_ONHOOK_ACTION);

        filter.addAction(MyApplication.DOOR_ACTION);
        
        filter.addAction(MyApplication.RING_SET_REPLY);
        filter.addAction(MyApplication.RING_GET_REPLY);
        filter.addAction(MyApplication.IPCAM_ACTION);
        filter.addAction(MyApplication.LOCK_ACTION);
        filter.addAction(MyApplication.PRESSKEY_ACTION);
        filter.addAction(MyApplication.MAIL_ACTION);     
        filter.addAction(MyApplication.SHOW_DOOR_RINGACTION);  
        filter.addAction(MyApplication.SHOW_LOCK_PINACTION);   
        filter.addAction(MyApplication.SHOW_MDOOR_ACTION);          
        filter.addAction(MyApplication.DOOR_ACTION_CALL);        

        filter.addAction(ACTION_SOS_EVENT);
        filter.addAction(GET_APP_MSG);

        receiver.mainBroadcastReceiver = this;
        registerReceiver(receiver, filter);
        //loadConsoleActivity();
        //loadLoginActivity1();
        //switchContent(R.id.login_fragment);
        RadioGroup menuGroup = (RadioGroup) findViewById(R.id.tab_menu);
        menuGroup.setOnCheckedChangeListener(this);

        menuGroup.setVisibility(View.GONE);
        misP2PLogin = false;
        //trylogin(0xff);

        m_lock_timer = 0;

        m_registers = new int[MyApplication.MAX_LINES];

        for(int i=0;i<MyApplication.MAX_LINES;i++)
        {
          if(isEnable(i+1) == true)
          {
              m_registers[i] = 1;

          }
          else
              m_registers[i] = 0;
        }
        phoneNumber = myApp.getPhoneNumber(1,false);
        initTimer();
        m_get_config = MAX_GET_CONFIG;
        m_get_ipcam = MAX_GET_IPCAM;
     
        reply_info_action();
    }

    public void trylogin(int line)
    {
      if(misP2PLogin == false)
      {
        Intent onLineIntent = new Intent(this, PortSipServiceP2P.class);
        onLineIntent.setAction(PortSipServiceP2P.ACTION_SIP_REGIEST);
        this.startService(onLineIntent);
        misP2PLogin = true;
      }

      if(line == 0xff || line == 1)
          registerServer(1);

      if(line == 0xff || line == 2)
          registerServer(2);

      if(line == 0xff || line == 3)
          registerServer(3);

    }

    public void registerServer(int line)
    {
      //showTips("registerServer "+line);
      m_check_register = MAX_CHECK_REGISTER;

      Intent onLineIntent;

      if(line == 1 && isEnable(1))
      {
          onLineIntent = new Intent(this, PortSipService.class);
      }

      else if(line == 2 && isEnable(2))
      {
          onLineIntent = new Intent(this, PortSipService2.class);
      }

      else if(line == 3 && isEnable(3))
      {
          onLineIntent = new Intent(this, PortSipService3.class);
      }
      else
      {
        onLineIntent = new Intent(this, PortSipService.class);
      }

      onLineIntent.setAction(PortSipService.ACTION_SIP_REGIEST);

      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
          this.startForegroundService(onLineIntent);
      }else{
          this.startService(onLineIntent);
      }

    }

    private void SaveUserInfo() {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(this).edit();

        editor.putBoolean(PortSipService.SIP_ENABLE, true);

        editor.putString(PortSipService.USER_NAME, "1014");
        editor.putString(PortSipService.USER_PWD, "1014ab");
        editor.putString(PortSipService.SVR_HOST, "************");
        editor.putString(PortSipService.SVR_PORT, "6088");

        editor.putString(PortSipService.USER_DOMAIN, "sipweema.com.tw");
        //editor.putString(PortSipService.OUTBOUND_PORT, editText_port_outbound.getText().toString());

        editor.commit();
    }

    public boolean isEnable(int line)
    {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(this);

        Boolean check_enable;
        String user_name;
        String passwd;
        String ip;

        check_enable = preferences.getBoolean(PortSipService.SIP_ENABLE, false);

        user_name = preferences.getString(PortSipService.USER_NAME, "");

        passwd = preferences.getString(PortSipService.USER_PWD, "");

        ip = preferences.getString(PortSipService.SVR_HOST, "");

        //if(check_enable == false)
        if(myApp.m_is_need_acc)
        {
            SaveUserInfo();
            check_enable = true;
            user_name = "1014";
            passwd = "1014ab";

            ip = "************";

        }

        if(line == 2)
        {

          check_enable = preferences.getBoolean(PortSipService.SIP_ENABLE2, false);

          user_name = preferences.getString(PortSipService.USER_NAME2, "");

          passwd = preferences.getString(PortSipService.USER_PWD2, "");

          ip = preferences.getString(PortSipService.SVR_HOST2, "");
        }
        else if(line == 3)
        {
          check_enable = preferences.getBoolean(PortSipService.SIP_ENABLE3, false);

          user_name = preferences.getString(PortSipService.USER_NAME3, "");

          passwd = preferences.getString(PortSipService.USER_PWD3, "");

          ip = preferences.getString(PortSipService.SVR_HOST3, "");


        }

        //Toast.makeText(MainActivity.this, "line "+String.valueOf(line)+" "+String.valueOf(check_enable), Toast.LENGTH_LONG).show();

        if(check_enable == true && !user_name.isEmpty() && !passwd.isEmpty() && !ip.isEmpty())
        {
            //Toast.makeText(MainActivity.this, "isenable "+user_name+" "+passwd+" "+ip, Toast.LENGTH_LONG).show();

            //return false;
            return true;
        }
        else
            return false;
    }
      @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);// must store the new intent unless getIntent() will return the old one
    }
    @Override
    protected void onResume() {
        super.onResume();
        myApp.setMyContext(this);
        //if(true) return;
        myApp.m_is_show = true;

        delay_record();
        myApp.m_local_ip = myApp.getLocalIP(false);
        m_local_ip = myApp.m_local_ip;
        //showTips("IP "+myApp.m_local_ip);
        m_is_show = true;
        requestPermissions (this);

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(REQ_ACTION_MANAGE_WRITE_SETTINGS == requestCode) {
            if (Build.VERSION.SDK_INT >= 23) {
                if (!Settings.System.canWrite(this)) {
                    System.exit(0);
                }
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        myApp.m_is_show = false;
        m_is_show = false;
        Log.i(TAG,"onPause");
        //showTips("onPause");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.i(TAG,"onDestroy");
        unregisterReceiver(receiver);
        showTips("onDestroy1");

    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           String permissions[], int[] grantResults) {
        switch (requestCode) {
            case REQ_DANGERS_PERMISSION:
                int i=0;
                for(int result:grantResults) {
                    if (result != PackageManager.PERMISSION_GRANTED) {
                        Toast.makeText(this, "you must grant the permission "+permissions[i], Toast.LENGTH_SHORT).show();
						i++;
                        stopService(new Intent(this,PortSipService.class));
                        System.exit(0);
                    }
                }
                break;
        }
    }
    @Override
    public void onCheckedChanged(RadioGroup radioGroup, int checkedId) {
        switch (checkedId) {
        
        }

    }

    public void loadOptSOSActivity() {
      if (optSOSActivity == null) {
        optSOSActivity = new OptSOSActivity();
      }

      switchContent(optSOSActivity);
    }
    public void loadMDoorActivity() {
      if (mdoorActivity == null) {
        mdoorActivity = new MdoorActivity();
      }

      switchContent(mdoorActivity);
    }
    public void loadVideoCallActivity() {
      if (videoCallActivity == null) {
        videoCallActivity = new VideoCallActivity();
      }

      switchContent(videoCallActivity);
    }
    public void loadRingSettingActivity() {
      if (ringSettingActivity == null) {
        ringSettingActivity = new RingSettingActivity();
      }

      switchContent(ringSettingActivity);
    }
    public void loadSettingOtherActivity() {
      if (settingOtherActivity == null) {
        settingOtherActivity = new SettingOtherActivity();
      }

      switchContent(settingOtherActivity);
    }    
    public void loadSettingNetworkActivity() {
      if (settingNetworkActivity == null) {
        settingNetworkActivity = new SettingNetworkActivity();
      }

      switchContent(settingNetworkActivity);
    }    

    public void loadAlarmMsgActivity() {
      if (alarmMsgActivity == null) {
        alarmMsgActivity = new AlarmMsgActivity();
      }

      switchContent(alarmMsgActivity);
    }
    public void loadCenterIPActivity() {
      if (centerIPActivity == null) {
        centerIPActivity = new CenterIPActivity();
      }

      switchContent(centerIPActivity);
    }    
    public void loadVideoActivity() {
      if (videoActivity == null) {
        videoActivity = new VideoActivity();
      }

      switchContent(videoActivity);
    }        
    public void loadAlarmActivity() {
      if (alarmActivity == null) {
        alarmActivity = new AlarmActivity();
      }

      switchContent(alarmActivity);
    }
    public void loadBCActivity() {
      if (bcActivity == null) {
        bcActivity = new BCActivity();
      }

      switchContent(bcActivity);
    }
    public void loadMailActivity() {
      if (mailActivity == null) {
        mailActivity = new MailActivity();
      }

      switchContent(mailActivity);
    }
    public void loadZoneActivity() {
      if (zoneActivity == null) {
        zoneActivity = new ZoneActivity();
      }

      switchContent(zoneActivity);
    }
    public void loadVolumeActivity() {
      if (volumeActivity == null) {
        volumeActivity = new VolumeActivity();
      }

      switchContent(volumeActivity);
    }

    public void loadEditActivity() {
    	if (editActivity == null) {
    		editActivity = new EditActivity();
    	}

      switchContent(editActivity);
    }

    public void loadSettingFragment() {
    	if (settingFragment == null) {
    		settingFragment = new SettingFragment();
    	}
      switchContent(settingFragment);
    }

    public void loadTxlActivity() {
    	if (txlActivity == null) {
    		txlActivity = new TxlActivity();
    	}
      switchContent(txlActivity);
    }

    public void LoadTranSetActivity() {
      if (tranSetActivity == null) {
      	tranSetActivity = new TranSetActivity();
      }

      switchContent(tranSetActivity);
    }

    public void loadVideoFragment()
    {
      if (videoFragment == null)
          videoFragment = new VideoFragment();

      switchContent(videoFragment);

    }

    public void loadConsoleActivity()
    {
      //if(true)
      if(myApp.m_is_sos_mode == false)
      {
        loadMailActivity();
        return;
      }

      int id = myApp.get_alarm_id();

      if(id >= 0){
        sos_alarm_ex(id);
        return;
      }
      
      if (consoleActivity == null)
          consoleActivity = new ConsoleActivity();

      switchContent(consoleActivity);

    }

    public void loadLoginActivity()
    {
      if (loginActivity == null)
        loginActivity = new LoginActivity();

      switchContent(loginActivity);

    }

    public void loadLoginActivity1()
    {
      if (loginFragment == null)
          loginFragment = new LoginFragment();

      switchContent(loginFragment);

    }
    public void loadSOSActivity()
    {
      if (sosActivity == null)
        sosActivity = new SOSActivity();

        switchContent(sosActivity);

    }
    public void loadSetUnlockActivity()
    {
      if (setunlockActivity == null)
        setunlockActivity = new SetUnLockActivity();

        switchContent(setunlockActivity);

    }
    public void loadUnLockActivity()
    {
      if (unlockActivity == null)
        unlockActivity = new UnLockActivity();

        switchContent(unlockActivity);
    }
    public void loadMySettingFragment()
    {
      if (mySettingActivity == null)
        mySettingActivity = new MySettingActivity();

        switchContent(mySettingActivity);
    }
 
    public Boolean inCall()
    {
      Boolean ret = false;
      if(mContent == (Fragment)videoFragment)
      {
        ret = true;
      }

      return ret;
    }

    public Boolean isReady()
    {
      Boolean ret = false;
      if(mContent == (Fragment)consoleActivity)
      {
        ret = true;
      }

      return ret;
    }
    Fragment mContent = null;
    private void switchContent(Fragment to) {
        if (mContent != to) {
          try{
            FragmentTransaction fTransaction = getFragmentManager().beginTransaction();
            if (!to.isAdded()) {
                if (mContent != null) {
                    fTransaction.hide(mContent);
                }
                fTransaction.add(R.id.content, to).commit();
            } else {
                // Hide current fragment,display next fragment
                fTransaction.hide(mContent).show(to).commit();
            }

            mContent = to;
          }
          catch(Exception ex)
          {
             ex.printStackTrace();
          }
        }

    }

    private void switchContent(@IdRes int fragmentId) {
        Fragment fragment = getFragmentManager().findFragmentById(fragmentId);
        //Fragment login_fragment = getFragmentManager().findFragmentById(R.id.login_fragment);
        //Fragment numpad_fragment = getFragmentManager().findFragmentById(R.id.numpad_fragment);
        //Fragment video_fragment = getFragmentManager().findFragmentById(R.id.video_fragment);
        //Fragment setting_fragment = getFragmentManager().findFragmentById(R.id.setting_fragment);
        //Fragment message_fragment = getFragmentManager().findFragmentById(R.id.message_fragment);

        FragmentTransaction fTransaction = getFragmentManager().beginTransaction();
        //fTransaction.hide(login_fragment).hide(numpad_fragment).hide(video_fragment).hide(setting_fragment).hide(message_fragment);
        //fTransaction.hide(setting_fragment);

        if(fragment!=null){
            fTransaction.show( fragment).commit();
        }
    }

    public void requestPermissions(Activity activity) {
        // Check if we have write permission
        if(		PackageManager.PERMISSION_GRANTED == ActivityCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                &&PackageManager.PERMISSION_GRANTED == ActivityCompat.checkSelfPermission(activity, Manifest.permission.CAMERA)
                &&PackageManager.PERMISSION_GRANTED == ActivityCompat.checkSelfPermission(activity, Manifest.permission.RECORD_AUDIO))
        {
            requestSpecialPermission(activity);
        }else{
            ActivityCompat.requestPermissions(activity,new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE,
                            Manifest.permission.CAMERA,Manifest.permission.RECORD_AUDIO},
                    REQ_DANGERS_PERMISSION);
        }
    }

    private void requestSpecialPermission(Activity activity){
        if(Build.VERSION.SDK_INT>=23){
            if(!Settings.System.canWrite(activity)){
                Intent intent = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS);
                intent.setData(Uri.parse("package:" + activity.getPackageName()));
                activity.startActivityForResult(intent, REQ_ACTION_MANAGE_WRITE_SETTINGS );
            }
        }
    }

   
    public void processCallChange(long sessionId)
    {
      Session session  = CallManager.Instance().findSessionBySessionID(sessionId);

      if(session == null)    return;
      if(CallManager.Instance().isCurrentLine(session) == false)
      {
        CallManager.Instance().setCurrentLine(session);
      }
      //ret = 0;
      if (session != null)
      {
        switch (session.state)
        {
          case INCOMING:
            
            myApp.saveCallLog(session);
            loadVideoFragment();

            break;
          case TRYING:
            loadVideoFragment();
            break;
          case CONNECTED:
            loadVideoFragment();
            break;
          case FAILED:
          case CLOSED:

            break;

        }
      }

    }

    public boolean is_alarm_state()
    {
      boolean ret = false;
      for(int i=0;i<m_alarm.length;i++)
      {
        if(m_alarm[i] != false)
        {
          ret = true;
          break;
        }
      }

      return ret;
    }
    private void outgoingCall() {

        for(int i=0;i<5;i++)
        {
            String number = myApp.getNextSOSCall();
            if(number == null)
            {
              number = myApp.getNextSOSCall();
              
            }
            if(number == null)
            {
              break;
              
            }

            long ret = myApp.outgoingCall(true,number);
            if(ret > 0)
            {
                Ring.getInstance(this).startRingBackTone();
                //myApp.closeBZ();
                loadVideoFragment();
                break;
            }
        }
    }
    private void sos_alarm_ex(int id)
    {
        if(id >= 0 && id < 8)
        {
            if(id >= 5 || myApp.m_is_lock != false)
            {
                m_alarm[id] = true;

                if(myApp.previous_call_status == 0){
                  outgoingCall();
                }
                else if(mContent != (Fragment)alarmActivity)
                {
                    loadAlarmActivity();
                }
            }

        }

    }   

    public void sos_alarm(int id)
    {
        if(id >= 0 && id < 8)
        {
            if(id >= 5 || myApp.m_is_lock != false)
            {
                m_alarm[id] = true;

                if(mContent == (Fragment)videoFragment)
                {
                    myApp.get_alarm_id();
                    Session session = CallManager.Instance().getCurrentSession();
                    if(session != null)
                      playSOSAlarmWav(session.voip_line,session.getSessionId());
                }
                else if(mContent != (Fragment)alarmActivity)
                {
                    loadAlarmActivity();
                }
            }

        }

    }   
    private void show_door_ringAction()
    {
        showTips("show_door_ringAction");
           
    }  
    private void show_lock_pinAction()
    {
        showTips("show_lock_pinAction");
           
    }   
    private void show_mdoor_action()
    {
        showTips("show_mdoor_action");
           
    }            
  private void sos_key_press(int state)
  {

    if(state == 1 && m_is_sos_press == false)
	  {
      showTips("SOS PRESS");
		  m_is_sos_press = true;

		  m_key_sos_release = MAX_SOS_RELEASE;
									
	  }
	  else
	  {
      showTips("SOS UP");
		  m_is_sos_press = false;
	  }    
  }  

  private void update_lock()
  {
    if(m_is_show)
      showTips(myApp.m_message);

    if(consoleActivity != null)
    {
      consoleActivity.update_lock();
    }    
  }
private void postMessage(int id)
{
  Message message=new Message();
  message.what = id;
  mHandler.sendMessage(message);
}  


private void reply_info_action()
{
    Intent it = new Intent(REPLY_INFO_ACTION);
    it.putExtra(EXTRA_ACTIVITY,"com.weema.sipsample.ui.MainActivity");
    it.putExtra(EXTRA_NAME,"softphone1");
    it.putExtra(EXTRA_VER,MyApplication.VERSION);
    sendBroadcast(it);
}
  public int OnBroadcastReceiver(Intent intent) {
  	String action = intent == null ? "" : intent.getAction();
  	Log.i(TAG,"OnBroadcastReceiver "+action+" "+String.valueOf(m_is_show));
    int ret;
        
    ret = -1;

    if (action.equals(MyApplication.SHOW_DOOR_RINGACTION))
    {
      postMessage(SHOW_DOOR_RINGACTION);

      ret = 0;
    }
    else if (action.equals(MyApplication.SHOW_LOCK_PINACTION))
    {
      postMessage(SHOW_LOCK_PINACTION);

      ret = 0;
    }    
    else if (action.equals(MyApplication.SHOW_MDOOR_ACTION))
    {
      postMessage(SHOW_MDOOR_ACTION);

      ret = 0;
    }        
    else if (action.equals(GET_APP_MSG))
    {

      reply_info_action();
 
    }

    else if (action.equals(ACTION_SOS_EVENT))
    {
      int state = intent.getIntExtra(EXTRA_SOS_STATE,0);

      postMessage(SOS_KEY_UP+state);
     
 
    }

    else if(MyApplication.LOCK_ACTION.equals(action))
    {
      postMessage(LOCK_ACTION);

      ret = 0;
    }

    if(m_is_show == false) return 0;

    if (PortSipService.CALL_CHANGE_ACTION.equals(action)) {
  		long sessionId = intent.getLongExtra(PortSipService.EXTRA_CALL_SEESIONID, Session.INVALID_SESSION_ID);
      Session session  = CallManager.Instance().findSessionBySessionID(sessionId);
      ret = 0;
      if (session != null)
      {
        if(CallManager.Instance().isCurrentLine(session) == false)
        {
          CallManager.Instance().setCurrentLine(session);
        }
  
      	switch (session.state)
      	{
      		case INCOMING:
          
          myApp.saveCallLog(session);
          postMessage(BaseMainActivity.LOADVIDEOFRAGMENT);
          
      		break;
      		case TRYING:
      		break;
      		case CONNECTED:
            postMessage(BaseMainActivity.LOADVIDEOFRAGMENT);
            
          break;
      		case FAILED:
      		case CLOSED:

      		break;

      	}
      }

  	}

    else if (PortSipService.REGISTER_CHANGE_ACTION.equals(action)) {
            //String tips  =intent.getStringExtra(EXTRA_REGISTER_STATE);

      int line = intent.getIntExtra(EXTRA_REGISTER_LINE,-1);
      if(line <= 0)
        return -1;

      boolean myregister = CallManager.Instance().getregist(line);

      if (true && myregister == false) {
        String tips  =intent.getStringExtra(EXTRA_REGISTER_STATE);
        myApp.setRegTips(line-1,tips);

        m_registers[line-1] = 1;
        m_first_timer = 1;

        //Toast.makeText(BaseMainActivity.this, "REGISTER_CHANGE_ACTION false "+tips, Toast.LENGTH_LONG).show();

      }
    }

    else if (PortSipService.ONHOOK_ACTION.equals(action))
    {

      ret = intent.getIntExtra(PortSipService.EXTRA_ONHOOK, -1);
      if(ret == 0)
      {
        postMessage(BaseMainActivity.LOADCONSOLEACTIVITY);
        
      }

    }
    else if (MyApplication.NETWORK_CHANGE_ACTION.equals(action))
    {

    }

    return ret;
  }

    public boolean isOnline(Context context)
    {
        boolean isOnline1 = false;
        try
        {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo netInfo = cm.getActiveNetworkInfo();
            //should check null because in airplane mode it will be null
            isOnline1 = (netInfo != null && netInfo.isConnected());
        }
        catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return isOnline1;
    }

    private int getNextRegisterLine()
    {
      int ret=0;

      for(int i=0;i<MyApplication.MAX_LINES;i++)
      {
          if(m_registers[i]>0)
          {
            ret = i+1;
            break;
          }
      }

      //showTips("getNextRegisterLine "+String.valueOf(ret));
      return ret;

    }

    private void getMoveRegisterLine(int line)
    {
       m_registers[line-1] = 0;

    }
    public boolean getRingNumber(String str_ip)
    {
        if(MyApplication.is_valid(str_ip) == false) return false;

        m_is_get = true;
        m_ring_ip = str_ip;

        m_ring_timer = 1;

        return true;
    }
    public boolean setRingNumber(String[] str_arr)
    {
        if(MyApplication.is_valid(str_arr[0]) == false) return false;

        m_is_get = false;
        m_ring_ip = str_arr[0];

        m_ring_enable = str_arr[1];

        m_ring_number = str_arr[2];

        m_ring_mic = str_arr[3];

        m_ring_volume = str_arr[4];

        m_ring_phone_ip = str_arr[5];

        m_ring_timer = 1;

        return true;
    }

    public boolean do_setRingNumber()
    {
         if(ringThread != null)
         {
           ringThread = null;

         }

         Log.i(TAG,"do_setRingNumber");
         ringThread=new Thread(new RingThread());                //賦予執行緒工作
         ringThread.start();

         return true;

    }


    private String m_local_ip;

    private void wa_set_ring(String msg)
    {
         String[] str_arr = msg.split(" ");

        if(str_arr[0].contains("OK"))
        {
            if(m_is_get != false)
            {
                if(str_arr.length >= 5)
                    myApp.ring_get_reply(str_arr);
            }
            else
                myApp.ring_set_reply();
        }
    }

    private Socket ringSocket;
    private PrintWriter socket_output_ring;
    private BufferedReader socket_input_ring;


    class RingThread implements Runnable {

       public void run() {

          try {
            if(myApp.skip_network)    return;
           
             ringSocket=new Socket();
             Log.i(TAG,"ringhread "+m_ring_ip);
					   InetSocketAddress isa = new InetSocketAddress(m_ring_ip, MyApplication.SERVER_PORT);
             
					   ringSocket.connect(isa, MyApplication.MAX_CONNECT_TIME);
             //socket = new Socket(SERVER_IP, MyApplication.SERVER_PORT);
             socket_output_ring = new PrintWriter(ringSocket.getOutputStream());

             socket_input_ring = new BufferedReader(new InputStreamReader(ringSocket.getInputStream()));
             while(ringSocket.isConnected())
             {
                 Log.i(TAG,"Connected");
                 ringSocket.setSoTimeout(MyApplication.MAX_SOCKET_TIMEOUT);
                 String str;
                 if(m_is_get == false)
                     str = "RINGSET "+m_local_ip+" "+m_ring_enable+" "+m_ring_number+" "+m_ring_mic+" "+m_ring_volume+" "+m_ring_phone_ip;
                 else
                     str = "RINGGET "+m_local_ip;

                 str = str+"\n";

                 Log.i(TAG,str);
                 socket_output_ring.write(str);
                 socket_output_ring.flush();

                 final String message = socket_input_ring.readLine();
                 if (message != null) {
                    Log.i(TAG,message);
                    
                     wa_set_ring(message);

                 } else {

                 }

                 ringSocket.close();

             }
          }
          catch (SocketTimeoutException e) {
             //e.printStackTrace();
             Log.i(TAG,"SocketTimeoutException1");
             //ringSocket.close();

          }
          catch (IOException e) {
             e.printStackTrace();


          }
          catch (Throwable t) {
            // just end the background thread
            CrashLogCatch.logit(t);
            }          
          ringSocket = null;
          ringThread = null;

       }
    }
  private void handle_need_login()
  {
    Intent onLineIntent;
    int register_line=0;

    if(isOnline(this) == false)
    {
      m_first_timer = 1;
         
      return;
    }

    trylogin(0);

    register_line = getNextRegisterLine();
    if(register_line == 1)
    {

      onLineIntent = new Intent(this, PortSipService.class);
    }
    else if(register_line == 2)
    {
      onLineIntent = new Intent(this, PortSipService2.class);
    }
    else if(register_line == 3)
    {
      onLineIntent = new Intent(this, PortSipService3.class);
    }
    else
      return;

    m_register_line = register_line;
    boolean ischeck = isEnable(register_line);
    if(ischeck == false)
    {
      m_registers[register_line-1] = 0;
      return;
    }
    onLineIntent.setAction(PortSipService.ACTION_SIP_UNREGIEST);
    startService(onLineIntent);

    m_need_register = 1;

  }
  private void handle_trylogin()
  {
    if(m_register_line > 0)
    {
      getMoveRegisterLine(m_register_line);
      trylogin(m_register_line);
       m_first_timer = 1;
    }

  }

  private void handle_sos_start()
  {
    showTips("SOS");
    myApp.dataReceiver(5);
  }
  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<BaseMainActivity> mActivity;
    public StaticHandler(BaseMainActivity activity)
    {
      mActivity = new WeakReference<BaseMainActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      BaseMainActivity activity = mActivity.get();
      if(activity == null) return;

      switch(msg.what)
      {
        case BaseMainActivity.NEED_LOGIN:
        activity.handle_need_login();

        break;
        case BaseMainActivity.TRYLOGIN:
        activity.handle_trylogin();

        break;
        case BaseMainActivity.NEED_LOCK:

        break;
        case BaseMainActivity.GET_MDOOR:

        break;
        case BaseMainActivity.GET_CONFIG:
          activity.do_get_config();
        break;
        case BaseMainActivity.GET_IPCAM:
          activity.do_get_ipcam();
        break;          
        case BaseMainActivity.RECORD_START:
          activity.gotoRecord();
            
        break;     
        case BaseMainActivity.SOS_START:
          activity.handle_sos_start();      
        break;    
       case BaseMainActivity.LOCK_ACTION:
          activity.update_lock();      
        break;         
       case BaseMainActivity.LOADVIDEOFRAGMENT:
          activity.loadVideoFragment();      
        break;      
       case BaseMainActivity.LOADCONSOLEACTIVITY:
          activity.loadConsoleActivity();      
        break;       
      case BaseMainActivity.SOS_KEY_UP:
          activity.sos_key_press(0);      
        break;  
     case BaseMainActivity.SOS_KEY_DOWN:
          activity.sos_key_press(1);      
        break;     
      case BaseMainActivity.SHOW_DOOR_RINGACTION:
          activity.show_door_ringAction();      
      break;  
      case BaseMainActivity.SHOW_LOCK_PINACTION:
          activity.show_lock_pinAction();      
      break;  
      case BaseMainActivity.SHOW_MDOOR_ACTION:
          activity.show_mdoor_action();      
      break;                                                                              
      }

    }
  }

    public void short_delay_record()
    {
        record_timer = SHORT_RECORD_TIME;
    }
    public void delay_record()
    {
        record_timer = RECORD_TIME;
    }
    public void stop_record()
    {
        record_timer = 0;
    }

    private void gotoRecord()
    {
      if(myApp.is_record_enable == false)    return;

      consoleActivity.m_is_active = false;
      
      Intent intent = new Intent(this, RecordActivity.class);
      //intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
      startActivity(intent);

    }
    public void stopSOSAlarm()
    {
        m_alarm_timer = 0;
        if(m_SOSPlayer != null)
        {
            m_SOSPlayer.stop();
            m_SOSPlayer = null;
        }

    }
    public void playSOSAlarm(int id)
    {
      try{
        if(m_SOSPlayer == null)
        {
            int alarm_id;
            if(id < 5)
                alarm_id = R.raw.win1;
             else if(id == 5)
               alarm_id = R.raw.sos1;
             else if(id == 6)
               alarm_id = R.raw.gas1;
             else
               alarm_id = R.raw.smog1;

            m_SOSPlayer = MediaPlayer.create(BaseMainActivity.this,alarm_id);
            m_SOSPlayer.prepare();
       }
      }catch(IllegalStateException e)
      {
        e.printStackTrace();
      }

      catch(Exception e){
        e.printStackTrace();
        //return;
      }

      m_SOSPlayer.start();
      m_SOSPlayer.setLooping(false);
      m_alarm_timer = MAX_ALARM_TIME;
    }


    private void process_mdoor()
    {
      if(myApp.skip_network)    return;

      if(m_mdoor_timer > 0)
      {
        m_mdoor_timer--;

        if(m_mdoor_timer == 0)
        {
          Log.i(TAG,"getFromMDoor");
          myApp.getFromMDoor();

          m_mdoor_timer = MAX_MDOOR_TIME;

        }
      }
    }    

    private void process_alarm()
    {
      if(m_alarm_timer > 0)
      {
        m_alarm_timer--;
        if(m_alarm_timer == (MAX_ALARM_TIME/2))
        {
          m_SOSPlayer.start();
        }
        else if(m_alarm_timer == 0)
        {
          myApp.openBZ();
        }
      }
    }

    private void process_lock()
    {
      if(m_lock_timer > 0)
      {
        m_lock_timer--;
        if(m_lock_timer == 0)
        {
          postMessage(BaseMainActivity.NEED_LOCK);
          //m_lock_timer = 1;
        }
      }
    }

    private void process_ring()
    {
      if(m_ring_timer > 0)
      {
        m_ring_timer--;
        if(m_ring_timer == 0)
        {
          do_setRingNumber();

        }
      }
    }

    private void process_first()
    {
      if(m_first_timer > 0)
      {
      
        m_first_timer--;

        if(m_first_timer == 0)
        {
          postMessage(BaseMainActivity.NEED_LOGIN);
      
        }
      }
    }

    private void process_need_register()
    {
      if(myApp.skip_network)    return;

      if(m_need_register > 0)
      {
        if(m_first_timer == 0)
          m_need_register--;

        if(m_need_register == 0)
        {
          postMessage(BaseMainActivity.TRYLOGIN);
      
        }
      }
      else
      {
        m_need_register = 0;
      }
    }

    private void process_check_register()
    {
      if(myApp.skip_network)    return;

      if(m_check_register > 0)
      {
        if(m_first_timer == 0)
          m_check_register--;

        if(m_check_register == 0)
        {
          m_check_register = MAX_CHECK_REGISTER;
          if(CallManager.Instance().getregist(1) == false)
          {
            m_registers[0] = 1;
            postMessage(BaseMainActivity.NEED_LOGIN);
        
          }
          else if(CallManager.Instance().getregist(2) == false)
          {
            m_registers[1] = 1;
            postMessage(BaseMainActivity.NEED_LOGIN);
           
          }
          else if(CallManager.Instance().getregist(3) == false)
          {
            m_registers[2] = 1;
            postMessage(BaseMainActivity.NEED_LOGIN);
          
          }

        }
      }
      else
      {
        m_check_register = MAX_CHECK_REGISTER;
      }
    }
    private void process_get_config()
    {
      if(myApp.skip_network)    return;

      if(m_get_config > 0)
      {
        m_get_config--;

        if(m_get_config == 0)
        {
          postMessage(BaseMainActivity.GET_CONFIG);

        }
      }
      else
      {
        m_get_config = MAX_GET_CONFIG;
      }
    }   

    private void process_get_ipcam()
    {
      if(myApp.skip_network)    return;

      if(m_get_ipcam > 0)
      {
        m_get_ipcam--;

        if(m_get_ipcam == 0)
        {
          postMessage(BaseMainActivity.GET_IPCAM);
        
        }
      }
      else
      {
        m_get_ipcam = MAX_GET_IPCAM;
      }
    }   
    public void do_get_config()
    {
      if(myApp.skip_network)    return;

      m_get_config = MAX_GET_CONFIG;
      center_ip = myApp.m_center_ip; 
      if (!TextUtils.isEmpty(center_ip) && !TextUtils.isEmpty(phoneNumber)) 
      {
        //phoneNumber = "5001";
        String url = "http://"+center_ip+"/api/users/getlimitcall?range="+phoneNumber;
        Log.i(TAG,url);
        new httpPostLimitcall().execute(url);
      }
    }
    public void do_get_ipcam()
    {
      if(myApp.skip_network)    return;

      m_get_ipcam = MAX_GET_IPCAM;
      String m_video_ip = myApp.m_video_ip; 
      Log.i(TAG,"do_get_ipcam "+m_video_ip);
      if (!TextUtils.isEmpty(m_video_ip)) 
      {
        //phoneNumber = "5001";
        String url = "http://"+m_video_ip+"/api/users/getipcam";
        Log.i(TAG,url);
        new httpPostIPCam().execute(url);
      }
    }    
    private void do_init_timer()
    {
      if(timer != null)
          timer = null;

   		timer=new Timer();
   		timer.schedule(timerTask,0,1000);
      
      do_get_config();
      do_get_ipcam();      
      
    } 
    private void process_record()
    {
        if(record_timer > 0)
        {
            --record_timer;
            if(record_timer == 0)
            {
              postMessage(BaseMainActivity.RECORD_START);
          
            }
        }
    }   
   
    private void process_fragment()
    {
      if (consoleActivity != null)
        consoleActivity.timeout();

      if (videoFragment != null)
        videoFragment.timeout();

      if (mySettingActivity != null)
          mySettingActivity.timeout();

      if (alarmActivity != null) {
          alarmActivity.timeout();
      }

      if (bcActivity != null) {
          bcActivity.timeout();
      }
      if (mailActivity != null) {
          mailActivity.timeout();
      }
      if (loginActivity != null)
          loginActivity.timeout();

     if (tranSetActivity != null) {
      	tranSetActivity.timeout();
      }

      if (unlockActivity != null)
        unlockActivity.timeout();

      if (centerIPActivity != null) 
        centerIPActivity.timeout();   

      if (alarmMsgActivity != null) {
        alarmMsgActivity.timeout();   
      }  
      if (mdoorActivity != null) {
        mdoorActivity.timeout();   
      }   

      if (zoneActivity != null) {
        zoneActivity.timeout();  
      }
      if (videoCallActivity != null) {
        videoCallActivity.timeout();  
      }

      if (videoActivity != null) {
        videoActivity.timeout();  
      }

      if (sosActivity != null)
        sosActivity.timeout();  

      if (setunlockActivity != null)
        setunlockActivity.timeout();  
      
      if (settingOtherActivity != null) {
        settingOtherActivity.timeout();  
      }
      if (settingNetworkActivity != null) {
        settingNetworkActivity.timeout();  
      }

      if (ringSettingActivity != null) {
        ringSettingActivity.timeout();  
      }  

     if (optSOSActivity != null) {
        optSOSActivity.timeout();  
      }         
    }    
    private void process_sos()
    {
	    if(m_is_sos_press != false && m_key_sos_release > 0)
	    {
		    m_key_sos_release--;
		    if(m_key_sos_release == 0)
		    {
		      m_is_sos_press = false;
          postMessage(BaseMainActivity.SOS_START);
	
		    }
	    }      
    }

    private void process_doorphone()
    {
      myApp.process_doorphone();
      myApp.process_delayLock();
    }

    private void initTimer()
   	{
   		timerTask = null;
   		timerTask=new TimerTask()
   		{
   				public void run()
   				{
            //Log.i(TAG,"timerTask");
            process_mdoor();
            process_alarm();
            process_lock();
            process_ring();
            process_first();
            process_need_register();
            process_check_register();
            process_get_config();
            process_get_ipcam();
            process_record();
            process_fragment();
            process_sos();
            process_doorphone();
   				}
   		};

      do_init_timer();     


   	}

    private File m_tmpWinFile = null;
    private File m_tmpGasFile = null;
    private File m_tmpSmogFile = null;
    private File m_tmpSOSFile = null;
    private File m_tmpFile = null;
    private File m_tmpFile1 = null;

    void playSOSAlarmWav(int voip_line,long session_id)
    {
      PortSipSdk myEngine = myApp.getVoipLine(voip_line);

        if(myApp.m_sos_id < 0 || myApp.m_is_sos_play == false) 
        {
          try {
          if(m_tmpFile1 == null)
            m_tmpFile1 = File.createTempFile("abc",".wav");
            //myEngine.playAudioFileToRemote(session_id,m_tmpFile1.getPath(),16000,false);
            
          }
            catch (IOException e) {
              e.printStackTrace();
          }

          return;
        }
                
        raw_to_file();

        Log.i(TAG,m_tmpFile.getPath()+" "+String.valueOf(voip_line)+" "+String.valueOf(session_id));
        //showTips(tmpFile.getPath());
     

        //myEngine.playAudioFileToRemote(session_id,"/storage/emulated/0/Download/gas1.wav",16000,true);

        myEngine.playAudioFileToRemote(session_id,m_tmpFile.getPath(),16000,false);
        //playAudioFileToRemote(seesionId,tmpFile.getPath(),8000,false);

    }
    void raw_to_file()
    {
          int sos_id;
          int id;
          sos_id = myApp.m_sos_id;
          String filename;
          if(sos_id >= 0 && sos_id < 5)
          {
            filename = "win";
            id = R.raw.win1;

          }
          else if(sos_id == 5)
          {
            filename = "sos";
            id = R.raw.sos1;
          }
          else if(sos_id == 6)
          {
            filename = "gas";
            id = R.raw.gas1;
          }
          else
          {
            filename = "smog";
            id = R.raw.smog1;
          }

          InputStream ins = BaseMainActivity.this.getResources().openRawResource (id);

          OutputStream output;

          try {
            if(sos_id >= 0 && sos_id < 5)
            {
                if(m_tmpWinFile == null)
                {
                  m_tmpWinFile = File.createTempFile(filename,".wav");
                }
                m_tmpFile = m_tmpWinFile;
            }
            else if(sos_id == 5)
            {
              if(m_tmpSOSFile == null)
              {
                m_tmpSOSFile = File.createTempFile(filename,".wav");
              }

              m_tmpFile = m_tmpSOSFile;
            }
            else if(sos_id == 6)
            {
              if(m_tmpGasFile == null)
              {
                m_tmpGasFile = File.createTempFile(filename,".wav");
              }

              m_tmpFile = m_tmpGasFile;
            }
            else
            {
                if(m_tmpSmogFile == null)
                {
                  m_tmpSmogFile = File.createTempFile(filename,".wav");
                }

                m_tmpFile = m_tmpSmogFile;

            }

          if(m_tmpFile == null) return;
          output = new FileOutputStream(m_tmpFile);

           final byte[] buffer = new byte[102400];
           int read;

           while ((read = ins.read(buffer))!= -1) {
               output.write(buffer, 0, read);
           }
           output.flush();
           output.close();
           ins.close();
           } catch (IOException e) {
               e.printStackTrace();
           }


    }
    void showTips(int id) {
  		//mtips.setText(text);
  		//spinnerAdapter.notifyDataSetChanged();
      //Log.i("myApp",text);
      if(m_is_show == false) return ;
  		Toast.makeText(this, id, Toast.LENGTH_LONG).show();
  	}
    void showTips(String text) {
      if(TextUtils.isEmpty(text))    return;
  		//mtips.setText(text);
  		//spinnerAdapter.notifyDataSetChanged();
      if(m_is_show == false) return ;
  		Log.i(TAG,text);
  		Toast.makeText(this, text, Toast.LENGTH_LONG).show();
  	}

    public void showTips_ex(String text) {
      if(TextUtils.isEmpty(text))    return;
  		//mtips.setText(text);
  		//spinnerAdapter.notifyDataSetChanged();
      if(m_is_show == false) return ;
  		Log.i(TAG,text);
  		Toast.makeText(this, text, Toast.LENGTH_LONG).show();
  	}

    @Override
    public void onBackPressed() {
        // your code.
        if(mContent == (Fragment)settingFragment ||
           mContent == (Fragment)volumeActivity ||
           mContent == (Fragment)loginActivity ||
           mContent == (Fragment)optSOSActivity ||
           mContent == (Fragment)zoneActivity
           )
    		{
            loadMySettingFragment();
    				//loadConsoleActivity();
    		}
        else if(mContent == (Fragment)unlockActivity ||
                mContent == (Fragment)setunlockActivity)
        {
            loadOptSOSActivity();
        }
        else if(mContent == (Fragment)mySettingActivity ||
                mContent == (Fragment)tranSetActivity)
        {
            loadConsoleActivity();
        }
        else
        {
            super.onBackPressed();
        }
    }
  	private void quit() {


    }
    private void fromJSon(String data) {

	    try {
	    // Here we convert Java Object to JSON
		  JSONObject jObj = new JSONObject(data);

		  JSONObject jObj1 = jObj.getJSONObject("data");

      String str = jObj1.getString("time");
      myApp.setTimeOffset(str);
      str = jObj1.getString("json");

      int[] night_time = new int[2];
      night_time[0] = 0;
      night_time[1] = 0;

      if(str.equals("{}"))
      {
          myApp.setLimitcall(this,"",false,night_time,"128");
          return ;
      }
      jObj = new JSONObject(str);
				
		  String allow = jObj.getString("allow");
     
      boolean night_enable = false;
      int int_enable = jObj.getInt("night_enable");
      if(int_enable > 0)    night_enable = true;

      int night_hour_start = jObj.getInt("night_hour");
      int night_min_start = jObj.getInt("night_min");

      int night_hour_end = jObj.getInt("night_hour_end");
      int night_min_end = jObj.getInt("night_min_end");

		  String night_call = jObj.getString("night_call");

      night_time = new int[2];

      night_time[0] = night_hour_start*100+night_min_start;
      night_time[1] = night_hour_end*100+night_min_end;

		  String[] range = allow.split(",");
      
      myApp.setLimitcall(this,allow,night_enable,night_time,night_call);
      

	    }
	    catch(JSONException ex) {
	        ex.printStackTrace();
	    }

    }	

    private void fromJSonIPCam(String data) {

	    try {
	      // Here we convert Java Object to JSON
		    JSONObject jObj = new JSONObject(data);

		    jObj = jObj.getJSONObject("data");

        String str_obj = jObj.getString("json");

        JSONArray jarr = new JSONArray(str_obj);
 
        if(jarr.length() == 0)
        {
          myApp.setIPCam("");
          return;
        }

        String str_ipcam = "";

        for(int i=0;i<jarr.length();i++)
        {
          jObj = jarr.getJSONObject(i);
          String ip = jObj.getString("ip");
          String type = jObj.getString("type");
          String number = jObj.getString("number");
          String port = jObj.getString("port");

          String enable = jObj.getString("enable");

          String str = enable+","+number+","+type+","+ip+","+port+"\n";

          str_ipcam = str_ipcam+str;
          Log.i(TAG,str);
        }

		    Intent i = new Intent(MyApplication.IPCAM_ACTION);

        i.putExtra(MyApplication.EXTRA_IPCAM,str_ipcam);

		    sendBroadcast(i);

        myApp.setIPCam(str_ipcam);
		
	    }
	    catch(JSONException ex) {
	        ex.printStackTrace();
	    }

    }	    
  public class httpPostLimitcall extends AsyncTask<String, Void, String> {
	  private final static String TAG = "httpPostLimitcall";
	  private String parameter1="1234";
	  private String parameter2;
	  private String parameter3;

	  @Override
	  protected String doInBackground(String... urls) {
		  return POST(urls[0]);
	  }

	  @Override
	  protected void onPostExecute(String result) {
		  Log.d(TAG,"onPostExecute");
      Log.i(TAG,result);
      if(!TextUtils.isEmpty(result))
      {
		    fromJSon(result);
        //showTips(myApp.getTimeOffset());
      }
	  }
   
	  private String POST(String APIUrl) {
		  String result = "";
		  HttpURLConnection connection;
		  try {
			  URL url = new URL(APIUrl);
			  connection = (HttpURLConnection)url.openConnection();
			  connection.setRequestMethod("POST");
			  //connection.setRequestProperty("authentication", MainActivity.Authentication);
			  connection.setDoInput(true);
			  connection.setDoOutput(true);
			  DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream());
			  StringBuilder stringBuilder = new StringBuilder();
			  stringBuilder.append("msg=").append(URLEncoder.encode(parameter1, "UTF-8")).append("&");
			  //stringBuilder.append("parameter2=").append(URLEncoder.encode(parameter2, "UTF-8")).append("&");
			  //stringBuilder.append("parameter3=").append(URLEncoder.encode(parameter3, "UTF-8")).append("&");
			  outputStream.writeBytes(stringBuilder.toString());
			  outputStream.flush();
			  outputStream.close();

			  InputStream inputStream = connection.getInputStream();
			  int status = connection.getResponseCode();
			  Log.d(TAG, String.valueOf(status));
			  if(inputStream != null){
				  InputStreamReader reader = new InputStreamReader(inputStream,"UTF-8");
				  BufferedReader in = new BufferedReader(reader);

				  String line="";
				  while ((line = in.readLine()) != null) {
					  result += (line+"\n");
				  }
			  } else{
				  result = "";
			  }
     
			  return  result;
		  } catch (Exception e) {
			  Log.d("ATask1 InputStream", e.getLocalizedMessage());
			  //e.printStackTrace();
			  result = "";
			  return result;
		  }
	  }
  }


  public class httpPostIPCam extends AsyncTask<String, Void, String> {
	  private final static String TAG = "httpPostIPCam";

	  @Override
	  protected String doInBackground(String... urls) {
      
		  return POST(urls[0]);
	  }

	  @Override
	  protected void onPostExecute(String result) {
		  Log.d(TAG,"onPostExecute");
      Log.i(TAG,result);

      if(!TextUtils.isEmpty(result))
		    fromJSonIPCam(result);
	  }
   
	  private String POST(String APIUrl) {
		  String result = "";
		  HttpURLConnection connection;
		  try {
			  URL url = new URL(APIUrl);
			  connection = (HttpURLConnection)url.openConnection();
			  connection.setRequestMethod("POST");
			  //connection.setRequestProperty("authentication", MainActivity.Authentication);
			  connection.setDoInput(true);
			  connection.setDoOutput(true);
			  DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream());
			  //StringBuilder stringBuilder = new StringBuilder();
			  //stringBuilder.append("msg=").append(URLEncoder.encode(parameter1, "UTF-8")).append("&");
			  //stringBuilder.append("parameter2=").append(URLEncoder.encode(parameter2, "UTF-8")).append("&");
			  //stringBuilder.append("parameter3=").append(URLEncoder.encode(parameter3, "UTF-8")).append("&");
			  //outputStream.writeBytes(stringBuilder.toString());
			  outputStream.flush();
			  outputStream.close();

			  InputStream inputStream = connection.getInputStream();
			  int status = connection.getResponseCode();
			  Log.d(TAG, String.valueOf(status));
			  if(inputStream != null){
				  InputStreamReader reader = new InputStreamReader(inputStream,"UTF-8");
				  BufferedReader in = new BufferedReader(reader);

				  String line="";
				  while ((line = in.readLine()) != null) {
					  result += (line+"\n");
				  }
			  } else{
				  result = "";
			  }
     
			  return  result;
		  } catch (Exception e) {
			  Log.d("ATask InputStream", e.getLocalizedMessage());
			  //e.printStackTrace();
			  result = "";
			  return result;
		  }
	  }
  }

}
