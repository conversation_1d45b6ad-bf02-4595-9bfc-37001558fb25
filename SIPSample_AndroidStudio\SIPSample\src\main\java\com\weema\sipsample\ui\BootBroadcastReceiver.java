package com.weema.sipsample.ui;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import com.weema.sipsample.service.WaService;
import com.weema.sipsample.service.RemoteCastielService;

public class BootBroadcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        if(intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED)){
            //Intent.ACTION_BOOT_COMPLETED == android.intent.action.BOOT_COMPLETED

            Intent intent1 = new Intent(context , MainActivity.class);
            intent1.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent1);

            //Data.m_is_boot_complete = true;
            //執行一個Activity

            //Intent intent2 = new Intent(context , MyService.class);
            //context.startService(intent2);
            //執行一個Service
        }
    }
}
