package com.weema.sipsample.ui;

import com.weema.R;
import com.weema.sipsample.receiver.PortMessageReceiver;
import com.weema.sipsample.service.PortSipService;

import android.app.Fragment;
import android.content.Intent;

import android.os.Bundle;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;

import android.widget.EditText;

import android.widget.TextView;

import static com.weema.sipsample.service.PortSipService.EXTRA_REGISTER_STATE;

import android.widget.BaseAdapter;
import android.widget.ListView;
import java.util.ArrayList;

import com.weema.sipsample.util.TongXunJiLu;
import android.widget.ImageButton;
import java.util.HashMap;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.ImageView;

import android.view.View.OnLongClickListener;

import android.util.Log;
import android.content.DialogInterface;
import com.weema.sipsample.util.SettingConfig;
import android.app.AlertDialog;
import com.weema.sipsample.util.JsonUtil;

import android.widget.Toast;
import com.weema.sipsample.service.WaService;

import 	android.os.Environment;
import java.io.File;

import android.media.MediaPlayer;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.text.TextUtils ;
import com.weema.sipsample.util.Ring;
import java.util.Arrays;

public class ConsoleActivity extends BaseFragment implements AdapterView.OnItemSelectedListener, View.OnClickListener,PortMessageReceiver.BroadcastListener{

  private static final String TAG="ConsoleActivity";
  private static final int DOOR_ACTION = 1;
  private static final int REGISTER_CHANGE_ACTION=2;
  private static final int FWD_ACTION=3;  
  private static final int PRESSKEY_ACTION=4;  
  private static final int MAIL_ACTION=5;
  private ImageView m_door_close_info;
  private ImageView m_door_open_info;

  private MainActivity mainActivity;

	private MyAdapter myAdapter = null;
  private ListView txl_view;

	private EditText mReception=null;
  private ImageButton button_wdr;
	  
	private ImageButton button_zj;
	private ImageButton m_button_sm;

  private TextView m_phoneTextView;
  private TextView m_phoneTextView2;
  private TextView m_phoneTextView3;
	private TextView m_textView;
  private String m_call_info;
  private ArrayList<TongXunJiLu> list = new ArrayList<TongXunJiLu>();
  private View rootView;

  private boolean m_is_clear;
  private int m_call_timer=0;
  private int m_call_index=0;
  private int m_miss;
  private int m_incoming;
  private static final int MAX_MAIL_TIME=30;
  private int mail_timer;

	private MyApplication myApp;

	private int m_MAX_LOG=100;
    
  public boolean m_is_active;
    
  private int zhuangtai;
  private ImageButton m_button_jf;

  private static final int MAX_KEY_TIME=1;
  private int m_key_timer;

  private boolean is_called;
  private boolean m_is_video;
  private String m_str_redial;
	private static MediaPlayer mp_0, mp_1, mp_2, mp_3, mp_4, mp_5, mp_6, mp_7,
			mp_8, mp_9, mp_xing, mp_jing, mp_tongxinlu, mp_wurao, mp_zhuanjie,
			mp_neixian, mp_juxian, mp_shequ, mp_guanlishi,
			mp_kaimen, mp_jiefei, mp_sheding;

  @Nullable
  @Override
  public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
    mainActivity = (MainActivity) getActivity();
        
    myApp = (MyApplication) mainActivity.getApplicationContext();
   
    View view = inflater.inflate(R.layout.console, container, false);
    rootView = view;
        
    m_is_active = false;
    zhuangtai = 0;
    return view;
  }

	private void initMS() {

		if (mp_sheding == null) {

			//progressDialog = ProgressDialog.show(this, "", "隤���葉...");
			new Thread() {

				@Override
				public void run() {
					mp_0 = MediaPlayer.create(mainActivity, R.raw.m_0);
					mp_1 = MediaPlayer.create(mainActivity, R.raw.m_1);
					mp_2 = MediaPlayer.create(mainActivity, R.raw.m_2);
					mp_3 = MediaPlayer.create(mainActivity, R.raw.m_3);
					mp_4 = MediaPlayer.create(mainActivity, R.raw.m_4);
					mp_5 = MediaPlayer.create(mainActivity, R.raw.m_5);
					mp_6 = MediaPlayer.create(mainActivity, R.raw.m_6);
					mp_7 = MediaPlayer.create(mainActivity, R.raw.m_7);
					mp_8 = MediaPlayer.create(mainActivity, R.raw.m_8);
					mp_9 = MediaPlayer.create(mainActivity, R.raw.m_9);
					mp_xing = MediaPlayer.create(mainActivity, R.raw.m_xing);
					mp_jing = MediaPlayer.create(mainActivity, R.raw.m_jing);
					mp_tongxinlu = MediaPlayer.create(mainActivity, R.raw.m_txl);
					mp_wurao = MediaPlayer.create(mainActivity, R.raw.m_wurao);
					mp_zhuanjie = MediaPlayer.create(mainActivity, R.raw.m_zhuanjie);
					//mp_shuoming = MediaPlayer.create(ConsoleActivity.this, R.raw.m_shuoming);
					mp_neixian = MediaPlayer.create(mainActivity, R.raw.m_neixian);
					mp_juxian = MediaPlayer.create(mainActivity, R.raw.m_juxian);
					mp_shequ = MediaPlayer.create(mainActivity, R.raw.m_shequ);
					mp_guanlishi = MediaPlayer.create(mainActivity, R.raw.m_guanlishi);
					mp_kaimen = MediaPlayer.create(mainActivity, R.raw.m_kaimen);
					mp_jiefei = MediaPlayer.create(mainActivity, R.raw.m_jiefei);
					mp_sheding = MediaPlayer.create(mainActivity, R.raw.m_sheding);
/*
					if (progressDialog != null) {
						progressDialog.cancel();
						progressDialog.dismiss();
          }
 */         
				}
			}.start();

		}
	}

  private void show_lock()
  {
    boolean isLock = myApp.m_is_lock;

    if (isLock == false)
      m_button_sm.setBackgroundResource(R.drawable.sm_2);
    else
      m_button_sm.setBackgroundResource(R.drawable.sm_1);

  }
  private void show_log()
  {
  	m_miss = 0;
  	m_incoming = 0;

  	list.clear();

  	String str = SettingConfig.getRecord(mainActivity);
    if(!TextUtils.isEmpty(str)){
  	//if (str != null && !str.equals("")) {
  		//Toast.makeText(ConsoleActivity.this, str, Toast.LENGTH_LONG).show();

  		String[] arrStr = str.split("=");

  		int len = arrStr.length;
  		if(len > m_MAX_LOG)
  		{
  			len = m_MAX_LOG;
  		}

  		if(arrStr.length > m_MAX_LOG)
  		{
  			String str1=null;

  				for (int i = 0; i < len ; i++) {
  					if(arrStr[i] != null && arrStr[i].length() > 3)
  					{
              if(str1 == null)
                str1 = arrStr[i];
              else
  						  str1 = str1 + "=" + arrStr[i];

  					}
  				}

  				SettingConfig.setRecord(mainActivity,str1);

  		}

  		for (int i = 0; i < len ; i++) {
  			if(arrStr[i] != null && arrStr[i].length() > 3)
  			{
  				TongXunJiLu my_obj = (TongXunJiLu) JsonUtil.fromJSon(arrStr[i],"TongXunJiLu");
          if(my_obj == null)
  					continue;

  				if (my_obj.isJieTong()) {// 已接
  					m_incoming++;
  				}
  				else
  				{
  					m_miss++;
  				}

  				list.add(my_obj);
  			}
  		}

  	}

    m_call_info = String.format("%d/%d ",m_miss,m_incoming);
  	m_textView.setText(m_call_info);

  	myAdapter.notifyDataSetChanged();

  }

  @Override
  public void onViewCreated(View view, Bundle savedInstanceState) {
    super.onViewCreated(view, savedInstanceState);

    mainActivity = (MainActivity)getActivity();

    initView(view);

    myAdapter = new MyAdapter();
    txl_view.setAdapter(myAdapter);

    initOnClickListener();
    initOnLongClickListener();

    onHiddenChanged(false);

  }

  @Override
  public void onHiddenChanged(boolean hidden) {
    super.onHiddenChanged(hidden);

    m_is_active = !hidden;

    if (!hidden){
      m_is_video = false;
      //is_extern_file_exist();
      if (myApp.isDaoLan) {
        initMS();
      }
      is_called = false;  
      myApp.m_is_monitor_call = false;
      m_key_timer = 0;
      if(myApp.check_alarm())
      {
        String number = myApp.getNextSOSCall();
        if(number == null)
        {
          mainActivity.showTips("outgoing call end");

        }
        else
        {
          long ret = myApp.outgoingCall(true,number);
          if(ret > 0)
          {
            Ring.getInstance(getActivity()).startRingBackTone();
            mainActivity.loadVideoFragment();
            return;
          }
        }
      }
      mainActivity.receiver.broadcastReceiver = this;
      
      show_log();

      show_fwd();
    
      show_mdoor_state();
            
      mainActivity.delay_record();
      showPhoneNumber();

    }
    else
    {
      mReception.setText("");
      mainActivity.stop_record();
          
    }

    m_is_clear = false;

  }

@Override
public void onDestroyView() {
  super.onDestroyView();

}

public void timeout()
{
  if(m_is_active == false)    return;
  
  if(m_key_timer > 0)
    --m_key_timer;

  if(m_call_timer > 0)
    --m_call_timer;   

  process_mail();   
}

private void process_mail()
{
    if(mail_timer > 0 && !TextUtils.isEmpty(myApp.mail_content))
    {
        --mail_timer;
        if(mail_timer == 0)
        {
          postMessage(MAIL_ACTION);
      
        }
    }
}    
private void initView(View view) {
  mReception = (EditText) view.findViewById(R.id.EditTextReception);

  m_button_jf = (ImageButton) view.findViewById(R.id.dialButton_jf);

  button_wdr = (ImageButton) view.findViewById(R.id.dialButton_wdr);

  boolean isDND = myApp.getDND();
  if (isDND) {
  	button_wdr.setBackgroundResource(R.drawable.wdr_on);

  } else {
  	button_wdr.setBackgroundResource(R.drawable.wdr);
  }

  button_zj = (ImageButton) view.findViewById(R.id.dialButton_zj);

  m_button_sm = (ImageButton) view.findViewById(R.id.dialButton_sm);

  m_phoneTextView = (TextView) view.findViewById(R.id.phoneTextView);
  m_phoneTextView2 = (TextView) view.findViewById(R.id.phoneTextView2);
  m_phoneTextView3 = (TextView) view.findViewById(R.id.phoneTextView3);

  m_door_close_info = (ImageView) view.findViewById(R.id.door_close_info);
  m_door_open_info = (ImageView) view.findViewById(R.id.door_open_info);

  m_textView = (TextView) view.findViewById(R.id.call_info);
  m_textView.setOnClickListener(viewDoListener);
  m_call_info = "0/0 ";
  m_textView.setText(m_call_info);

  txl_view = (ListView) view.findViewById(R.id.txjllistView);
  txl_view.setOnItemClickListener(new OnItemClickListener() {

  @Override
  public void onItemClick(AdapterView<?> arg0, View arg1, int arg2,
  	long arg3) {

    String num = list.get(arg2).getPhonenum();

  	if(m_call_timer > 0)
  	{
  		if(m_call_index == arg2)
      {
  			boolean isVideo = list.get(arg2).getVideo();

  			outgoingCall(isVideo,num);

  			mReception.setText("");
        m_call_timer = 0;
      }
      else
      {
  			m_call_index = arg2;
  			m_call_timer = 2;
      }

  	}
  	else
  	{
  		m_call_index = arg2;
  		m_call_timer = 2;
  		myApp.cancel_notify();

  	}
	}
  });

  m_miss = 0;
  m_incoming = 0;

  myAdapter = new MyAdapter();
  txl_view.setAdapter(myAdapter);

}
  
private void showFileList()
{
  String path = Environment.getExternalStorageDirectory().toString()+"/Download";
  Log.d("Files", "Path: " + path);
  File directory = new File(path);
  File[] files = directory.listFiles();
  Log.d("Files", "Size: "+ files.length);
  for (int i = 0; i < files.length; i++)
  {
    Log.d("Files", "FileName:" + files[i].getName());
  }
}
public void update_lock()
{
  if(m_is_active != false)
  {
    show_lock();
  }
}

private void show_mdoor_state()
{
  switch(myApp.m_door_state)
  {
    case WaService.CLOSE_STATE:
      m_door_close_info.setVisibility(View.VISIBLE);
      m_door_open_info.setVisibility(View.GONE);

      break;
    case WaService.OPEN_STATE:
      m_door_close_info.setVisibility(View.GONE);
      m_door_open_info.setVisibility(View.VISIBLE);
      break;
    default:
      m_door_close_info.setVisibility(View.GONE);
      m_door_open_info.setVisibility(View.GONE);
      break;
  }

  if(myApp.m_mdoor_enable == false)
  {
    m_door_close_info.setVisibility(View.GONE);
    m_door_open_info.setVisibility(View.GONE);
  }

}

  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<ConsoleActivity> mActivity;
    public StaticHandler(ConsoleActivity activity)
    {
      mActivity = new WeakReference<ConsoleActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      ConsoleActivity activity = mActivity.get();
      if(activity == null) return;

      switch(msg.what)
      {
        case DOOR_ACTION:
        activity.show_mdoor_state();

        break;
        case REGISTER_CHANGE_ACTION:
        activity.showPhoneNumber();
        break;
        case FWD_ACTION:
        activity.show_fwd();
        break;     
        case PRESSKEY_ACTION:
        
        int key = msg.getData().getInt("key");
        //activity.mainActivity.showTips("PRESSKEY_ACTION "+String.valueOf(key));
        activity.doPress(key);
        break;  
        case MAIL_ACTION:
        activity.show_mail();
        break;                              
      }

    }
  }
private void postMessage(int id,int key)
{
  Message message=mHandler.obtainMessage();
  
  Bundle data = new Bundle();
  
  data.putInt("key", key);
  message.setData(data);

  message.what = id;
  mHandler.sendMessage(message);
}
private void postMessage(int id)
{
  Message message=new Message();
  message.what = id;
  mHandler.sendMessage(message);
}
public int OnBroadcastReceiver(Intent intent) {
  int ret = -1;
  String action = intent == null ? "" : intent.getAction();
  
  if (MyApplication.FWD_CHANGE_ACTION.equals(action)) 
  {
    postMessage(FWD_ACTION);
    ret = 0;
  }     
  else if (MyApplication.PRESSKEY_ACTION.equals(action)) 
  {
    int key = intent.getIntExtra(MyApplication.EXTRA_KEY,-1);
    //if(key >= 0)
      postMessage(PRESSKEY_ACTION,key);
    ret = 0;
  }     
  else if(m_is_active == false)    return ret;

  else if (MyApplication.DOOR_ACTION.equals(action)) {
    postMessage(DOOR_ACTION);
    ret = 0;
  }
  else if (PortSipService.REGISTER_CHANGE_ACTION.equals(action)) {
    //String tips  =intent.getStringExtra(EXTRA_REGISTER_STATE);

    postMessage(REGISTER_CHANGE_ACTION);
    //setOnlineStatus(tips);
  } else if (PortSipService.CALL_CHANGE_ACTION.equals(action)) {
    //long sessionId = intent.GetLongExtra(PortSipService.EXTRA_CALL_SEESIONID, Session.INVALID_SESSION_ID);
    //callStatusChanged(sessionId);
    ret = -1;
  }
  else if (PortSipService.AUTO_ONHOOK_ACTION.equals(action)) {
    //String tips  =intent.getStringExtra(EXTRA_REGISTER_STATE);

    postMessage(REGISTER_CHANGE_ACTION);
    ret = 0;
  } 
  return ret;
}

  private void setOnlineStatus(String tips) {

    String line1=myApp.getPhoneNumber(1,true);
    String line2=myApp.getPhoneNumber(2,true);
    String line3=myApp.getPhoneNumber(3,true);

    if(!TextUtils.isEmpty(line1))
    {
      line1 = "line 1 "+line1;
    }
    
    if(!TextUtils.isEmpty(line2))
    {
      line2 = "line 2 "+line2;
    }

    if(!TextUtils.isEmpty(line3))
    {
      line3 = "line 3 "+line3;
    }

    m_phoneTextView.setText(line1);
    m_phoneTextView2.setText(line2);
    m_phoneTextView3.setText(line3);

  }

    @Override
    public void onClick(View view) {
     
    }

    @Override
    public void onItemSelected(AdapterView<?> adapterView, View view, int position, long l) {
      
    }

    @Override
    public void onNothingSelected(AdapterView<?> adapterView) {

    }
    private void do_lock()
    {
      if(MyApplication.m_is_test == false)
      {
        boolean isLock = myApp.m_is_lock;

        if (isLock != false) {
            mainActivity.loadUnLockActivity();
        } else {
            boolean ret = myApp.setLock(mainActivity,true);
            if(ret != false)
            {
                if(myApp.m_is_lock)
                {
                  m_button_sm.setBackgroundResource(R.drawable.sm_1);
                  myApp.send_update();    
                }
                else 
                {
                  mainActivity.showTips(R.string.str_waiting);
                }
            }
            else 
                mainActivity.showTips(myApp.m_message);
           
        }
        
      }
      else
      {
        //myApp.door_ringAction();
        //myApp.dataReceiver(5);
        if(myApp.m_door_state == 0)
          myApp.m_door_state = 1;
        else
          myApp.m_door_state = 0;
        myApp.updateMDoor(myApp.m_door_state);
      }

    }
    private void do_redial()
    {
      
      outgoingCall_ext(true,m_str_redial);
    }
    private void show_mail()
    {
      mainActivity.loadMailActivity();
    }
    private void doPress(int key)
    {
      if(mReception == null )    return;
      mainActivity.delay_record();
      if(m_is_clear != false)
      {
          m_is_clear = false;
          mReception.setText("");

      }

     String number=null;
      String str;
      if(m_key_timer > 0)    return;
      m_key_timer = MAX_KEY_TIME;
      switch(key)
      {
      case MyApplication.KEY_NEXT:
      mReception.setText("");
      break;        
      case MyApplication.KEY_SPK:
      m_is_video = true;
      break;
      case MyApplication.KEY_REDIAL:
        do_redial();
        
      break;
      case R.id.dialButton_video:

      number = mReception.getText().toString();
      outgoingCall_ext(true,number);

      break;
      case R.id.dialButton_jf:
         if (zhuangtai == 0) {//

          if (myApp.isDaoLan) {
            mp_shequ.start();
          }
      
              m_button_jf.setBackgroundResource(R.drawable.jf);
              zhuangtai = 1;
         } else if (zhuangtai == 1) {//
            if (myApp.isDaoLan) {
              mp_jiefei.start();
            }    
            m_button_jf.setBackgroundResource(R.drawable.sq);
            zhuangtai = 0;
         }

      break;
      case R.id.dialButton_sd:
        if (myApp.isDaoLan) {
          mp_sheding.start();
        }
         mainActivity.loadMySettingFragment();

      break;
      case R.id.dialButton_sm:
        do_lock();
      
      break;
      case R.id.dialButton_zj:
      
      if (myApp.isDaoLan) {
        mp_zhuanjie.start();
      }          
      toogleFWD();

      break;
      case R.id.dialButton_txl:

      if (myApp.isDaoLan) {
        mp_tongxinlu.start();
      }

      if(MyApplication.m_is_test_open_door == false)
      {
        mainActivity.loadTxlActivity();

      }
      else
      {
        mainActivity.showTips("open door");
        myApp.i2c_open_door();
      }
      break;
      case R.id.dialButton_wdr:
      if (myApp.isDaoLan) {
        mp_wurao.start();
      }
      boolean isDND = myApp.getDND();
      if (isDND == false) {
        isDND = true;
        button_wdr.setBackgroundResource(R.drawable.wdr_on);

      } else {
        isDND = false;
        button_wdr.setBackgroundResource(R.drawable.wdr);
      }

      myApp.setDND(isDND);

      break;
      case R.id.dialButton_gls:
        if (myApp.isDaoLan) {
          mp_guanlishi.start();
        }
        outgoingCall(false,myApp.m_mgr_number);
      break;
      case R.id.dialButton_back:
          number = mReception.getText().toString();

          if (number != null && number.length() > 0) {
              number = number.substring(0, number.length() - 1);
              mReception.setText(number);
          }
     break;
     case R.id.dialButton_audio:
          if(myApp.m_monitor_enable)
          {
              myApp.m_is_monitor_call = true;
              outgoingCall_ext(true,myApp.m_monitor_number);
          }
     break;
     case MyApplication.KEY_P:
      if (myApp.isDaoLan) {
        mp_jing.start();
      }
         number = mReception.getText().toString();
         outgoingCall_ext(m_is_video,number);

     break;
     case MyApplication.KEY_S:
      if (myApp.isDaoLan) {
        mp_xing.start();
      }        
         m_key_timer = 0;
         str = "*";
         mReception.append(str);
     break;
     case MyApplication.KEY_0:
        if (myApp.isDaoLan) {
          mp_0.start();
        }         
        m_key_timer = 0;
        str = "0";
        mReception.append(str);

    break;
    case MyApplication.KEY_9:
      if (myApp.isDaoLan) {
        mp_9.start();
      }        
        m_key_timer = 0;
        str = "9";
        mReception.append(str);

    break;
    case MyApplication.KEY_8:
      if (myApp.isDaoLan) {
        mp_8.start();
      }        
        m_key_timer = 0;
        str = "8";
        mReception.append(str);

    break;
    case MyApplication.KEY_7:
    if (myApp.isDaoLan) {
      mp_7.start();
    }        
      m_key_timer = 0;
      str = "7";
      mReception.append(str);
      break;
  case MyApplication.KEY_6:
    if (myApp.isDaoLan) {
      mp_6.start();
    }
      m_key_timer = 0;
      str = "6";
      mReception.append(str);
      break;
  case MyApplication.KEY_5:
      if (myApp.isDaoLan) {
        mp_5.start();
      }
      m_key_timer = 0;
      str = "5";
      mReception.append(str);
      break;
  case MyApplication.KEY_4:
      if (myApp.isDaoLan) {
        mp_4.start();
      }      
      m_key_timer = 0;
      str = "4";
      mReception.append(str);
      break;
  case MyApplication.KEY_3:
      if (myApp.isDaoLan) {
        mp_3.start();
      }      
      m_key_timer = 0;
      str = "3";
      mReception.append(str);
      break;
  case MyApplication.KEY_2:
      if (myApp.isDaoLan) {
        mp_2.start();
      }     
      m_key_timer = 0;
      str = "2";
      mReception.append(str);
      break;
  case MyApplication.KEY_1:
      if (myApp.isDaoLan) {
        mp_1.start();
      }      
      m_key_timer = 0;
      str = "1";
      mReception.append(str);
      break;

  }      
    }
    private ImageButton.OnClickListener btnDoListener=new ImageButton.OnClickListener(){
      @Override
	    public void onClick(View v)
      {
        //myApp.isDaoLan = false;
        //if(true)    return;

        int key = myApp.get_map_value(v.getId());
        doPress(key);

	    }
	};

    class MyAdapter extends BaseAdapter {

  		HashMap<Integer, View> map = new HashMap<Integer, View>();

  		@Override
  		public int getCount() {

  			return list.size();
  		}

  		@Override
  		public Object getItem(int position) {

  			return list.get(position);
  		}

  		@Override
  		public long getItemId(int position) {

  			return position;
  		}

  		@Override
  		public boolean isEnabled(int position) {

  			return super.isEnabled(position);
  		}

  		@Override
  		public View getView(int position, View convertView, ViewGroup parent) {

  			ViewHolder holder = null;
  			View view1 = convertView;

  			if (map.get(position) == null) {
  				view1 = LayoutInflater.from(mainActivity.getApplicationContext()).inflate(
  						R.layout.txjl_item, null);
  				holder = new ViewHolder();

  				holder.iv = (ImageView) view1.findViewById(R.id.zhuangtaiIMG);
  				holder.num = (TextView) view1.findViewById(R.id.haoma);
  				holder.name = (TextView) view1.findViewById(R.id.name);
  				holder.riqi = (TextView) view1.findViewById(R.id.riqi);
  				holder.shijian = (TextView) view1.findViewById(R.id.shijian);
  				view1.setTag(holder);
  				map.put(position, view1);

  			} else {
  				view1 = map.get(position);
  				holder = (ViewHolder) view1.getTag();
  			}

  			TongXunJiLu txl = list.get(position);


        if (txl.getOutgoing()) {// 已接
          //m_incoming ++;
          holder.iv.setImageResource(R.drawable.outgoing);
        } 
  			else if (txl.isJieTong()) {// 已接
  			        //m_incoming ++;
  				holder.iv.setImageResource(R.drawable.answer);
  			} else {// 未接
  			        //m_miss++;
  				holder.iv.setImageResource(R.drawable.answer_no);
  			}

  			holder.num.setText(txl.getPhonenum());
  			holder.name.setText(txl.getName());
  			holder.riqi.setText(txl.getRiqi());
  			holder.shijian.setText(txl.getShijian());

  			return view1;
  		}
  	}

private void initOnClickListener() {

  int my_ids[] = {
    R.id.dialButton_video,R.id.dialButton_jf,R.id.dialButton_sd,
    R.id.dialButton_sm,R.id.dialButton_zj,R.id.dialButton_txl,
    R.id.dialButton_wdr,R.id.dialButton_gls,R.id.dialButton_audio,
    R.id.dialButton_back,R.id.dialButton_jing,R.id.dialButton_xing,
    R.id.dialButton_0,R.id.dialButton_9,R.id.dialButton_8,
    R.id.dialButton_7,R.id.dialButton_6,R.id.dialButton_5,
    R.id.dialButton_4,R.id.dialButton_3,R.id.dialButton_2,
    R.id.dialButton_1,
  };

  ImageButton b = null;

  for( int i=0 ; i< my_ids.length ; ++i )
    if( ( b = (ImageButton)rootView.findViewById( my_ids[i]) ) != null )
      b.setOnClickListener(btnDoListener);
}

private void initOnLongClickListener() {
  button_zj.setOnLongClickListener(new OnLongClickListener() {

  @Override
  public boolean onLongClick(View v) {
  	// TODO Auto-generated method stub

  	Log.i(TAG,"LoadTranSetActivity");

  	mainActivity.LoadTranSetActivity();

  		return true;
  }
  });

}
 
private void show_fwd()
{
  boolean value = SettingConfig.getEnableFWD(mainActivity);

  if (value == false) {
    button_zj.setBackgroundResource(R.drawable.zj);

  }else {
    select_tran();

  }

}


private void toogleFWD()
{
  boolean value = SettingConfig.getEnableFWD(mainActivity);

  if (value != false) {
    button_zj.setBackgroundResource(R.drawable.zj);
    SettingConfig.setEnableFWD(mainActivity,false);
  }else {
    select_tran();
    SettingConfig.setEnableFWD(mainActivity,true);

  }

  myApp.getConfigFWD();

}

private void select_tran()
{
  String data = SettingConfig.getFWD(mainActivity);

  if (data != null && !data.equals("")) {

    String[] arr_Str = data.split("-");

    Boolean ck_A,ck_B;

    ck_A = Boolean.parseBoolean(arr_Str[0]);
    ck_B = Boolean.parseBoolean(arr_Str[4]);

    if(ck_A)
    {
      button_zj.setBackgroundResource(R.drawable.all_fwd);
    }
    else if(ck_B)
    {
      button_zj.setBackgroundResource(R.drawable.busy_fwd);
    }
    else
    {
      button_zj.setBackgroundResource(R.drawable.noanswer_fwd);
    }
  }
  else
  {
    button_zj.setBackgroundResource(R.drawable.all_fwd);
  }

}

private void outgoingCall_ext(boolean isVideo,String callTo) {
  String number;

  if(TextUtils.isEmpty(callTo))  return;

  if(zhuangtai == 0)
  {
    number = callTo;
  }
  else
  {
    number = "2*"+callTo;
  }

  outgoingCall(isVideo,number);
}

private void outgoingCall(boolean isVideo,String callTo) {
      
  if(is_called == true)    return;

  is_called = true;
  long ret = myApp.outgoingCall(isVideo,callTo);
  if(ret > 0)
  {
    m_str_redial = callTo;
    Ring.getInstance(getActivity()).startRingBackTone();

      mainActivity.loadVideoFragment();
  }
  is_called = false;    
}

private View.OnClickListener viewDoListener=new View.OnClickListener(){
	@Override
  public void onClick(View v)
	{
		switch(v.getId())
		{
		  case R.id.call_info:
	      show_dialog();
	      break;
		}
	}
};

class ViewHolder {

		ImageView iv;
		TextView num;
		TextView name;
		TextView riqi;
		TextView shijian;
	}

  private void show_dialog()
	{
      if(m_miss == 0 && m_incoming == 0)
		      return;

			new AlertDialog.Builder(mainActivity)
					.setTitle(getResources().getString(R.string.str_confirm_window))
					.setIcon(R.drawable.ic_launcher)
					.setMessage(getResources().getString(R.string.str_clear_log))
		      .setPositiveButton(getResources().getString(R.string.str_ok), new DialogInterface.OnClickListener() {
				public void onClick(DialogInterface dialoginterface, int i)
				{
						list.clear();
						SettingConfig.setRecord(mainActivity,"");

						m_miss = 0;
						m_incoming = 0;

            m_call_info = "0/0 ";
						m_textView.setText(m_call_info);
						myAdapter.notifyDataSetChanged();

				}
				})
				.setNegativeButton(getResources().getString(R.string.str_cancel), new DialogInterface.OnClickListener() {
					public void onClick(DialogInterface dialoginterface, int i)
					{

					}
				})
				.show();


	}

  private void showPhoneNumber()
	{
    setOnlineStatus("");
    show_lock();
	
	}

}
