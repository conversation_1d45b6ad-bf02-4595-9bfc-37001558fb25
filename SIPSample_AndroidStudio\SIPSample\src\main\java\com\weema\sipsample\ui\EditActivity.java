package com.weema.sipsample.ui;

import java.util.ArrayList;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.weema.R;
import android.widget.Button;
import android.widget.EditText;


import android.app.Fragment;
import android.content.Context;


import com.weema.sipsample.util.CharacterParser;
import com.weema.sipsample.util.SettingConfig;
import com.weema.sipsample.util.JsonUtil;
//import com.alibaba.fastjson.JSON;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import com.weema.sipsample.util.TongXunLu;
import com.weema.sipsample.util.JsonUtil;

public class EditActivity extends Fragment {

	public static ArrayList<TongXunLu> txl_list = new ArrayList<TongXunLu>();

	public static TongXunLu Curr_TongXunLu = null;

	private EditText ed_1, ed_2, ed_3;
  private Context mContext;

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {

		super.onCreateView(inflater, container, savedInstanceState);

		mContext = getActivity();
		View	myView = inflater.inflate(R.layout.edit_txl, container, false);

		Button editBT = (Button) myView.findViewById(R.id.yes);
		editBT.setOnClickListener(new View.OnClickListener() {

			@Override
			public void onClick(View v) {
				savaData();
				if (Curr_TongXunLu == null) {
					//startActivity(new Intent(EditActivity.this, TxlActivity.class));
				}
				((MainActivity)mContext).loadTxlActivity();
				//finish();

			}
		});
		Button no = (Button) myView.findViewById(R.id.no);
		no.setOnClickListener(new View.OnClickListener() {

			@Override
			public void onClick(View v) {
				//finish();
				((MainActivity)mContext).loadTxlActivity();

			}
		});

		ed_1 = (EditText) myView.findViewById(R.id.edit_1);
		ed_2 = (EditText) myView.findViewById(R.id.edit_2);
		ed_3 = (EditText) myView.findViewById(R.id.edit_3);

		if (Curr_TongXunLu != null) {
			ed_1.setText(Curr_TongXunLu.getXingming());
			ed_2.setText(Curr_TongXunLu.getHaoma());
			ed_3.setText(Curr_TongXunLu.getBeizhu());
		}

    return myView;
	}

	private void savaData() {

		if (Curr_TongXunLu != null) {
			Curr_TongXunLu.setXingming(ed_1.getText().toString().trim());
			Curr_TongXunLu.setHaoma(ed_2.getText().toString().trim());
			Curr_TongXunLu.setBeizhu(ed_3.getText().toString().trim());
		} else {
			TongXunLu t = new TongXunLu();
			t.setXingming(ed_1.getText().toString().trim());
			t.setHaoma(ed_2.getText().toString().trim());
			t.setBeizhu(ed_3.getText().toString().trim());

			   // 汉字转换成拼音
            if (t.getXingming() == null && t.getXingming().equals("")) {
                t.setXingming("#" + t.getHaoma());
            }
            String pinyin = CharacterParser.getInstance().getSelling(t.getXingming());
            String sortString = pinyin.substring(0, 1).toUpperCase();

            // 正则表达式，判断首字母是否是英文字母
            if (sortString.matches("[A-Z]")) {
                t.setSortLetters(sortString.toUpperCase());
            } else {
                t.setSortLetters("#");
            }

			txl_list.add(t);
		}

		StringBuffer sb = new StringBuffer();
		int size = txl_list.size();

		for (int i = 0; i < size; i++) {
			String data = JsonUtil.toJSon(txl_list.get(i));
			sb.append(data);
			if (i != (size - 1)) {
				sb.append("=");
			}
		}

    SettingConfig.setContacts(mContext,sb.toString());
		//Data.writePreferences(this, "txl", sb.toString());

	}

}
