package com.weema.sipsample.ui;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;

import android.app.Activity;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;
import android.view.KeyEvent;
import android.view.LayoutInflater;

import android.view.View;
import android.view.ViewGroup;

import android.widget.TextView;
import android.widget.Toast;

//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.app.Fragment;
import android.content.Intent;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import com.weema.R;
import com.weema.sipsample.service.PortSipService;
import com.weema.sipsample.service.PortSipService2;
import com.weema.sipsample.service.PortSipService3;
import com.weema.sipsample.receiver.PortMessageReceiver;
import com.weema.sipsample.util.CallManager;


import android.widget.CheckBox;
import android.widget.EditText;

import android.util.Log;

//import android.widget.AdapterView.OnItemSelectedListener;
import android.view.View.OnClickListener;
import android.widget.ImageButton;

public class LoginActivity extends Fragment implements OnClickListener,PortMessageReceiver.BroadcastListener{

    private static final String TAG="LoginActivity";

    private BaseMainActivity mainActivity;
    private CheckBox check_enable;
    private EditText editText_name;
    private EditText editText_passwd;
    private EditText editText_ip;
    private EditText editText_port;
    private EditText editText_domain;
    private EditText editText_port_outbound;

    private CheckBox check_enable2;
    private EditText editText_name2;
    private EditText editText_passwd2;
    private EditText editText_ip2;
    private EditText editText_port2;
    private EditText editText_domain2;
    private EditText editText_port_outbound2;

    private CheckBox check_enable3;
    private EditText editText_name3;
    private EditText editText_passwd3;
    private EditText editText_ip3;
    private EditText editText_port3;
    private EditText editText_domain3;
    private EditText editText_port_outbound3;

    private ImageButton mbtnReg;
    private ImageButton mbtnReg2;
    private ImageButton mbtnReg3;
 
    private static int m_start_timer = 0;

    private int m_need_register;
    private final int MAX_REGISTER_TIME = 20;

    private static int m_register_line;

    private int m_back_timer = 0;
    private final int MAX_BACK_TIME = 60;

    private boolean m_is_active;
   @Override
   public void onCreate (Bundle savedInstanceState)
   {
       super.onCreate(savedInstanceState);

   }
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
          super.onCreateView(inflater, container, savedInstanceState);
          mainActivity = (BaseMainActivity) getActivity();
          
          View rootView = inflater.inflate(R.layout.activity_login, null);

         return rootView;
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
      super.onViewCreated(view, savedInstanceState);

      initView(view);
      

      Log.i(TAG,"OnCreate");

      m_need_register = 0;
      m_start_timer = 0;
      m_back_timer = MAX_BACK_TIME;

      onHiddenChanged(false);

    }

   private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    } 

    public final StaticHandler mHandler = new StaticHandler(this);
    private  static class StaticHandler extends Handler{
    private final WeakReference<LoginActivity> mActivity;
    public StaticHandler(LoginActivity activity)
    {
      mActivity = new WeakReference<LoginActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      LoginActivity activity = mActivity.get();
      if(activity == null) return;

	    switch(msg.what)
	    {
	      case MyApplication.LOGIN_FAIL:
        Log.i(TAG,"login fail");
        Toast.makeText(activity.mainActivity, "login fail", Toast.LENGTH_SHORT).show();

		    break;
        case MyApplication.START_REGISTER:
        activity.do_register(m_register_line);
		    break;
        case MyApplication.GOTO_BACK:
        m_start_timer = 0;
        activity.mainActivity.loadConsoleActivity();
	      break;
        case MyApplication.LOGIN_OK:
        activity.mainActivity.loadMySettingFragment();

        Toast.makeText(activity.mainActivity, "login ok", Toast.LENGTH_SHORT).show();       

	      break;        
        default:
        Toast.makeText(activity.mainActivity, "error", Toast.LENGTH_LONG).show();

        break;

 	    }

    }
  }      


   public void timeout() {
    // TODO Auto-generated method stub

    if(m_is_active == false)    return;

    Log.i(TAG,"timertask");

    if(m_back_timer > 0)
    {
      m_back_timer--;
      if(m_back_timer == 0)
      {
        postMessage(MyApplication.GOTO_BACK);
            
      }
    }

    if(m_need_register > 0)
    {
      m_need_register--;
      if(m_need_register == 0)
      {
        postMessage(MyApplication.START_REGISTER);
          
      }
    }
    else
    {
      m_need_register = 0;
    }
    if(m_start_timer > 0)
    {
      --m_start_timer;
    
      if(m_start_timer == 0)
      {
        postMessage(MyApplication.LOGIN_FAIL);
               

      }

    }
  }    
    private void initView(View view) {

      check_enable = (CheckBox)view.findViewById(R.id.login_local);
      editText_name = (EditText)view.findViewById(R.id.editText_login_name);
      editText_passwd = (EditText)view.findViewById(R.id.editText_login_passwd);
      editText_ip = (EditText)view.findViewById(R.id.editText_login_ip);
      editText_port = (EditText)view.findViewById(R.id.editText_login_port);

      editText_domain = (EditText)view.findViewById(R.id.editText_domain);
      editText_port_outbound = (EditText)view.findViewById(R.id.editText_outbound_port);

      check_enable2 = (CheckBox)view.findViewById(R.id.login_save2);
      editText_name2 = (EditText)view.findViewById(R.id.editText_login_name2);
      editText_passwd2 = (EditText)view.findViewById(R.id.editText_login_passwd2);
      editText_ip2 = (EditText)view.findViewById(R.id.editText_login_ip2);
      editText_port2 = (EditText)view.findViewById(R.id.editText_login_port2);

      editText_domain2 = (EditText)view.findViewById(R.id.editText_domain2);
      editText_port_outbound2 = (EditText)view.findViewById(R.id.editText_outbound_port2);

      check_enable3 = (CheckBox)view.findViewById(R.id.login_save3);
      editText_name3 = (EditText)view.findViewById(R.id.editText_login_name3);
      editText_passwd3 = (EditText)view.findViewById(R.id.editText_login_passwd3);
      editText_ip3 = (EditText)view.findViewById(R.id.editText_login_ip3);
      editText_port3 = (EditText)view.findViewById(R.id.editText_login_port3);

      editText_domain3 = (EditText)view.findViewById(R.id.editText_domain3);
      editText_port_outbound3 = (EditText)view.findViewById(R.id.editText_outbound_port3);

      mbtnReg = (ImageButton) view.findViewById(R.id.login_btn);

      mbtnReg.setOnClickListener(this);

      mbtnReg2 = (ImageButton) view.findViewById(R.id.login_btn2);

      mbtnReg2.setOnClickListener(this);

      mbtnReg3 = (ImageButton) view.findViewById(R.id.login_btn3);

      mbtnReg3.setOnClickListener(this);

      ImageButton btn = (ImageButton) view.findViewById(R.id.back_btn);

      btn.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
      switch (v.getId()) {
      case R.id.login_btn:
        online(1);
        mainActivity.do_get_config();
        break;
        case R.id.login_btn2:
          online(2);

        break;
        case R.id.login_btn3:
          online(3);

          break;

        case R.id.back_btn:
        m_start_timer = 0;
        mainActivity.loadMySettingFragment();

          break;
      default:
        break;
      }

    }

    private void loadUserInfo(){

      SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getActivity());

      check_enable.setChecked(preferences.getBoolean(PortSipService.SIP_ENABLE, false));

      editText_name.setText(preferences.getString(PortSipService.USER_NAME, null));

  		editText_passwd.setText(preferences.getString(PortSipService.USER_PWD, null));

  	  editText_ip.setText(preferences.getString(PortSipService.SVR_HOST, getString(R.string.str_default_sip1_server_host)));

      editText_port.setText(preferences.getString(PortSipService.SVR_PORT, getString(R.string.str_default_sip1_server_port)));

      editText_domain.setText(preferences.getString(PortSipService.USER_DOMAIN, getString(R.string.str_default_sip1_user_domain)));
      //editText_port_outbound.setText(preferences.getString(PortSipService.OUTBOUND_PORT, "5060"));

      check_enable2.setChecked(preferences.getBoolean(PortSipService.SIP_ENABLE2, false));

      editText_name2.setText(preferences.getString(PortSipService.USER_NAME2, null));

  		editText_passwd2.setText(preferences.getString(PortSipService.USER_PWD2, null));

  	  editText_ip2.setText(preferences.getString(PortSipService.SVR_HOST2, null));

      editText_port2.setText(preferences.getString(PortSipService.SVR_PORT2, "5060"));

      editText_domain2.setText(preferences.getString(PortSipService.USER_DOMAIN2, null));
      //editText_port_outbound2.setText(preferences.getString(PortSipService.OUTBOUND_PORT2, "5060"));

      check_enable3.setChecked(preferences.getBoolean(PortSipService.SIP_ENABLE3, false));

      editText_name3.setText(preferences.getString(PortSipService.USER_NAME3, null));

  		editText_passwd3.setText(preferences.getString(PortSipService.USER_PWD3, null));

  	  editText_ip3.setText(preferences.getString(PortSipService.SVR_HOST3, null));

      editText_port3.setText(preferences.getString(PortSipService.SVR_PORT3, "5060"));

      editText_domain3.setText(preferences.getString(PortSipService.USER_DOMAIN3, null));
      //editText_port_outbound3.setText(preferences.getString(PortSipService.OUTBOUND_PORT3, "5060"));

  	}
    private void SaveUserInfo() {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(getActivity()).edit();

        editor.putBoolean(PortSipService.SIP_ENABLE, check_enable.isChecked());

        editor.putString(PortSipService.USER_NAME, editText_name.getText().toString());
        editor.putString(PortSipService.USER_PWD, editText_passwd.getText().toString());
        editor.putString(PortSipService.SVR_HOST, editText_ip.getText().toString());
        editor.putString(PortSipService.SVR_PORT, editText_port.getText().toString());

        editor.putString(PortSipService.USER_DOMAIN, editText_domain.getText().toString());
        //editor.putString(PortSipService.OUTBOUND_PORT, editText_port_outbound.getText().toString());

        editor.putBoolean(PortSipService.SIP_ENABLE2, check_enable2.isChecked());

        editor.putString(PortSipService.USER_NAME2, editText_name2.getText().toString());
        editor.putString(PortSipService.USER_PWD2, editText_passwd2.getText().toString());
        editor.putString(PortSipService.SVR_HOST2, editText_ip2.getText().toString());
        editor.putString(PortSipService.SVR_PORT2, editText_port2.getText().toString());

        editor.putString(PortSipService.USER_DOMAIN2, editText_domain2.getText().toString());
        //editor.putString(PortSipService.OUTBOUND_PORT2, editText_port_outbound2.getText().toString());

        editor.putBoolean(PortSipService.SIP_ENABLE3, check_enable3.isChecked());

        editor.putString(PortSipService.USER_NAME3, editText_name3.getText().toString());
        editor.putString(PortSipService.USER_PWD3, editText_passwd3.getText().toString());
        editor.putString(PortSipService.SVR_HOST3, editText_ip3.getText().toString());
        editor.putString(PortSipService.SVR_PORT3, editText_port3.getText().toString());

        editor.putString(PortSipService.USER_DOMAIN3, editText_domain3.getText().toString());
        //editor.putString(PortSipService.OUTBOUND_PORT3, editText_port_outbound3.getText().toString());

        editor.commit();
    }

    private  void do_register(int line)
    {
      Intent onLineIntent;

      if(line == 1)
           onLineIntent = new Intent(mainActivity, PortSipService.class);
      else if(line == 2)
            onLineIntent = new Intent(mainActivity, PortSipService2.class);
      else if(line == 3)
            onLineIntent = new Intent(mainActivity, PortSipService3.class);
      else
            onLineIntent = new Intent(mainActivity, PortSipService.class);

      onLineIntent.setAction(PortSipService.ACTION_SIP_REGIEST);
      mainActivity.startService(onLineIntent);

    }

    private int online(int line) {
      Log.i(TAG,"online "+String.valueOf(line));
      m_back_timer = MAX_BACK_TIME;
      SaveUserInfo();

      Intent onLineIntent;

      if(line == 1)
          onLineIntent = new Intent(getActivity(), PortSipService.class);
      else if(line == 2)
          onLineIntent = new Intent(getActivity(), PortSipService2.class);
      else if(line == 3)
          onLineIntent = new Intent(getActivity(), PortSipService3.class);
      else
          onLineIntent = new Intent(getActivity(), PortSipService.class);

      onLineIntent.setAction(PortSipService.ACTION_SIP_UNREGIEST);
      mainActivity.startService(onLineIntent);

      Boolean ischeck=check_enable.isChecked();

      if(line == 1)
          ischeck = check_enable.isChecked();
      else if(line == 2)
          ischeck = check_enable2.isChecked();
      else if(line == 3)
          ischeck = check_enable3.isChecked();

      if(ischeck == true)
      {
          m_need_register = 1;
          m_start_timer = MAX_REGISTER_TIME;
          Toast.makeText(mainActivity, "wait a moment", Toast.LENGTH_SHORT).show();

      }
      else
      {
        Toast.makeText(mainActivity, "unregister ok", Toast.LENGTH_SHORT).show();
      }

      m_register_line = line;

      return 0;
  	}

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);

        m_start_timer = 0;
        m_is_active = !hidden;

        if (!hidden){
            loadUserInfo();
            mainActivity.receiver.broadcastReceiver = this;
            m_back_timer = MAX_BACK_TIME;
            //setOnlineStatus();
            Log.i(TAG,"onHiddenChanged");
          
        }
        else
        {
          m_back_timer = 0;
        
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        
        Log.i(TAG,"onDestroyView");
    }

    public int OnBroadcastReceiver(Intent intent) {
        String action = intent == null ? "" : intent.getAction();
        Log.i(TAG,"OnBroadcastReceiver");
        int ret;

        ret = -1;
        if (PortSipService.REGISTER_CHANGE_ACTION.equals(action)) {
            int lineId = intent.getIntExtra(PortSipService.EXTRA_REGISTER_LINE,-1);

            ret = 0;
            if(lineId > 0)
                checkState(lineId);
        } else if (PortSipService.CALL_CHANGE_ACTION.equals(action)) {
            //long sessionId = intent.GetLongExtra(PortSipService.EXTRA_CALL_SEESIONID, Session.INVALID_SESSION_ID);
            //callStatusChanged(sessionId);
        }

        return ret;
    }

    private void checkState(int lineid)
    {
      Boolean regist = CallManager.Instance().getregist(lineid);
      if (regist && m_start_timer > 0)
      {
        m_start_timer = 0;
        
        postMessage(MyApplication.LOGIN_OK);

      }
    }
}
