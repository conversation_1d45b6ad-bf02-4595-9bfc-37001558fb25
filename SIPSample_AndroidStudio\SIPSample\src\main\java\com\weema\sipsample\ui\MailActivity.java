package com.weema.sipsample.ui;


import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.content.Context;

import android.os.Bundle;
import android.view.View;

import android.widget.Button;

import android.widget.TextView;
import android.widget.EditText;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;
import android.content.Intent;
import android.text.TextUtils;
import com.weema.sipsample.receiver.PortMessageReceiver;
import android.util.TypedValue;
import com.weema.sipsample.util.Ring;
import com.weema.sipsample.service.PortSipService;
public class MailActivity extends Fragment implements PortMessageReceiver.BroadcastListener{

    MainActivity mainActivity;
    
    private static final String TAG = "MailActivity";
    private static final int MAX_PAGE = 11;
    private static final int PRESSKEY_ACTION=1; 
    private static final int UPDATE_ACTION=2;  
    private static final int REGISTER_CHANGE_ACTION=3;

    private MyApplication myApp;
    
    private TextView titleView;
    private TextView infoView;
    private TextView m_phoneTextView;
    private TextView[] textView;
    private EditText mReception;
    private boolean m_is_active;
    private boolean m_is_video;
    private int m_index;
    private int m_total;
    private int m_key_timer;
    private boolean is_called;
    private String m_str_redial;
    private String m_version;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  		Bundle savedInstanceState) {
        // TODO Auto-generated method stub
        Log.i(TAG,"onCreateView");
        super.onCreateView(inflater, container, savedInstanceState);

        View rootView = inflater.inflate(R.layout.activity_mail, null);
        //initView(rootView);
        mainActivity = (MainActivity) getActivity();
        myApp = (MyApplication) mainActivity.getApplicationContext();

        textView = new TextView[MAX_PAGE];
        return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;

       if (!hidden){

           mainActivity.receiver.broadcastReceiver = this;
           m_index = 0;
           is_called = false;
           m_is_video = false;
           show_mail(m_index);
           Log.i(TAG,"onHiddenChanged");
           showPhoneNumber();
  
           m_version= "APP ("+MyApplication.VERSION+") ";

           m_version = m_version + " "+String.valueOf(myApp.keyRet)+" "+myApp.getLocalIP(false);
   
           infoView.setText(m_version);
   
       }
       else {
          
           
       }
   }

    private void show_mail(int index)
    {
        String content = myApp.mail_content;
        //mainActivity.showTips(content);
        String[] msg_arr = new String[MAX_PAGE];

        m_total = 1;
    
        if(!TextUtils.isEmpty(content))    
        {
            String[] str_arr = content.split("&&");
        
            m_total = str_arr.length/MAX_PAGE+1;
            
            for(int i=index*MAX_PAGE;(i<(index*MAX_PAGE+MAX_PAGE)) && (i<str_arr.length);i++)
            {
                msg_arr[i-index*MAX_PAGE] = str_arr[i];
                
            }
        }

        
        String title = "("+String.valueOf(index+1)+"/"+String.valueOf(m_total)+")";
        titleView.setText(title);
        for(int i=0;i<MAX_PAGE;i++)
        {
            textView[i].setText(msg_arr[i]);
        }

    }

   
   private void initView(View view) {

        titleView = (TextView)view.findViewById(R.id.mail_title);
        infoView = (TextView)view.findViewById(R.id.mail_info);
        m_phoneTextView = (TextView)view.findViewById(R.id.mail_number);
        
        textView[0] = (TextView)view.findViewById(R.id.mail_text1);
        textView[1] = (TextView)view.findViewById(R.id.mail_text2);
        textView[2] = (TextView)view.findViewById(R.id.mail_text3);
        textView[3] = (TextView)view.findViewById(R.id.mail_text4);
 
        textView[4] = (TextView)view.findViewById(R.id.mail_text5);
        textView[5] = (TextView)view.findViewById(R.id.mail_text6);
        textView[6] = (TextView)view.findViewById(R.id.mail_text7);
        textView[7] = (TextView)view.findViewById(R.id.mail_text8);

        textView[8] = (TextView)view.findViewById(R.id.mail_text9);
        textView[9] = (TextView)view.findViewById(R.id.mail_text10);
        textView[10] = (TextView)view.findViewById(R.id.mail_text11);
       
        for(int i=0;i<MAX_PAGE;i++)
        {
          textView[i].setTextSize(TypedValue.COMPLEX_UNIT_SP, 30);
        }

        mReception = (EditText) view.findViewById(R.id.EditTextReception);
        

   }

   private void do_key_next()
   {
      if((m_total > 0) && (m_index < (m_total-1)))
      {
        m_index = m_index+1;
        
      }
      else 
      {
         m_index = 0;
      }

      show_mail(m_index);
   }
   private void do_key_home()
   {
    m_index = 0;
    show_mail(m_index);
   }
   private Button.OnClickListener btnDoListener=new Button.OnClickListener(){
       @Override
       public void onClick(View v)
       {
         

       }
   };

    public void timeout() {
         // TODO Auto-generated method stub

        if(m_is_active == false)    return;

        if(m_key_timer > 0)
          --m_key_timer;
      
    }
    private void postMessage(int id,int key)
    {
      Message message=mHandler.obtainMessage();
      
      Bundle data = new Bundle();
      
      data.putInt("key", key);
      message.setData(data);
    
      message.what = id;
      mHandler.sendMessage(message);
    }    
    
    private void postMessage(int id)
    {
        Message message=mHandler.obtainMessage();
        message.what = id;
        mHandler.sendMessage(message);
    }   
    
  
  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<MailActivity> mActivity;
    public StaticHandler(MailActivity activity)
    {
      mActivity = new WeakReference<MailActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
        MailActivity activity = mActivity.get();
        if(activity == null) return;

        int key;
        switch(msg.what)
        {
            case PRESSKEY_ACTION:
            key = msg.getData().getInt("key");
            //activity.mainActivity.showTips("PRESSKEY_ACTION "+String.valueOf(key));
            activity.doPress(key);
            break;
            case UPDATE_ACTION:
            key = MyApplication.KEY_FUNC;
            //activity.mainActivity.showTips("PRESSKEY_ACTION "+String.valueOf(key));
            activity.doPress(key);
            break;
            case REGISTER_CHANGE_ACTION:
            activity.showPhoneNumber();
            break;            
        }
    }
  }    

  public int OnBroadcastReceiver(Intent intent) {
    int ret = -1;
    String action = intent == null ? "" : intent.getAction();
    
    if (MyApplication.PRESSKEY_ACTION.equals(action)) 
    {
      int key = intent.getIntExtra(MyApplication.EXTRA_KEY,-1);
      //if(key >= 0)
        postMessage(PRESSKEY_ACTION,key);
      ret = 0;
    }     
   
    else if (MyApplication.MAIL_ACTION.equals(action)) 
    {
      
      postMessage(UPDATE_ACTION);
      ret = 0;
    }   
    else if (PortSipService.REGISTER_CHANGE_ACTION.equals(action)) {
      //String tips  =intent.getStringExtra(EXTRA_REGISTER_STATE);
  
      postMessage(REGISTER_CHANGE_ACTION);
      //setOnlineStatus(tips);
    }
    return ret;
  }

  private void doPress(int key)
  {
    String number;
    boolean is_clear=false;
    m_key_timer = 0;
    String str="";

    switch(key)
    {
    case MyApplication.KEY_NEXT:
      do_key_next();
      break;     
    case MyApplication.KEY_FUNC:
      do_key_home();
      break;      
    case MyApplication.KEY_SPK:
      m_is_video = true;
      break;
    case MyApplication.KEY_REDIAL:
      do_redial();
      is_clear = true;
      break;
    
    case MyApplication.KEY_P:
   
       number = mReception.getText().toString();
       outgoingCall_ext(true,number);
       is_clear = true;
      break;
    case MyApplication.KEY_S:
       str = "*";
      
      break;
    case MyApplication.KEY_0:
      str = "0";

      break;
    case MyApplication.KEY_9:
      str = "9"; 

      break;
    case MyApplication.KEY_8:
      str = "8";
  
      break;
    case MyApplication.KEY_7:
      str = "7";
      
      break;
    case MyApplication.KEY_6:
      str = "6";
      
      break;
    case MyApplication.KEY_5:
      str = "5";
      
      break;
    case MyApplication.KEY_4:
      str = "4";
      break;
    case MyApplication.KEY_3:
        
      str = "3";
      break;
    case MyApplication.KEY_2:
      str = "2";
      break;
    case MyApplication.KEY_1:
       str = "1";
    
      break;

    }    
    
    if(is_clear == true)
      mReception.setText("");
    else if(!TextUtils.isEmpty(str))
      mReception.append(str);
  }  

  private void outgoingCall_ext(boolean isVideo,String callTo) {
    String number;
  
    if(TextUtils.isEmpty(callTo))  return;
  
    number = callTo;
    outgoingCall(isVideo,number);
  }
  private void do_redial()
  {
    
    outgoingCall_ext(true,m_str_redial);
  }
  private void outgoingCall(boolean isVideo,String callTo) {
        
    if(is_called == true)    return;
  
    is_called = true;
    long ret = myApp.outgoingCall(isVideo,callTo);
    if(ret > 0)
    {
      m_str_redial = callTo;
      Ring.getInstance(getActivity()).startRingBackTone();
  
      mainActivity.loadVideoFragment();
    }
    is_called = false;    
  } 
  
  private void showPhoneNumber()
	{
    setOnlineStatus("");
  
	}  

  private void setOnlineStatus(String tips) {

    String line1=myApp.getPhoneNumber(1,true);
    
    if(!TextUtils.isEmpty(line1))
    {
      line1 = "(line 1 "+line1+")";
    }
  
    m_phoneTextView.setText(line1);
   
  }

}
