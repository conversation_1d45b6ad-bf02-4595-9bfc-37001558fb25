package com.weema.sipsample.ui;

import android.Manifest;
import android.app.Activity;
import android.app.Fragment;
import android.app.FragmentTransaction;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
//import android.support.annotation.IdRes;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.os.Bundle;
//import android.support.v4.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.widget.RadioGroup;
import android.widget.Toast;


import com.weema.R;
import com.weema.sipsample.receiver.PortMessageReceiver;
import com.weema.sipsample.service.PortSipService;
import com.weema.sipsample.service.PortSipService2;
import com.weema.sipsample.service.PortSipService3;

import com.weema.sipsample.service.PortSipServiceP2P;

import android.content.SharedPreferences;
import android.preference.PreferenceManager;

import android.view.View;

import android.util.Log;


import com.weema.sipsample.util.Session;
import com.weema.sipsample.util.CallManager;


import android.content.Context;

import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import java.util.Timer;
import java.util.TimerTask;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.PowerManager;
import android.app.KeyguardManager;
import android.app.KeyguardManager.KeyguardLock;

import android.util.DisplayMetrics;
import static com.weema.sipsample.service.PortSipService.EXTRA_REGISTER_STATE;
import static com.weema.sipsample.service.PortSipService.EXTRA_REGISTER_LINE;
import com.weema.sipsample.service.WaService;
import android.media.MediaPlayer;
import java.io.File;
import com.portsip.PortSipSdk;
import java.io.InputStream;
import java.io.OutputStream;
import 	java.io.FileOutputStream;
import 	java.io.IOException;
import 	java.io.FileInputStream;
import 	android.os.Environment;

import java.net.Socket;
import java.net.InetAddress;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import 	java.net.SocketTimeoutException;
import 	java.net.InetSocketAddress;


import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import android.util.Log;

import 	java.util.Calendar;
import java.text.SimpleDateFormat;
import 	java.text.ParseException;

/*
import be.tarsos.dsp.AudioDispatcher;
import be.tarsos.dsp.AudioEvent;
import be.tarsos.dsp.io.android.AudioDispatcherFactory;


import be.tarsos.dsp.GainProcessor;

import be.tarsos.dsp.pitch.PitchDetectionHandler;
import be.tarsos.dsp.pitch.PitchDetectionResult;
import be.tarsos.dsp.pitch.PitchProcessor;
import be.tarsos.dsp.pitch.PitchProcessor.PitchEstimationAlgorithm;
*/
public class MainActivity extends BaseMainActivity {

    private static final String TAG="MainActivity";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
       
    }
    @Override
    protected void onResume() {
        super.onResume();
    
        Log.i(TAG,"onresume");

        //String sessionId= getIntent().getStringExtra("EXTRA_SESSION_ID");
        Intent intent = getIntent();

        long sessionId = intent.getLongExtra(PortSipService.EXTRA_CALL_SEESIONID, Session.INVALID_SESSION_ID);

        intent.removeExtra(PortSipService.EXTRA_CALL_SEESIONID);

        if(Session.INVALID_SESSION_ID != sessionId)
        {
            processCallChange(sessionId);
        }
        else
        {

            int id = intent.getIntExtra(MyApplication.EXTRA_SOS_ALARM,-1);

            intent.removeExtra(MyApplication.EXTRA_SOS_ALARM);

            if(id >= 0)
            {
                sos_alarm(id);
                return;
            }

            id = intent.getIntExtra(MyApplication.BC_ACTION,-1);

            intent.removeExtra(MyApplication.BC_ACTION);

            if(id >= 0)
            {
                loadBCActivity();
                return;
            }

            Session currentLine = CallManager.Instance().getCurrentSession();
           
            if(currentLine != null && currentLine.IsIdle() == false)
            {
                Log.i(TAG,"onresume1");
                loadVideoFragment();

            }
            else
               loadConsoleActivity();

        }      
    }
}
