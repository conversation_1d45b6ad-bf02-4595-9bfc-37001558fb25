package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.CheckBox;

import android.widget.Button;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;

import android.widget.EditText;

import android.widget.Toast;

public class MdoorActivity extends Fragment {

    private static final String TAG="MdoorActivity";
    private MainActivity mainActivity;

    private int m_back_timer = 0;
    private int m_send_timer = 0;

    private MyApplication myApp;
  
    private static final int MAX_BACK_TIME = 30;

    private CheckBox m_checkB;
    private CheckBox m_checkbox_is_mdoor;
    private EditText m_edittext;
    private Button btn_ok;
    private Button btn_esc;

    private String m_mdoor_number;
    private boolean m_mdoor_enable;
    private boolean m_is_mdoor;

    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
          // TODO Auto-generated method stub

          super.onCreateView(inflater, container, savedInstanceState);

          View rootView = inflater.inflate(R.layout.set_monitor_door, null);
          //initView(rootView);
          mainActivity = (MainActivity) getActivity();
          myApp = (MyApplication) mainActivity.getApplicationContext();
          return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;
       if (!hidden){

           mainActivity.receiver.broadcastReceiver = null;

           m_mdoor_number = myApp.m_mdoor_number;
           m_checkB.setChecked(myApp.m_mdoor_enable);
           m_checkbox_is_mdoor.setChecked(myApp.m_is_mdoor);
           m_edittext.setText(m_mdoor_number);
           m_back_timer = MAX_BACK_TIME;
           //setOnlineStatus();
           Log.i(TAG,"onHiddenChanged");
         
       }
       else {
         
         m_back_timer = 0;

       
       }
   }


   private void initView(View view) {
         m_checkB = (CheckBox)view.findViewById(R.id.mdoor_checkB_1);
         m_checkbox_is_mdoor = (CheckBox)view.findViewById(R.id.checkbox_is_mdoor);

         m_edittext = (EditText)view.findViewById(R.id.mdoor_editText1);

         btn_ok= (Button)view.findViewById(R.id.mdoor_button_ok);
         btn_ok.setOnClickListener(btnDoListerner);

         btn_esc= (Button)view.findViewById(R.id.mdoor_button_esc);
         btn_esc.setOnClickListener(btnDoListerner);
   }


    private Button.OnClickListener btnDoListerner=new Button.OnClickListener(){
        @Override
	public void onClick(View v)
        {
	      switch(v.getId())
	      {
	      case R.id.mdoor_button_ok:

            m_mdoor_enable = m_checkB.isChecked();
            m_is_mdoor = m_checkbox_is_mdoor.isChecked();
            String number = m_edittext.getText().toString();

            Log.i(TAG,Boolean.toString(m_is_mdoor)+" "+m_mdoor_number+" "+number);
            if(m_is_mdoor == false && m_mdoor_enable != false)
            {
                myApp.getFromMDoor(number);
                m_back_timer = MAX_BACK_TIME;
                m_send_timer = MAX_BACK_TIME;
                mainActivity.showTips(R.string.str_waiting);
            }
            else
            {
                 m_back_timer = 1;
                 mainActivity.showTips(R.string.str_setting_ok);
            }

            myApp.setMDoor(mainActivity,m_mdoor_enable,number,m_is_mdoor);

            m_mdoor_number = number;

		   break;
	     case R.id.mdoor_button_esc:
           m_back_timer = 1;

		      break;

	     }
       }
    };

    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout() {
      // TODO Auto-generated method stub

      if(m_is_active == false)    return;
      Log.i(TAG,"timertask");

      if(m_back_timer > 0)
      {
        m_back_timer--;
        if(m_back_timer == 0)
        {
          postMessage(MyApplication.GOTO_BACK);
        

        }
      }

      if(m_send_timer > 0)
      {
        m_send_timer--;
        if(m_send_timer == 0)
        {
          postMessage(MyApplication.SEND_FAIL);
          
        }
        else if(myApp.m_mdoor_send_ret < 0)
        {
          m_send_timer = 0;
          postMessage(MyApplication.SEND_FAIL);
          
        }
        else if(myApp.m_mdoor_send_ret > 0)
        {
          Log.i(TAG,"SEND OK");
          m_send_timer = 0;
          m_back_timer = 0;
          postMessage(MyApplication.SEND_OK);
          
        }


      }
    }

    public final StaticHandler mHandler = new StaticHandler(this);
    private  static class StaticHandler extends Handler{
    private final WeakReference<MdoorActivity> mActivity;
    public StaticHandler(MdoorActivity activity)
    {
      mActivity = new WeakReference<MdoorActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      MdoorActivity activity = mActivity.get();
      if(activity == null) return;

	    switch(msg.what)
      {
        case MyApplication.GOTO_BACK:
          activity.mainActivity.loadMySettingFragment();

          //Toast.makeText(OptSOSActivity.this, R.string.str_no_respose, Toast.LENGTH_LONG).show();
          break;
        case MyApplication.SEND_FAIL:

          Toast.makeText(activity.mainActivity, R.string.str_no_respose, Toast.LENGTH_LONG).show();
          break;
        case MyApplication.SEND_OK:

          activity.mainActivity.loadMySettingFragment();

          Toast.makeText(activity.mainActivity, R.string.str_setting_ok, Toast.LENGTH_LONG).show();
          break;
        }

    }
  }          

}
