package com.weema.sipsample.ui;

import com.portsip.PortSipSdk;
import com.weema.R;
import com.weema.sipsample.adapter.ContactAdapter;
import com.weema.sipsample.receiver.PortMessageReceiver;
import com.weema.sipsample.service.PortSipService;
import com.weema.sipsample.util.CallManager;
import com.weema.sipsample.util.Contact;
import com.weema.sipsample.util.ContactManager;

import android.app.Fragment;
import android.content.Intent;
import android.os.Bundle;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Toast;

import java.util.List;

public class MessageFragment extends BaseFragment implements View.OnClickListener ,PortMessageReceiver.BroadcastListener{
    EditText etContact, etStatus, etmsgdest, etMessage;
    ListView lvContacts;

    MyApplication application;
    MainActivity activity;
    private ContactAdapter mAdapter;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        activity = (MainActivity) getActivity();
        application = (MyApplication) activity.getApplication();

        return inflater.inflate(R.layout.message, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        lvContacts = (ListView) view.findViewById(R.id.lvcontacs);
        view.findViewById(R.id.btsubscribe).setOnClickListener(this);
        view.findViewById(R.id.btclear).setOnClickListener(this);

        mAdapter = new ContactAdapter(getActivity(), ContactManager.Instance().getContacts());

        lvContacts.setAdapter( mAdapter);

        etStatus = (EditText) view.findViewById(R.id.etstatus);
        etMessage = (EditText) view.findViewById(R.id.etmessage);
        etContact = (EditText) view.findViewById(R.id.etcontact);
        etmsgdest = (EditText) view.findViewById(R.id.etmsgdest);

        view.findViewById(R.id.btsendmsg).setOnClickListener(this);
        view.findViewById(R.id.btsendstatus).setOnClickListener(this);
        view.findViewById(R.id.btaddcontact).setOnClickListener(this);

        view.findViewById(R.id.btaccept).setOnClickListener(this);
        view.findViewById(R.id.btrefuse).setOnClickListener(this);
        view.findViewById(R.id.btsubscribe).setOnClickListener(this);
        onHiddenChanged(false);

    }

    private void BtnAddContact_Click(PortSipSdk sdk) {
        if (!IsOnline())
            return;
        String sendTo = etContact.getText().toString();
        if (TextUtils.isEmpty(sendTo)) {
            return;
        }

        Contact contact = ContactManager.Instance().FindContactBySipAddr(sendTo);
        if (contact == null) {

            contact = new Contact();
            contact.SipAddr = sendTo;
            ContactManager.Instance().AddContact(contact);
        }
        updateLV();
    }

    private void BtnSubscribeContact_Click(PortSipSdk sdk) {
        if (!IsOnline())
            return;
        Contact contact = GetSelectContact();
        if (contact != null) {
            sdk.presenceSubscribe(contact.SipAddr, "hello");//subscribe remote
            contact.SubscribRemote = true;
        }
        updateLV();
    }

    private void BtnClearContact_Click() {
        ContactManager.Instance().RemoveAll();
        updateLV();
    }

    private void updateLV() {
        mAdapter.notifyDataSetChanged();
    }

    private void BtnSetStatus_Click(PortSipSdk sdk) {
        if (!IsOnline())
            return;

        String content = etStatus.getText().toString();
        if (TextUtils.isEmpty(content)) {
            //showTips("please input status description string");
            return;
        }
        List<Contact> contacts = ContactManager.Instance().getContacts();
        for (Contact contact : contacts) {
            long subscribeId = contact.SubId;

            String statusText = etStatus.getText().toString();
            if (contact.state == Contact.SUBSCRIBE_STATE_FLAG.ACCEPTED)//向已经接受的订阅，发布自己的出席状态
            {
                sdk.setPresenceStatus(subscribeId, statusText);
            }
        }

    }

    private void BtnAcceptSubscribe_Click(PortSipSdk sdk) {
        if (!IsOnline())
            return;
        Contact contact = GetSelectContact();
        if (contact != null && contact.state == Contact.SUBSCRIBE_STATE_FLAG.UNSETTLLED) {
            sdk.presenceAcceptSubscribe(contact.SubId);//accept
            contact.state = Contact.SUBSCRIBE_STATE_FLAG.ACCEPTED;
            String status = etStatus.getText().toString();
            if (!TextUtils.isEmpty(status)) {
                status = "hello";
            }
            sdk.setPresenceStatus(contact.SubId, status);//set my status

        }

        updateLV();
    }

    private void BtnRefuseSubscribe_Click(PortSipSdk sdk) {
        if (!IsOnline())
            return;
        Contact contact = GetSelectContact();
        if (contact != null && contact.state == Contact.SUBSCRIBE_STATE_FLAG.UNSETTLLED) {

            sdk.presenceRejectSubscribe(contact.SubId);//reject
            contact.state = Contact.SUBSCRIBE_STATE_FLAG.REJECTED;// reject subscribe
            contact.SubId = 0;

        }
        updateLV();
    }

    private void BtnSend_Click(PortSipSdk sdk) {

        if (!IsOnline())
            return;
        String content = etMessage.getText().toString();
        String sendTo = etmsgdest.getText().toString();
        if (TextUtils.isEmpty(sendTo)) {
            Toast.makeText(getActivity(), "Please input send to target",
                    Toast.LENGTH_SHORT).show();
            return;
        }

        if (TextUtils.isEmpty(content)) {
            Toast.makeText(getActivity(), "Please input message content",
                    Toast.LENGTH_SHORT).show();
            return;
        }
        byte[] contentBinary = content.getBytes();
        if (contentBinary != null) {
            sdk.sendOutOfDialogMessage(sendTo, "text", "plain", false,
                    contentBinary, contentBinary.length);
        }
    }

    public int OnBroadcastReceiver(Intent intent) {
        String action = intent == null ? "" : intent.getAction();
        if (PortSipService.PRESENCE_CHANGE_ACTION.equals(action)) {
            updateLV();
        }

        return 0;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            activity.receiver.broadcastReceiver = this;
            updateLV();
        }
    }

    private boolean IsOnline() {
        if (!CallManager.Instance().getregist(1)) {
            Toast.makeText(getActivity(), "Please login at first", Toast.LENGTH_SHORT).show();
        }
        return CallManager.Instance().getregist(1);
    }

    private Contact GetSelectContact() {
        List<Contact> contacts = ContactManager.Instance().getContacts();
        int checkedItemPosition = lvContacts.getCheckedItemPosition();
        if (ListView.INVALID_POSITION != checkedItemPosition && contacts.size()> checkedItemPosition) {
            return contacts.get(checkedItemPosition);
        }
        return null;
    }

    @Override
    public void onClick(View view) {
        if (application.mEngine == null) {
            return;
        }
        switch (view.getId()) {
            case R.id.btsendmsg:
                BtnSend_Click(application.mEngine);
                break;
            case R.id.btsendstatus:
                BtnSetStatus_Click(application.mEngine);
                break;
            case R.id.btaddcontact:
                BtnAddContact_Click(application.mEngine);
                break;
            case R.id.btclear:
                BtnClearContact_Click();
                break;
            case R.id.btsubscribe:
                BtnSubscribeContact_Click(application.mEngine);
                break;
            case R.id.btaccept:
                BtnAcceptSubscribe_Click(application.mEngine);
                break;
            case R.id.btrefuse:
                BtnRefuseSubscribe_Click(application.mEngine);
                break;
            default:
                break;
        }
    }
}
