package com.weema.sipsample.ui;


import com.portsip.PortSipSdk;
import com.portsip.PortSIPVideoRenderer;
import com.portsip.PortSipEnumDefine;

import android.app.Application;

import com.weema.sipsample.service.PortSipService;
import com.weema.sipsample.service.PortSipService2;
import com.weema.sipsample.service.PortSipService3;

import com.weema.sipsample.util.Session;
import com.weema.sipsample.util.CallManager;
import com.weema.sipsample.util.UserInfo;
import com.weema.R;

import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.widget.Toast;

import android.os.PowerManager;
import com.weema.sipsample.util.SettingConfig;
import com.weema.sipsample.util.Ring;

import android.content.Intent;
import com.weema.sipsample.service.WaService;
import com.weema.sipsample.service.RemoteCastielService;

import java.util.Date;
import java.text.SimpleDateFormat;
import com.weema.sipsample.util.TongXunJiLu;
import com.weema.sipsample.util.JsonUtil;
import com.weema.sipsample.util.TongXunLu;
import com.weema.sipsample.util.Network;

import android.content.Context;
import java.util.Arrays;
import android.util.Log;
import java.util.ArrayList;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import android.text.TextUtils ;

import android.app.Service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;

import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import 	android.app.ActivityManager;

import java.io.File;
import 	java.nio.channels.FileChannel;
import java.io.FileInputStream;
import 	java.io.FileOutputStream;
import 	java.io.FileNotFoundException;
import android.os.Environment;
import 	java.io.IOException;
import java.util.TimeZone;

import java.io.DataOutputStream;

import java.io.InputStream;
import com.weema.sipsample.util.CrashLogCatch;
import java.util.HashMap;
import java.io.BufferedReader;
import java.io.InputStreamReader;

import com.weema.sipsample.model.SettingModel;

import com.weema.sipsample.service.ServiceWork;
import com.weema.sipsample.util.Utility;
import com.weema.sipsample.model.SIPStore;
public class MyApplication extends Application {
	private static final String TAG = "MyApplication";
	public static final String NETWORK_CHANGE_ACTION = "com.androiderstack.broadcastreceiverdemo.NetworkChangeReceiver";
	public static final String FWD_CHANGE_ACTION = "FWD_CHANGE_ACTION";
	public static final String EXTRA_SOS_ALARM = "EXTRA_SOS_ALARM";
	public static final String DOOR_ACTION = "DOOR_ACTION";
	public static final String DOOR_ACTION_CALL = "DOOR_ACTION_CALL";
	public static final String LOCK_ACTION = "LOCK_ACTION";
	public static final String GAS_ACTION = "GAS_ACTION";
	public static final String IPCAM_ACTION = "IPCAM_ACTION";
	public static final String PRESSKEY_ACTION = "PRESSKEY_ACTION";
	public static final String MAIL_ACTION = "MAIL_ACTION";

	public static final String BC_ACTION = "BC_ACTION";	
	public static final String RING_SET_REPLY = "RING_SET_REPLY";
	public static final String RING_GET_REPLY = "RING_GET_REPLY";
	public static final String SHOW_DOOR_RINGACTION="SHOW_DOOR_RINGACTION";
	public static final String SHOW_LOCK_PINACTION="SHOW_LOCK_PINACTION";
	public static final String SHOW_MDOOR_ACTION="SHOW_MDOOR_ACTION";

	public static final String EXTRA_RING_ENABLE = "EXTRA_RING_ENABLE";
	public static final String EXTRA_RING_NUMBER = "EXTRA_RING_NUMBER";
	public static final String EXTRA_RING_MIC = "EXTRA_RING_MIC";
	public static final String EXTRA_RING_VOLUME = "EXTRA_RING_VOLUME";
	public static final String EXTRA_PHONE_IP = "EXTRA_PHONE_IP";	
	public static final String EXTRA_KEY = "EXTRA_KEY";	
	private static final String AUTOUPDATE_NEED_UPDATE = "AUTOUPDATE_NEED_UPDATE";

	public final static long MIN_SIZE = 1000000000;

	public static final String EXTRA_IPCAM = "EXTRA_IPCAM";

  	public static final int SERVER_PORT = 20020;
	public static final int MAX_SOCKET_TIMEOUT = 5000;
	public static final int MAX_CONNECT_TIME=5000;

  	public static final int MAX_LINES=3;
  	public static String VERSION;

	public static final int LOGIN_FAIL = 1;
	public static final int LOGIN_OK = 2;
	public static final int START_REGISTER = 3;
	public static final int GOTO_BACK = 4;
	public static final int SOS_CALL = 5;
  	public static final int WAIT_TIMEOUT = 6;
  	public static final int SEND_OK = 7;
  	public static final int SEND_FAIL = 8;
	public static final int GET_OK = 9;
	public static final int MAIN_MENU = 10;
	public static final int ALARM_DELAY = 11;


	public static final int ALL_FWD=0;
	public static final int BUSY_FWD=1;
	public static final int TIMEOUT_FWD=2;


	public static final int MAX_RING_CNT=10;

	public static final int KEY_0 = 0;
	public static final int KEY_1 = 1;
	public static final int KEY_2 = 2;
	public static final int KEY_3 = 3;
	public static final int KEY_4 = 4;
	public static final int KEY_5 = 5;
	public static final int KEY_6 = 6;
	public static final int KEY_7 = 7;	
	public static final int KEY_8 = 8;
	public static final int KEY_9 = 9;	
	
	public static final int KEY_S = 10;
	public static final int KEY_P = 11;
	public static final int KEY_SPK = 12;
	public static final int KEY_NEXT = 13;
	public static final int KEY_FUNC = 14;
	public static final int KEY_REDIAL = 15;			

  	public int m_sos_index=0;
  	private WaService m_WaService=null;
  	public boolean m_is_login_first= false;
	public boolean mConference= false;
	public PortSipSdk mEngine;
	public PortSipSdk mEngine2;
	public PortSipSdk mEngine3;
	public PortSipSdk mEngineP2P;

	private Service portsipService;
	private Service portsipService2;
	private Service portsipService3;
	private Service portsipServiceP2P;


	public boolean mUseFrontCamera= false;

  	public boolean isRefer;

  	protected PowerManager.WakeLock mCpuLock;

	private Network netmanager;

	private boolean misEnableFWD;
	private int mtypeFWD;
	private String mnumberFWD;
	private int mtimeoutFWD;
	private boolean misDND;
	public boolean isTransfer;

  	private String[] mregtips;

	private int m_ring_volume;

	public int m_volume;
	public int mCurMicVol;
	private Long m_tsLong=0L;
	private double[] m_scaling={0,0.707,1,1.25,1.56,2};
	private int[] m_mic_scaling={0,29,32,35,37,40,43,46,49,52,55};
	private int[] m_spk_scaling={0,72,80,88,100,105,108,111,114,117,120};

	public int[] m_key_map={KEY_S,KEY_7,KEY_4,KEY_1,
							KEY_0,KEY_8,KEY_5,KEY_2,
							KEY_P,KEY_9,KEY_6,KEY_3,
							KEY_REDIAL,KEY_FUNC,KEY_NEXT,KEY_SPK
							};

	private Context myContext=null;
	public  final int MAX_TIMESECOND=2;
	public static final String myLicenseKey = "4ANDPx43REI5QzI4NzFBM0UxOUY0N0Q4QkY3QjBGMzBDNjNEQUBFRjFFQzFBRTJDQkNGN0Q2QUQyRUVGMzM2NUYxNEI3NEA3OUIyNjI5QTEyRTBDMEMzRkVBNDIwMUZCOTQ4OEFBQUA5RjM2RTVGNjBFRUUxMTUzOEIzRDY2RTQ4RjQ2MDQwQg";
  	public int keyRet = -1;

	public boolean m_is_lock;
	public String m_str_passwd;
	public boolean[] m_zone;
  	private int[] m_sos_state;
	public static final int SOS_IDLE_STATE = 0;
	public static final int SOS_ALARM_STATE = 1;

    public String allow;
    public boolean night_enable;
    public int[] night_time;
    public String night_call;

	public boolean[] m_sos_enable;
	public int[] m_sos_select_line;
	public String[] m_sos_number;
	public String[] ipcam_arr;

	public int[] m_dtmf_recv;
	public int m_dtmf_index = 0;
	public static final int MAX_DTMF_RECV = 30;
  	public boolean m_sd;
	public boolean m_ms;

  	public int m_sos_id=-1;

	public int m_door_state;

	public boolean m_mdoor_enable;
	public boolean m_is_mdoor;
	private int m_mdoor_state;
	public String m_mdoor_number;

  	private ArrayList<String> myList;
  	public boolean m_mdoor_stop = true;
  	public String m_local_ip;

	public boolean m_alarm_msg_enable = true;
  	public String m_alarm_msg_number;
  	public String m_center_ip;
  	public static final boolean m_is_test = false;
	public static final boolean m_is_test_open_door = false;
  	public static final boolean m_is_need_acc=false;

	public String m_app_number = "";

  	public boolean m_ring_enable;
	public String m_ring_number;
	public String m_ring_phone_ip;
	public boolean m_ring_first;
	public int m_ring_cnt;

  	public boolean m_monitor_enable = false;
	public String m_monitor_number;
	public boolean m_is_monitor_call=false;
	public String m_video_ip;

	public String bc_content=null;
	public String mail_content=null;

  	public String m_mdoor_send;
	public int m_mdoor_send_ret;

	public boolean is_record_enable;
	public boolean isDaoLan;
	public boolean m_is_door_notify;

	public int previous_call_status = 0;
	private static final Pattern IP_ADDRESS
	    = Pattern.compile(
	        "((25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9])\\.(25[0-5]|2[0-4]"
	        + "[0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9]|0)\\.(25[0-5]|2[0-4][0-9]|[0-1]"
	        + "[0-9]{2}|[1-9][0-9]|[1-9]|0)\\.(25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}"
			+ "|[1-9][0-9]|[0-9]))");
			
	public boolean m_is_ipcam = false;

	public String m_path = "";	
	public String m_message;

	public boolean m_is_need_rec=false;
	public String m_rec_source_str;
	public String m_rec_det_str;

	public String m_door_ip=null;

	public int m_sd_timer=0;
    private final int MAX_DELAY_TIME = 30;

	public String m_ring_config;


	private boolean[] m_sip_enable;
	private String[] m_user_name;
	private String[] m_user_pwd;
	private String[] m_svr_host;
	private String[] m_svr_port;
	private String[] m_user_domain;
	public boolean m_video_record_start=false;
	public boolean m_video_record_enable=true;
	private String m_usb_rec_path=null;

	private PortSIPVideoRenderer sdklocalRenderScreen=null;
	private boolean sdkstate=false;
	private boolean sdkmirror=false;

	public boolean m_sostalk_enable;

	private long m_time_offset=0;
	public boolean m_is_show = true;
	public boolean m_ctrl_gas = false;

	public boolean m_is_sos_play = false;
	public boolean skip_network = false;
	private boolean m_agc;
	public String m_mgr_number = "128";

	public boolean m_is_sos_mode = true;
	public int m_ctrl_bz;
	public boolean m_is_debug = false;

	public String m_ipaddr;
	public String m_netmask;
	public String m_gateway;
	public String m_dns;	
	private HashMap<Integer, Integer> m_map = new HashMap<Integer, Integer>();	
	
	public SettingModel settingModel;
	public ServiceWork serviceWork;
	public SIPStore sipStore;
	private void init_map()
	{

        m_map.put( R.id.dialButton_0,KEY_0);
		m_map.put( R.id.dialButton_1,KEY_1);	
		m_map.put( R.id.dialButton_2,KEY_2);	
		m_map.put( R.id.dialButton_3,KEY_3);	
		m_map.put( R.id.dialButton_4,KEY_4);	
		m_map.put( R.id.dialButton_5,KEY_5);	
		m_map.put( R.id.dialButton_6,KEY_6);
		m_map.put( R.id.dialButton_7,KEY_7);
		m_map.put( R.id.dialButton_8,KEY_8);
		m_map.put( R.id.dialButton_9,KEY_9);	
		m_map.put( R.id.dialButton_xing,KEY_S);	
		m_map.put( R.id.dialButton_jing,KEY_P);	
			
											
	}
	public int get_map_value(int key)
	{
		int ret = key;
		if(m_map.containsKey(key))
		{
			ret = m_map.get(key);
		}

		return ret;
	}
	private void local_onCreate()
	{
		settingModel = new SettingModel(getApplicationContext());
		serviceWork = new ServiceWork(settingModel);
		sipStore = new SIPStore();
		init_map();
		m_ring_cnt = MAX_RING_CNT;
		m_ring_first = true;

    	myList = new ArrayList<String>();
		String str = SettingConfig.getDoorList(getApplicationContext());

		String[] str_arr = str.split(" ");
		int len = str_arr.length;

    	Log.i(TAG,"add "+String.valueOf(len)+" "+str);
		for(int i=0;i<len;i++)
		{
			if(!TextUtils.isEmpty(str_arr[i]))
			{
				Log.i(TAG,"add "+str_arr[i]);
			    myList.add(str_arr[i]);
      		}
		}
		mEngine = new PortSipSdk();
		mEngine2 = new PortSipSdk();
		mEngine3 = new PortSipSdk();
    	mEngineP2P = new PortSipSdk();

		m_dtmf_recv = new int[MAX_DTMF_RECV];
		m_dtmf_index = 0;

    	isTransfer = false;

		m_str_passwd = SettingConfig.getLockPasswd(getApplicationContext());
    	m_is_lock = SettingConfig.getLock(getApplicationContext());
		mCurMicVol = SettingConfig.getMIC(getApplicationContext());
		m_volume = SettingConfig.getVolume(getApplicationContext());

		allow = SettingConfig.getAllow(getApplicationContext());
		night_enable = SettingConfig.getNightEnable(getApplicationContext());
		night_time = SettingConfig.getNightTime(getApplicationContext());
		night_call = SettingConfig.getNightCall(getApplicationContext());

		m_sos_enable = SettingConfig.getEnableSOS(getApplicationContext());
		m_sos_select_line = SettingConfig.getSelectLine(getApplicationContext());
		m_sos_number = SettingConfig.getSOSNumber(getApplicationContext());
		m_sd = SettingConfig.getSD(getApplicationContext());
		m_ms = SettingConfig.getMS(getApplicationContext());

		m_is_mdoor = SettingConfig.getIsMDoor(getApplicationContext());
    	m_mdoor_enable = SettingConfig.getMDoorEnable(getApplicationContext());
		m_mdoor_number = SettingConfig.getMDoor(getApplicationContext());

		m_alarm_msg_enable = SettingConfig.getAlarmMsgEnable(getApplicationContext());
		m_alarm_msg_number = SettingConfig.getAlarmMsg(getApplicationContext());

		m_center_ip = SettingConfig.getCenterIP(getApplicationContext());

		m_ring_enable = SettingConfig.getRingEnable(getApplicationContext());
		m_ring_number = SettingConfig.getRingNumber(getApplicationContext());
		m_ring_phone_ip = SettingConfig.getRingPhoneIP(getApplicationContext());

		m_monitor_enable = SettingConfig.getMonitorEnable(getApplicationContext());
		m_monitor_number = SettingConfig.getMonitorNumber(getApplicationContext());

		is_record_enable = SettingConfig.getRecordEnable(getApplicationContext());

		m_video_ip = SettingConfig.getVideoIP(getApplicationContext());

		if(TextUtils.isEmpty(m_video_ip))
			m_video_ip = "***********";

		isDaoLan = SettingConfig.getVoice(getApplicationContext());

		String str_ipcam = SettingConfig.getIPCam(getApplicationContext());
    	ipcam_arr = str_ipcam.split("\n");
		
		m_door_ip = SettingConfig.getDoorIP(getApplicationContext());

		m_is_door_notify = SettingConfig.getDoorNotify(getApplicationContext());
		m_video_record_enable = SettingConfig.getDoorRecord(getApplicationContext());

		m_sostalk_enable = SettingConfig.getSOSTalkEnable(getApplicationContext());

		m_ring_config = SettingConfig.getRingConfig(getApplicationContext());
		m_ctrl_gas = SettingConfig.getCtrlGas(getApplicationContext());

		m_is_sos_play = SettingConfig.getSOSPlay(getApplicationContext());
	
		m_agc = SettingConfig.getAGC(getApplicationContext());

		m_is_sos_mode = SettingConfig.getSOSMode(getApplicationContext());

		m_ctrl_bz = SettingConfig.getCtrlBZ(getApplicationContext());

		mail_content = SettingConfig.getMailContent(getApplicationContext());
		
		m_app_number = SettingConfig.getAppNumber(getApplicationContext());

		Log.i(TAG,"local_onCreate mic: " + mCurMicVol + ", sp: " + m_volume);

		loadUserInfo();
        //is_record_enable = true;
    	Log.i(TAG,Boolean.toString(m_ring_enable)+" "+m_ring_number);
    	m_door_state = WaService.UNKNOW_STATE;
		String zone = SettingConfig.getZone(getApplicationContext());
		char[] zone_c = zone.toCharArray();

    	m_zone = new boolean[5];
    	for(int i=0;i<5;i++)
    	{
       		m_zone[i] = false;
    	}

		if(zone_c.length >= 5)
		{
			for(int i=0;i<5;i++)
			{
				if( zone_c[i]== '1')
				{
					m_zone[i] = true;
				}
			}

		}

		m_sos_state = new int[16];
		for(int i=0;i<m_sos_state.length;i++)
		{
			m_sos_state[i] = SOS_IDLE_STATE;
		}

	  	//keepCpuRun(true);

		if(skip_network == false)
		{
		netmanager = new Network(this, new Network.NetWorkChangeListner() {
		@Override
		public void handleNetworkChangeEvent(final boolean wifiConnect,final boolean mobileConnect) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    synchronized (MyApplication.class) {
												
                }
            }
            }).start();
            }
		});
		}

		getConfigFWD();

    	misDND = SettingConfig.getDND(getApplicationContext());

		//m_volume = getVolume();
		if(myContext != null){
			m_ring_volume = Ring.getInstance(myContext).getRingVolume();
			//setVolume(2);
			//Ring.getInstance(myContext).setVolume(2);
		}
    	isRefer=false;

		mregtips = new String[MAX_LINES];

		for(int i=0;i<MAX_LINES;i++)
		{
			mregtips[i] = "";
		}

		startService(new Intent(getApplicationContext(), WaService.class));
		startService(new Intent(getApplicationContext(), RemoteCastielService.class));
		show_wawatchdog();
		get_app_info();
		reenableNetwork();
		copy_ethernet_file();
		//check_config();
	
	}
	public void setCtrlBZ(int val)
	{
		m_ctrl_bz = val;

		if(m_WaService != null)
			m_WaService.ctrlBZ();

		SettingConfig.setCtrlBZ(getApplicationContext(),val);
	
	}
	private void execute_as_root( String[] commands) {
		try {
			for( String command : commands ) {
				Log.i(TAG,command);
				
			}			
			// Do the magic
			Process p = Runtime.getRuntime().exec( "su" );
			InputStream es = p.getErrorStream();
			DataOutputStream os = new DataOutputStream(p.getOutputStream());

			BufferedReader bufferedReader = new BufferedReader(
				new InputStreamReader(p.getInputStream()));
  
			for( String command : commands ) {
				Log.i(TAG,command);
				os.writeBytes(command + "\n");
			}
			os.writeBytes("exit\n");
			os.flush();
			os.close();

			int read;
			byte[] buffer = new byte[4096];
			String output = new String();
			/* 
			while ((read = es.read(buffer)) > 0) {
			    output += new String(buffer, 0, read);
			}
			*/
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				output += line;
			}
			
			p.waitFor();
			Log.e(TAG, output.trim() + " (" + p.exitValue() + ")");
		
		} catch (IOException e) {
			Log.e(TAG, e.getMessage());
		} catch (InterruptedException e) {
			Log.e(TAG, e.getMessage());
		}
	}	
	public int get_cur_mic_value()
	{
		if(mCurMicVol >= 0 && mCurMicVol < 10)
		{
			return m_mic_scaling[mCurMicVol];
		}
	
		return 100;
	}
	public void copy_ethernet_file()
	{
		String path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath();
		 
	 	final String[] commands = {
			"cp -a /data/misc/ethernet/ipconfig.txt " + path,
			
			//libs + "am start -n com.weema/com.weema.sipsample.ui.MainActivity"
		};
	
		execute_as_root(commands);	// not supposed to return if successful

	}
	private void wakeUp() {
    	PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
    	Log.d(TAG, "wjz debug wakeUp: screen is on ? == " + powerManager.isInteractive());
    	if (!powerManager.isInteractive()) {
        	//屏锁管理器
//            KeyguardManager km = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
//            KeyguardManager.KeyguardLock kl = km.newKeyguardLock("unLock");
//            //解锁
//            kl.disableKeyguard();

        	PowerManager.WakeLock wakeLock = powerManager.newWakeLock(PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.SCREEN_DIM_WAKE_LOCK, "cyg_wake");

        	//点亮屏幕
        	wakeLock.acquire(1000L);
        	//释放
        	wakeLock.release();
    	}
	}
	public void LCD_on()
	{
		wakeUp();
		
	}

	public void doNetworkCmd()
	{
		final String[] commands = {
			"ifconfig eth0 down",
			"sleep 1",
			"ifconfig eth0 up",
			
			//libs + "am start -n com.weema/com.weema.sipsample.ui.MainActivity"
		};
	
		execute_as_root(commands);	// not supposed to return if successful

	}
	public void reboot()
	{
		final String[] commands = {
			"reboot now",
			
			//libs + "am start -n com.weema/com.weema.sipsample.ui.MainActivity"
		};
	
		execute_as_root(commands);	// not supposed to return if successful

	}
	
	public void reenableNetwork()
	{
		doNetworkCmd();
	}	
	public void disableNetwork()
	{
		doNetworkCmd();
	}
	public String getMDoorState()
	{

		String str="";

		if(m_is_mdoor)
		{
			str = String.valueOf(m_mdoor_state);
		}

		return str;
	}
	public String getUserInfo()
	{
		String str;

		str = "";
		for(int i=0;i<3;i++)
		{
			str = str+m_user_name[i]+","+m_user_pwd[i]+","+m_svr_host[i]+","+m_svr_port[i]+","+m_user_domain[i]+",";
		
			if(m_sip_enable[i])
				str = str+"1,";
			else 
				str = str+"0,";			
		}

		return str;

	}

	public void saveSOSInfo_arr(String[] str_arr)
	{
		int index=2;

		for(int i=0;i<m_sos_enable.length;i++)
		{
			if(str_arr[index+i*3].equals("1"))
			{
				m_sos_enable[i] = true;
			}
			else 
			{
				m_sos_enable[i] = false;
			}

			m_sos_select_line[i] = Utility.getNumber(str_arr[index+1+i*3]);
			m_sos_number[i] = str_arr[index+2+i*3];
			
		}

		m_str_passwd = str_arr[index+15];
	}
	public void saveDoorInfo_arr(String[] str_arr)
	{
		int index=2;

		if(str_arr[index].equals("1"))
			m_is_mdoor = true;
		else 
			m_is_mdoor = false;		

		if(str_arr[index+1].equals("1"))
			m_mdoor_enable = true;
		else 
			m_mdoor_enable = false;
	
		m_mdoor_number = str_arr[index+2];
	
		if(str_arr[index+3].equals("1"))
			m_ring_enable = true;
		else 
			m_ring_enable = false;	

		m_ring_number = str_arr[index+4];
		m_ring_phone_ip = str_arr[index+5];
		// MIC
		mCurMicVol = Utility.getNumber(str_arr[index+6]);
		// 喇叭音量
		m_volume = Utility.getNumber(str_arr[index+7]);
		// 鈴聲音量
		m_ring_volume = Utility.getNumber(str_arr[index+8]);
		// 喇叭硬體 SPEAKER(HW)
		Ring.getInstance(this).setVolume(Utility.getNumber(str_arr[index+9]));
		
		Log.d(TAG, "saveDoorInfo_arr mic: " + mCurMicVol + ", sp: " + m_volume + ", ring: " + m_ring_volume);
		

	}	
	public void saveEthernetInfo_arr(String[] str_arr)
	{	
		int index=2;

		if(str_arr.length < 6)    return;

		m_ipaddr = str_arr[index].trim();
		m_netmask = str_arr[index+1].trim();
		m_gateway = str_arr[index+2].trim();
		m_dns = str_arr[index+3].trim();

		save_file(m_ipaddr,m_netmask,m_gateway,m_dns);
	}		

	public void saveMailInfo_arr(String[] str_arr)
	{
		int index=2;
	
		if(str_arr.length <= 2)    return;

		String str;
		str = str_arr[index];

		String startString = str.substring(0, 1);

		if(startString.equals("$"))
		{
			mail_content = "";
			str = str.substring(1);
		}

		String speater = "";
		if(!TextUtils.isEmpty(mail_content))
		{
			speater = "&&";
		}
		
		if(!TextUtils.isEmpty(str))
		{	
		
			mail_content = mail_content+speater+str;	
		}	
	}		
	public void saveOtherInfo_arr(String[] str_arr)
	{
		int index=2;
	
		m_video_ip = str_arr[index];
		
		if(str_arr[index+1].equals("1"))
		{
			m_monitor_enable = true;
			
		}
		else 
		{
			m_monitor_enable = false;
		}

		m_monitor_number = str_arr[index+2];
		m_center_ip = str_arr[index+3];
		
		if(str_arr[index+4].equals("1"))
		{
			m_alarm_msg_enable = true;
			
		}
		else 
		{
			m_alarm_msg_enable = false;
		}
		
		m_alarm_msg_number = str_arr[index+5];
		
		if(str_arr[index+6].equals("1"))
		{
			is_record_enable = true;
			
		}
		else 
		{
			is_record_enable = false;
		}

	
		if(str_arr[index+7].equals("1"))
		{
			m_is_door_notify = true;
			
		}
		else 
		{
			m_is_door_notify = false;
		}	
			
		if(str_arr[index+8].equals("1"))
		{
			m_sd = true;
			
		}
		else 
		{
			m_sd = false;
		}		

		if(str_arr[index+9].equals("1"))
		{
			m_sostalk_enable = true;
			
		}
		else 
		{
			m_sostalk_enable = false;
		}				

		if(str_arr[index+10].equals("1"))
		{
			m_ctrl_gas = true;
			
		}
		else 
		{
			m_ctrl_gas = false;
		}				

		if(str_arr[index+11].equals("1"))
		{
			m_is_sos_play = true;
			
		}
		else 
		{
			m_is_sos_play = false;
		}				
			
		boolean agc;

		if(str_arr[index+12].equals("1"))
		{
			agc = true;
			
		}
		else 
		{
			agc = false;
		}	
		
		if(m_agc != agc)
		{
			m_agc = agc;
			setAGC();

		}

		if(str_arr[index+13].equals("1"))
		{
			m_is_sos_mode = true;
			
		}
		else 
		{
			m_is_sos_mode = false;
		}	

		m_ctrl_bz = Utility.getNumber(str_arr[index+14]);

		if((str_arr.length >= 19)  && str_arr[index+16].equals("1"))
		{
			Intent i = new Intent(MyApplication.AUTOUPDATE_NEED_UPDATE);
	
			sendBroadcast(i);
		}
				
	}		
	private void setAGC()
	{
		mEngine.enableAGC(m_agc);
		mEngine2.enableAGC(m_agc);
		mEngine3.enableAGC(m_agc);
		mEngineP2P.enableAGC(m_agc);
		SettingConfig.setAGC(getApplicationContext(),m_agc);
	}
	public void saveTransferInfo_arr(String[] str_arr)
	{
		int index=2;

		if(str_arr[index].equals("1"))
		{
			misEnableFWD = true;
		}
		else 
		{
			misEnableFWD = false;
		}
		
		mtypeFWD = Utility.getNumber(str_arr[index+1]);
	
		mnumberFWD = str_arr[index+2];
		mtimeoutFWD = Utility.getNumber(str_arr[index+3]);
	

	}		
	public void saveZoneInfo_arr(String[] str_arr)
	{
		int index=2;

		for(int i=0;i<m_zone.length;i++)
		{
			if(str_arr[index+i].equals("1"))
			{
				m_zone[i] = true;
			}
			else 
			{
				m_zone[i] = false;
			}
			
		}
	}	
	public void saveUserInfo_arr(String[] str_arr)
	{
		int index=2;

		for(int i=0;i<3;i++)
		{
			m_user_name[i] = str_arr[index+0+i*6];
			m_user_pwd[i] = str_arr[index+1+i*6];
			m_svr_host[i] = str_arr[index+2+i*6];
			m_svr_port[i] = str_arr[index+3+i*6];
			m_user_domain[i] = str_arr[index+4+i*6];	

			if(str_arr[index+i*6+5].equals("1"))
				m_sip_enable[i] = true;
			else 
				m_sip_enable[i] = false;

		}

	}	
	
    public void saveUserInfo() {
        SharedPreferences.Editor editor = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();

        editor.putBoolean(PortSipService.SIP_ENABLE, m_sip_enable[0]);

        editor.putString(PortSipService.USER_NAME, m_user_name[0]);
        editor.putString(PortSipService.USER_PWD, m_user_pwd[0]);
        editor.putString(PortSipService.SVR_HOST, m_svr_host[0]);
        editor.putString(PortSipService.SVR_PORT, m_svr_port[0]);

        editor.putString(PortSipService.USER_DOMAIN, m_user_domain[0]);

		editor.putBoolean(PortSipService.SIP_ENABLE2, m_sip_enable[1]);

        editor.putString(PortSipService.USER_NAME2, m_user_name[1]);
        editor.putString(PortSipService.USER_PWD2, m_user_pwd[1]);
        editor.putString(PortSipService.SVR_HOST2, m_svr_host[1]);
        editor.putString(PortSipService.SVR_PORT2, m_svr_port[1]);

        editor.putString(PortSipService.USER_DOMAIN2, m_user_domain[1]);
		
		editor.putBoolean(PortSipService.SIP_ENABLE3, m_sip_enable[2]);

        editor.putString(PortSipService.USER_NAME3, m_user_name[2]);
        editor.putString(PortSipService.USER_PWD3, m_user_pwd[2]);
        editor.putString(PortSipService.SVR_HOST3, m_svr_host[2]);
        editor.putString(PortSipService.SVR_PORT3, m_svr_port[2]);

        editor.putString(PortSipService.USER_DOMAIN3, m_user_domain[2]);		
	
        editor.commit();

		Intent onLineIntent;

		
		onLineIntent = new Intent(getApplicationContext(), PortSipService.class);
		onLineIntent.setAction(PortSipService.ACTION_SIP_UNREGIEST);
		startService(onLineIntent);

		if(m_sip_enable[0])
		{
			onLineIntent.setAction(PortSipService.ACTION_SIP_REGIEST);
			startService(onLineIntent);
		}

		onLineIntent = new Intent(getApplicationContext(), PortSipService2.class);
		onLineIntent.setAction(PortSipService.ACTION_SIP_UNREGIEST);
		startService(onLineIntent);

		if(m_sip_enable[1])
		{
			onLineIntent.setAction(PortSipService.ACTION_SIP_REGIEST);
			startService(onLineIntent);
		}

		onLineIntent = new Intent(getApplicationContext(), PortSipService3.class);
		onLineIntent.setAction(PortSipService.ACTION_SIP_UNREGIEST);
		startService(onLineIntent);

		if(m_sip_enable[2])
		{
			onLineIntent.setAction(PortSipService.ACTION_SIP_REGIEST);
			startService(onLineIntent);
		}
		
    }	
	public void loadUserInfo()
	{
		SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

		m_sip_enable = new boolean[3];
		m_user_name = new String[3];
		m_user_pwd = new String[3];
		m_svr_host = new String[3];
		m_svr_port = new String[3];
		m_user_domain = new String[3];

		m_sip_enable[0] = preferences.getBoolean(PortSipService.SIP_ENABLE, false);

		m_user_name[0] = preferences.getString(PortSipService.USER_NAME, "");
  
		m_user_pwd[0] = preferences.getString(PortSipService.USER_PWD, "");
  
		m_svr_host[0] = preferences.getString(PortSipService.SVR_HOST, getString(R.string.str_default_sip1_server_host));
  
		m_svr_port[0] = preferences.getString(PortSipService.SVR_PORT, getString(R.string.str_default_sip1_server_port));
  
		m_user_domain[0] = preferences.getString(PortSipService.USER_DOMAIN, getString(R.string.str_default_sip1_user_domain));
	
		m_sip_enable[1] = preferences.getBoolean(PortSipService.SIP_ENABLE2, false);
  
		m_user_name[1] = preferences.getString(PortSipService.USER_NAME2, "");
  
		m_user_pwd[1] = preferences.getString(PortSipService.USER_PWD2, "");
  
		m_svr_host[1] = preferences.getString(PortSipService.SVR_HOST2, "");
  
		m_svr_port[1] = preferences.getString(PortSipService.SVR_PORT2, "5060");
  
		m_user_domain[1] = preferences.getString(PortSipService.USER_DOMAIN2, "");
		//editText_port_outbound2.setText(preferences.getString(PortSipService.OUTBOUND_PORT2, "5060"));
  
		m_sip_enable[2] = preferences.getBoolean(PortSipService.SIP_ENABLE3, false);
  
		m_user_name[2] = preferences.getString(PortSipService.USER_NAME3, "");
  
		m_user_pwd[2] = preferences.getString(PortSipService.USER_PWD3, "");
  
		m_svr_host[2] = preferences.getString(PortSipService.SVR_HOST3, "");
  
		m_svr_port[2] = preferences.getString(PortSipService.SVR_PORT3, "5060");
  
		m_user_domain[2] = preferences.getString(PortSipService.USER_DOMAIN3, "");
		//editText_port_outbound3.setText(preferences.getString(PortSipService.OUTBOUND_PORT3, "5060"));
  
	}
	@Override
	public void onCreate() {
		CrashLogCatch.initCrashLog(this);  
		super.onCreate();

		int pid = android.os.Process.myPid();
		String processNamrString = "";
		ActivityManager mActivityMAnager = (ActivityManager)this.getSystemService(getApplicationContext().ACTIVITY_SERVICE);

		for(ActivityManager.RunningAppProcessInfo appProcess : mActivityMAnager.getRunningAppProcesses())
		{
			if(appProcess.pid == pid)
			{
				processNamrString = appProcess.processName;
				break;
			}
		}

		Log.i(TAG,processNamrString);

		if("weema.Android.sip".equals(processNamrString))
		{
			local_onCreate();
		}
		else if("weema.Android.sip:remote".equals(processNamrString))
		{

		}
	}

	private void get_app_info()
	{
		PackageManager pm = getPackageManager();
		String packageName = getPackageName();
		int flags = PackageManager.GET_PERMISSIONS;
		PackageInfo packageInfo = null;

		try {
			packageInfo = pm.getPackageInfo(packageName, flags);
			
			String versionName = packageInfo.versionName;
			int index = versionName.lastIndexOf(".");
			if(index >= 0)
			{
				//m_mgr_number = versionName.substring(index+1);
				//Log.i(TAG,m_mgr_number);	
			}
			Log.i(TAG,versionName);	
      		VERSION = versionName;

		} catch (NameNotFoundException e) {
//			e.printStackTrace();
			Log.e(TAG, e.getMessage());
		}    
	}
	private void setSOSMode(boolean val)
	{
		//if(m_WaService != null)
		//	m_WaService.i2c_reset(val);

		m_is_sos_mode = val;	
		SettingConfig.setSOSMode(getApplicationContext(),val);
		
	}		

	public void setSOSPlay(boolean val)
	{
		m_is_sos_play = val;

		SettingConfig.setSOSPlay(getApplicationContext(),val);
	}		

	public void setCtrlGas(boolean val)
	{
		m_ctrl_gas = val;
		if(m_WaService != null)
			m_WaService.ctrlGasAction(m_ctrl_gas);

		SettingConfig.setCtrlGas(getApplicationContext(),val);
	}	
	public void setRingConfig(String str)
	{
		m_ring_config = str;

		SettingConfig.setRingConfig(getApplicationContext(),str);
	}
	public void setDoorNotify(boolean is_enable)
	{
		m_is_door_notify = is_enable;
		SettingConfig.setDoorNotify(getApplicationContext(),is_enable);
	}
	public void setSOSTalkEnable(boolean is_enable)
	{
		m_sostalk_enable = is_enable;
		SettingConfig.setSOSTalkEnable(getApplicationContext(),is_enable);
	}		
	public void setDoorRecord(boolean is_enable)
	{
		m_video_record_enable = is_enable;
		SettingConfig.setDoorRecord(getApplicationContext(),is_enable);
	}
	public void send_update()
	{
		if(m_WaService != null)
			m_WaService.send_update();
	}
	public void setDoorIP(String ip)
	{
		m_door_ip = ip;
		SettingConfig.setDoorIP(getApplicationContext(),ip);
	}
	public void door_notify()
	{
		if(m_zone[0])   return;

		if(m_is_door_notify == false)   return;
		if(myContext == null)    return;

		Ring.getInstance(myContext).startDoorBellTone();
		
	}
	public void re_getstate()
	{
		m_ring_first = true;
		m_ring_cnt = 1;

	}
	public void process_delayLock()
	{
		if(m_sd && m_sd_timer > 0)
		{
			m_sd_timer--;

			if(m_sd_timer == 0)
			{
				int win = check_zone();

				if(win >= 0)
				{
					m_message = getResources().getString(R.string.str_need_lock)+" "+String.valueOf(win+1);
	
					//showTips(getResources().getString(R.string.str_need_lock)+" "+String.valueOf(win+1));
					if(m_WaService != null)
						m_WaService.openBZShort();
					return;
				}
				else
				{
					if(m_WaService != null)
						m_WaService.openBZLong();
				}

				m_message = "delay lock ";
				openLED();
		
				m_message = m_message+"設定成功";

				m_is_lock = true;
				SettingConfig.setLock(myContext,m_is_lock);
				if(m_WaService != null)
		  			m_WaService.send_update();
				update_lock();
			}
		}      
	
	}	
	public void process_doorphone()
	{
		if(m_ring_enable != false && m_ring_first != false && m_ring_cnt > 0)
		{
			m_ring_cnt--;

			if(m_ring_cnt == 0)
			{
		  		m_ring_cnt = MyApplication.MAX_RING_CNT;
				if(m_WaService != null)
					m_WaService.get_phone_sos();
			}
		}      
	
	}

	public boolean checkIPCam(String call_number)
	{
		m_is_ipcam = false;

		m_path = "";

		boolean ret;

		ret = false;
		if(TextUtils.isEmpty(call_number))
			return ret;
		if(ipcam_arr == null || ipcam_arr.length == 0)    return ret;
			
		for (String strTemp : ipcam_arr){
			Log.i(TAG,"checkIPCam "+strTemp);
			String[] ele = strTemp.split(",");

			if(ele == null || ele.length < 5)  continue;

			if(ele[0].equals("0"))
			     continue;

			showTips("ipcam "+call_number);
			if(ele[1].equals(call_number) == false)    continue;

			if(ele[2].equals("2"))
			{
				m_is_ipcam = true;
				m_path = "rtsp://"+ele[3]+":"+ele[4]+"/video0.sdp";
				
				ret = true;
				break;
			}	 

		}

		Log.i(TAG,m_path);

		return ret;
	}	

	public void setWaService(WaService service)
	{
		m_WaService = service;
	}
	public void setVideoIP(String ip)
	{
		m_video_ip = ip;

        SettingConfig.setVideoIP(getApplicationContext(),ip);
	}
	public void setVoice()
	{
		isDaoLan = !isDaoLan;

        SettingConfig.setVoice(getApplicationContext(),isDaoLan);
	}

	public void setRecordEnable(boolean is_enable)
	{
		is_record_enable = is_enable;

        SettingConfig.setRecordEnable(getApplicationContext(),is_enable);
	}
	public static boolean is_valid(String str)
	{
		if(str == null)    return false;
			Matcher matcher = IP_ADDRESS.matcher(str);
      	if (matcher.matches()) {
           // ip is correct
			return true;
     	}
		else
		    return false;
	}
	public void setVideoParam(Session cur)
	{
		PortSipSdk mysdk;
		mysdk = getVoipLine(cur.voip_line);

		mysdk.setVideoBitrate(cur.getSessionId(),1024);
		mysdk.setVideoFrameRate(cur.getSessionId(),30);
		mysdk.setVideoResolution(1280,720);

	}

	public void muteSession(Session cur,boolean is_mute)
	{
		if(is_mute && m_is_monitor_call == false)    return;

		PortSipSdk mysdk;
    	mysdk = getVoipLine(cur.voip_line);

		int ret;

		ret = mysdk.muteSession(cur.getSessionId(),false,is_mute,false,false);
		
    	Log.i(TAG,"mute "+String.valueOf(ret)+" "+String.valueOf(is_mute));
	}
	public void onPlayAudioFileFinished(int voip_line,long sessionId, String fileName)
	{
		Log.i(TAG,fileName);
	}
	public void openLED()
	{
		if(m_WaService != null)
			m_WaService.openLED();
	}
	public void closeLED()
	{
		if(m_WaService != null)
			m_WaService.closeLED();
	}
	public void openBZ()
	{
		if(m_WaService != null)
			m_WaService.openBZ();
	}
	public void closeBZ()
	{
		if(m_WaService != null)
			m_WaService.closeBZ();
	}
	
	public boolean toggleLock()
  	{
		boolean ret;

		ret = false;

		boolean is_lock;

	    if(m_is_lock)
		{
			unLockAlarm();
			is_lock = false;
			
		}
		else
		{
			is_lock = true;

		}

		if(myContext == null) return ret;

		ret = setLock(myContext,is_lock);

     	if(ret != false)
      	{
        	update_lock();
      	}
		return ret;
  	}
	public void set_sos_state(int index,int value)
	{
		m_sos_state[index] = value;
	}
	public int get_sos_state(int index)
	{
		return m_sos_state[index];
	}
  	public void ring_get_reply(String[] str_arr)
	{
		if(str_arr.length < 5)    return;
		Intent i = new Intent(MyApplication.RING_GET_REPLY);
		i.putExtra(MyApplication.EXTRA_RING_ENABLE, str_arr[1]);
		i.putExtra(MyApplication.EXTRA_RING_NUMBER, str_arr[2]);
		i.putExtra(MyApplication.EXTRA_RING_MIC, str_arr[3]);
		i.putExtra(MyApplication.EXTRA_RING_VOLUME, str_arr[4]);

		String phone_ip="";

		if(str_arr.length >= 6)
		{
			phone_ip = str_arr[5];
		}

		i.putExtra(MyApplication.EXTRA_PHONE_IP, phone_ip);
		sendBroadcast(i);

	}

	public void ring_get_reply(String enable,String number)
	{
		Intent i = new Intent(MyApplication.RING_GET_REPLY);
		i.putExtra(MyApplication.EXTRA_RING_ENABLE, enable);
		i.putExtra(MyApplication.EXTRA_RING_NUMBER, number);

		sendBroadcast(i);

	}

	public void ring_set_reply()
	{
		Log.i(TAG,"ring_set_reply");
		Intent i = new Intent(MyApplication.RING_SET_REPLY);
		//i.putExtra(MyApplication.EXTRA_SOS_ALARM, id);
		sendBroadcast(i);
	}
	public void show_lock_pingAction()
	{
		Log.i(TAG,"show_lock_pingAction");
		if(m_is_debug == false)    return;

		Intent i = new Intent(MyApplication.SHOW_LOCK_PINACTION);
		//i.putExtra(MyApplication.EXTRA_SOS_ALARM, id);
		sendBroadcast(i);				
	}
	private void show_door_ringAction()
	{
		Log.i(TAG,"show_door_ringAction");
		if(m_is_debug == false)    return;
		Intent i = new Intent(MyApplication.SHOW_DOOR_RINGACTION);
		//i.putExtra(MyApplication.EXTRA_SOS_ALARM, id);
		sendBroadcast(i);		
	}
	private void show_mdoor_action()
	{
		Log.i(TAG,"show_mdoor_action");
		if(m_is_debug == false)    return;
		Intent i = new Intent(MyApplication.SHOW_MDOOR_ACTION);
		//i.putExtra(MyApplication.EXTRA_SOS_ALARM, id);
		sendBroadcast(i);		
	}	
	public void door_ringAction()
	{
       	//showTips("ring");

		show_door_ringAction();
		if(m_is_sos_mode == false)    return;

    	if(myContext != null && m_ring_enable != false && !TextUtils.isEmpty(m_ring_number))
        {
			boolean status = CallManager.Instance().getregist(1);
			if(status == false)    return;
			
			Session currentLine = CallManager.Instance().getCurrentSession();
			if(currentLine == null || currentLine.IsIdle())
			{
            	long ret = outgoingCall(true,m_ring_number);

				if(ret < 0)    return;

				Ring.getInstance(myContext).startRingBackTone();
				Intent activityIntent;
		 
				activityIntent = new Intent(myContext, MainActivity.class);
				//activityIntent.putExtra("incomingSession",sessionId);
				activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
				activityIntent.putExtra(PortSipService.EXTRA_CALL_SEESIONID, ret);
				startActivity(activityIntent);
			}
		}

	}

	public void lockPinAction()
	{
		toggleLock();
			
	}
	
	public int get_alarm_id()
	{
		int id;

		id = -1;

		if(myContext == null) return id;
		for(int i=0;i<((BaseMainActivity)myContext).m_alarm.length;i++)
		{
			if(((BaseMainActivity)myContext).m_alarm[i] != false)
			{
				id = i;
				break;
			}
		}

    	m_sos_id = id;

		return id;
	}
	public int unLockAlarm()
	{
		int id;
		id = -1;

		if(myContext == null) 
		{
			return id;
		}
		Log.i(TAG,"sos_id "+String.valueOf(m_sos_id));
		
		if(m_sos_id >= 0 && m_sos_id < 5)
		{
			setLock(myContext,false);
		}

       	if(m_sos_id >= 0)
		{
			if(m_sos_id == 6)
			{
				setCtrlGas(false);
			}
		    m_sos_state[m_sos_id] = MyApplication.SOS_IDLE_STATE;
		    ((BaseMainActivity)myContext).m_alarm[m_sos_id] = false;
       	}

		id = get_alarm_id();

		if(id < 0)
		{
		 	closeBZ();
		}

		return id;

	}

	private void add_list(String number)
	{
		myList.add(number);

		String str;
      	str = null;
		int size = myList.size();
		for(int i=0;i<size;i++)
		{
			if(str == null)
				str = myList.get(i);
			else
				str = str + " "+myList.get(i);
		}
      	Log.i(TAG,"add list "+number);
		SettingConfig.setDoorList(getApplicationContext(),str);
	}
  	public boolean onRecvDtmfTone(int line_id,long session_id,int tone) {
		boolean ret;
		ret = false;

    	if(myContext == null) return ret;

		m_dtmf_recv[m_dtmf_index++] = tone;
		if(m_dtmf_index >= MAX_DTMF_RECV)    m_dtmf_index = 0;

    	if(m_dtmf_index >= m_str_passwd.length())
		{
			String arrayString = Arrays.toString(m_dtmf_recv).replaceAll("\\[|\\]|,|\\s", "");
			if(arrayString.indexOf(m_str_passwd) == 0
			)
			{
				int id;

				alarm_msg_send("1",m_sos_id);
				id = unLockAlarm();

				if(id >= 0)
				{
					alarm_msg_send("3",id);
            		((BaseMainActivity)myContext).playSOSAlarmWav(line_id,session_id);
				}

			}
			else
			{
				m_dtmf_index = 0;
			}

		}

		return ret;
	}

	public void updateMDoor(int state)
	{
		m_mdoor_state = state;

		if(myContext == null) return ;

      	int size = myList.size();
		if(size == 0)    return;

      	String msg = "SETMDOOR&"+String.valueOf(m_door_state);

      	ArrayList<String> list;
		list = new ArrayList<String> ();
		for(int i=0;i<size;i++)
		{
			String number = myList.get(i);
        	list.add(number);

		}

		for(int i=0;i<size;i++)
		{
			String number = list.get(i);

			sendSIPMessage(1,number,msg);
		}

	}
	private void do_mdoorAction(int state)
	{
		doorAction(state);
		updateMDoor(state);
		String status;

		if(state == WaService.CLOSE_STATE)
		{
			status = "1";
		}
		else
		{
			status = "3";
		}
		alarm_msg_send(status,WaService.DOOR_PIN);
	}
	private static final int MDOOR_VOIP_LINE=1;
	public boolean mdoorAction(int state)
	{
		boolean ret;

		ret = true;
	
		show_mdoor_action();
		//if(m_is_sos_mode == false)    return ret;
		if(isOnline(MDOOR_VOIP_LINE) == false)  return ret;
	
		//String str = myApp.getLocalIP(false);
		if(m_is_mdoor != false)
		{
		  do_mdoorAction(state);
	
		}		

		return ret;

	}


	public void ringSetAction(String[] str_arr)
	{
		if(str_arr.length < 7)    return;

		m_ring_enable = Boolean.parseBoolean(str_arr[2]);
		m_ring_number = str_arr[3];
	
        mCurMicVol = Utility.getNumber(str_arr[4]);
		if(mCurMicVol < 0)
			mCurMicVol = 0;
		else if(mCurMicVol > 10)
			mCurMicVol = 10;

		Session currentLine = CallManager.Instance().getCurrentSession();
		if(currentLine != null && !currentLine.IsIdle())
		{
			currentLine.mic_value = m_mic_scaling[mCurMicVol];
			adjust_mic_vol(currentLine);
		}
		else 
		{

			SettingConfig.setMIC(getApplicationContext(),mCurMicVol);
		}

		int val = Utility.getNumber(str_arr[5]);
    	setVolume(val);

		m_ring_phone_ip = str_arr[6];

	    SettingConfig.setRing(getApplicationContext(),m_ring_enable,m_ring_number,m_ring_phone_ip);
	
	}
	public void doorAction(int state)
	{
		m_door_state = state;
		//String data = msg.substring(msg.indexOf("REEP ")+5);
		Intent i = new Intent(MyApplication.DOOR_ACTION);

		sendBroadcast(i);


	}
	private void doorActionCall(int state)
	{
		//String data = msg.substring(msg.indexOf("REEP ")+5);
		Intent i = new Intent(MyApplication.DOOR_ACTION_CALL);
		i.putExtra(MyApplication.EXTRA_KEY, state);
		sendBroadcast(i);


	}

	public void presskeyReceiver(int id) {
		
		Intent i = new Intent(MyApplication.PRESSKEY_ACTION);
		i.putExtra(MyApplication.EXTRA_KEY, m_key_map[id]);
		sendBroadcast(i);
		
	  
	}
	public void dataReceiver(int id) {
		
		if(m_ring_enable)    return;

		LCD_on();
		m_sos_state[id] = MyApplication.SOS_ALARM_STATE;

		Intent activityIntent = new Intent(getApplicationContext(), MainActivity.class);
		//activityIntent.putExtra("incomingSession",sessionId);
		activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		activityIntent.putExtra(MyApplication.EXTRA_SOS_ALARM, id);
		startActivity(activityIntent);				
	  
	}

	public boolean check_alarm()
	{
		boolean ret = false;
		for(int i=0;i<8;i++)
		{
			if(m_sos_state[i] == SOS_ALARM_STATE)
			{
				ret = true;
			}

		}

		return ret;
	}
	public String getNextSOSCall()
	{
		String str=null;

		if(m_sos_index >= 5)
		{
          m_sos_index = 0;
		}

		for(int i=m_sos_index;i<5;i++)
		{
          	if(m_sos_enable[i] != false)
			{
				if(m_sos_select_line[i] > 0)
				{
					str = String.valueOf(m_sos_select_line[i]+1)+"*"+m_sos_number[i];

				}
            	else
					str = String.valueOf(m_sos_number[i]);

            //str = "1013";
				m_sos_index = (i+1);
				break;
			}
		}

		if(str == null){
			m_sos_index = 0;
		}
		return str;
	}
	public boolean setAlarmMSg(Context context,boolean is_enable,String number)
	{
		m_alarm_msg_enable = is_enable;
		m_alarm_msg_number = number;
	    SettingConfig.setAlarmMsg(context,is_enable,number);

		return true;
	}

	public String get_sos_output()
	{
		String str;

		if(m_sos_id >= 0)
		{
			str = "1,"+String.valueOf(m_sos_id);
		}
		else 
		{
			str = "0";
		}

		return str;
	}
	public boolean setCenterIP(Context context,String str_ip)
	{
		m_center_ip = str_ip;
	    SettingConfig.setCenterIP(context,str_ip);

		return true;
	}
  	public void onSendOutOfDialogMessageFailure(int line_id,String to)
	{
		int ret;

		ret = in_mdoor_list(to);
		if(ret >= 0)
		{
        	myList.remove(ret);
		}
		if(to.equals(m_mdoor_send))
		{
			m_mdoor_send_ret = -1;
		}
	}
  	public void onSendOutOfDialogMessageSuccess(int line_id,String to)
	{

	}
  	public void getFromMDoor()
	{
		if(m_is_mdoor == false && m_mdoor_enable != false)
		    getFromMDoor(m_mdoor_number);
	}
  	public void getFromMDoor(String number)
	{
		int voip_line;

    	if(TextUtils.isEmpty(number))    return;

		voip_line = 1;

		if(isOnline(voip_line) == false)
		{
			showTips(getResources().getString(R.string.str_online_err));
			return ;
		}

		SharedPreferences prefences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

		String domain = prefences.getString(PortSipService.USER_DOMAIN, "");
		//String port = prefences.getString(SVR_PORT, "5060");

		if(TextUtils.isEmpty(domain))    return;

		String str = number+"@"+domain;

    	m_mdoor_send = str;
		str = "sip:"+str;
		Log.i(TAG,str);
		sendSIPMessage(voip_line,str,"GETMDOOR");

		m_mdoor_send_ret = 0;

	}

	public boolean setMonitor(Context context,boolean is_enable,String number)
	{
		m_monitor_enable = is_enable;
		m_monitor_number = number;
		SettingConfig.setMonitor(context,is_enable,number);

		return true;
	}
	public boolean setMDoor(Context context,boolean is_enable,String number,boolean is_mdoor)
	{
		m_is_mdoor = is_mdoor;
		m_mdoor_enable = is_enable;
		m_mdoor_number = number;
	    SettingConfig.setMDoor(context,is_enable,number,is_mdoor);

		return true;
	}
	public void updateLimitcall()
	{
		((BaseMainActivity)myContext).do_get_config();
		((BaseMainActivity)myContext).do_get_ipcam();
	}
  	public void setLimitcall(Context context,String allow,boolean is_enable,int[] night_time,String str_call)
	{
		this.allow = allow;
		night_enable = is_enable;
		this.night_time = night_time;
		night_call = str_call;
		SettingConfig.setLimitcall(context,allow,is_enable,night_time,night_call);
		  
	}	


	public String getSOSInfo()
	{
		String str;
		str = "";
		for(int i=0;i<m_sos_enable.length;i++)
		{
			if(m_sos_enable[i])
			{
				str = str +"1,";
			}
			else 
			{
				str = str +"0,";
			}

			str = str +String.valueOf(m_sos_select_line[i])+",";
			str = str+m_sos_number[i]+",";

		}

		str = str+m_str_passwd+",";
		return str;
	}
	public String getOtherInfo()
	{
		String str;
		str = m_video_ip+",";
		
		if(m_monitor_enable)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}
		str = str+m_monitor_number+",";
		str = str+m_center_ip+",";

		if(m_alarm_msg_enable)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}
	
		str = str+m_alarm_msg_number+",";
	
		if(is_record_enable)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}
		
		if(m_is_door_notify)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}	
	
		if(m_sd)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}			

		if(m_sostalk_enable)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}			

		if(m_ctrl_gas)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}			

		if(m_is_sos_play)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}			
			
		if(m_agc)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}		

		if(m_is_sos_mode)
		{
			str = str +"1,";
		}
		else 
		{
			str = str +"0,";
		}		

		String str_bz = String.valueOf(m_ctrl_bz);
		str = str +str_bz+",";
		
		return str;
	}		
	public String getEthernetInfo()
	{
		read_ethernet_file();
		String str;
		str = m_ipaddr+","+m_netmask+","+m_gateway+","+m_dns+",";
		
		return str;
	}		

	public String getDoorInfo()
	{
		String str;
		str = "";

		if(m_is_mdoor)
			str = str+"1,";
		else 
			str = str+"0,";

		if(m_mdoor_enable)
			str = str+"1,";
		else 
			str = str+"0,";
	
		str = str+m_mdoor_number+",";

		if(m_ring_enable)
			str = str+"1,";
		else 
			str = str+"0,";
		
		str = str+m_ring_number+",";
		str = str+m_ring_phone_ip+",";

		// MIC
		str = str+String.valueOf(mCurMicVol)+",";
		// 喇叭音量
		str = str+String.valueOf(m_volume)+",";
		// 鈴聲音量
		str = str+String.valueOf(m_ring_volume)+",";
		// 喇叭音量硬體
		int volume_hw = getVolume();
		str = str+String.valueOf(volume_hw)+",";

		return str;
	}	
	public String getTransferInfo()
	{
		String str;
		str = "";

		if(misEnableFWD)
		{
			str = "1,";
		}
		else 
		{
			str = "0,";
		}
		
		str = str+String.valueOf(mtypeFWD)+",";

		str = str+mnumberFWD+",";
		str = str+String.valueOf(mtimeoutFWD)+",";
	
	
		return str;
	}		
	public String getZoneInfo()
	{
		String str;
		str = "";
		for(int i=0;i<m_zone.length;i++)
		{
			if(m_zone[i])
			{
				str = str +"1,";
			}
			else 
			{
				str = str +"0,";
			}
		}
		return str;
	}	
	public void do_setMail()
	{
		SettingConfig.setMailContent(getApplicationContext(),mail_content);
		if(myContext != null)
		{
            Session currentLine = CallManager.Instance().getCurrentSession();
           
            if(currentLine != null && currentLine.IsIdle() != false)
            {
                Log.i(TAG,"onresume1");
                
				//((MainActivity)myContext).loadConsoleActivity();
				update_mail();

			}
		}	
	}		
	public void do_setEthernet()
	{
		Log.i(TAG,"do_setEthernet reboot()");
		reboot();
	}		

	public void do_setOther()
	{
		setVideoIP(m_video_ip);
		SettingConfig.setMonitor(getApplicationContext(),m_monitor_enable,m_monitor_number);

		setCenterIP(getApplicationContext(),m_center_ip);
		
		setAlarmMSg(getApplicationContext(),m_alarm_msg_enable,m_alarm_msg_number);
		
		setRecordEnable(is_record_enable);
		
		setDoorNotify(m_is_door_notify);
		
		setSD(getApplicationContext(),m_sd);	
		
		setSOSTalkEnable(m_sostalk_enable);

		setCtrlGas(m_ctrl_gas);
		setSOSPlay(m_is_sos_play);
		setSOSMode(m_is_sos_mode);
		setCtrlBZ(m_ctrl_bz);
					
		
	}		
	public void do_setDoor()
	{
		Log.i(TAG,"do_setDoor()");
		Log.i(TAG,"setMDoor: " + m_mdoor_enable + " " + m_mdoor_number + " " + m_is_mdoor);
		Log.i(TAG,"setRing: " + m_ring_enable + " " + m_ring_number + " " + m_ring_phone_ip);
		Log.i(TAG,"setMIC: " + mCurMicVol);
		Log.i(TAG,"setVolume: " + m_volume);
		Log.i(TAG,"setRingVolume: " + m_ring_volume);

		SettingConfig.setMDoor(getApplicationContext(),m_mdoor_enable,m_mdoor_number,m_is_mdoor);
		SettingConfig.setRing(getApplicationContext(),m_ring_enable,m_ring_number,m_ring_phone_ip);
		SettingConfig.setMIC(getApplicationContext(),mCurMicVol);
		// 2024-12-26 修正 ptool 喇叭音量沒有設定成功的問題
		SettingConfig.setVolume(getApplicationContext(),m_volume);
		setVolume(m_volume);
		if(myContext != null)
			Ring.getInstance(myContext).setRingVolume(m_ring_volume);
				
	}	
	public void do_setTransfer()
	{
		SettingConfig.setEnableFWD(getApplicationContext(),misEnableFWD);
		
		StringBuffer sb = new StringBuffer();
		boolean a,b,c;
		String number_a,number_b,number_c;
		a=b=c=false;
		number_a=number_b=number_c="";

		if(mtypeFWD == ALL_FWD)
		{
			a = true;
			number_a = mnumberFWD;
		}
		else if(mtypeFWD == BUSY_FWD)
		{
			b = true;
			number_b = mnumberFWD;			
		}
		else 
		{
			c = true;
			number_c = mnumberFWD;				
		}
		sb.append(a);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(b);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(c);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(false);
		sb.append("-");
		sb.append(number_a + "#");
		sb.append("-");
		sb.append(number_b + "#");
		sb.append("-");
		sb.append(number_c + "#");
		sb.append("-");
		sb.append(String.valueOf(mtimeoutFWD) + "#");

    	SettingConfig.setFWD(getApplicationContext(),sb.toString());

		Intent broadIntent = new Intent(FWD_CHANGE_ACTION);
		sendBroadcast(broadIntent);

	}		
	public void do_setSOS()
	{
		Context context = getApplicationContext();
		SettingConfig.setEnableSOS(context,m_sos_enable);
		SettingConfig.setSelectLine(context,m_sos_select_line);
		SettingConfig.setSOSNumber(context,m_sos_number);	
		
		SettingConfig.setLockPasswd(context,m_str_passwd);
	}
  	public void setSOS(Context context,boolean[] is_enable,int[] select_line,String[] number)
	{
		m_sos_enable = is_enable;
		m_sos_select_line = select_line;
		m_sos_number = number;
		do_setSOS();
	}
	public int check_zone()
	{
		int i;
      	int ret;

		ret = -1;
		if(m_WaService == null)    return ret;
      	if(m_WaService.m_i2c_ret != null && m_WaService.m_i2c_ret.length >= 5)
		{
			for(i=0;i<5;i++)
			{
            	if((m_zone[i] != false) && (m_WaService.m_i2c_ret[i] == WaService.OPEN_STATE))
				{
					ret = i;
					break;
				}
			}
		}

		return ret;

	}

	private void sendPortSipMessage(Service service,String message, Intent broadIntent) {
        NotificationManager mNotifyMgr = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        Intent intent = new Intent(service, BaseMainActivity.class);
        PendingIntent contentIntent = PendingIntent.getActivity(service, 0, intent, 0);

        Notification.Builder builder;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            builder = new Notification.Builder(service,PortSipService.ChannelID);
        }else{
            builder = new Notification.Builder(service);
        }
        builder.setSmallIcon(R.drawable.icon)
                .setContentTitle("Sip Notify")
                .setContentText(message)
                .setContentIntent(contentIntent)
                .build();// getNotification()

        mNotifyMgr.notify(1, builder.build());

        sendBroadcast(broadIntent);
    }
	public void onInviteIncoming(long sessionId,
                            String callerDisplayName,
                            String caller,
                            String calleeDisplayName,
                            String callee,
                            String audioCodecNames,
                            String videoCodecNames,
                            boolean existsAudio,
                            boolean existsVideo,
							String sipMessage,
							int sdk_id
							)
	{
		LCD_on();
		PortSipSdk engine;
		engine = getVoipLine(sdk_id);
		Service service;
		service = getSdkService(sdk_id);
        if(CallManager.Instance().findIncomingCall()!=null){
            engine.rejectCall(sessionId,486);//busy
            return;
        }
        if(getEnableFWD())
     	{
     		String number = getNumberFWD();
     		int type =getTypeFWD();
            
     		if(type == MyApplication.ALL_FWD)
     		{
     			Log.i(TAG,number);
     			engine.forwardCall(sessionId,number);
     			return;

     		}
     		else if(type == MyApplication.BUSY_FWD)
     		{
                Session currentline = CallManager.Instance().getCurrentSession();
                if(currentline != null && !currentline.IsIdle())
     			{
     				Log.i(TAG,number);
     				engine.forwardCall(sessionId,number);
     				return;

     			}
     		}
     	}

        Session cur = CallManager.Instance().getCurrentSession();
        Session session;
        if(cur != null && cur.IsIdle())
        {
          session = cur;
        }
        else
        {
            session = CallManager.Instance().findIdleSession();
        }
        session.state = Session.CALL_STATE_FLAG.INCOMING;
        session.HasVideo = existsVideo;
        session.SessionID = sessionId;
        session.Remote = caller;
        session.DisplayName = callerDisplayName;
        session.voip_line = sdk_id;

        if(cur != session)
    	{
			Log.i(TAG,"startCallWaitingTone");
    		Ring.getInstance(service).startCallWaitingTone();
           

    		addCurrentLine(session);
    	}else
        {
            CallManager.Instance().setCurrentLine(session);

            if(getDND() == false)
			{
				Ring.getInstance(service).startRingTone(session);
			}
        }
		//adjust_mic(session);
     
       if(inCall() == false)
       {
           Intent activityIntent;
        
            activityIntent = new Intent(service, MainActivity.class);
           	//activityIntent.putExtra("incomingSession",sessionId);
           	activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
           	activityIntent.putExtra(PortSipService.EXTRA_CALL_SEESIONID, sessionId);
           	service.startActivity(activityIntent);
       }
       else
       {
        	Intent broadIntent = new Intent(PortSipService.CALL_CHANGE_ACTION);
        	broadIntent.putExtra(PortSipService.EXTRA_CALL_SEESIONID, sessionId);

        	String description = session.LineName + " onInviteIncoming";
        	broadIntent.putExtra(PortSipService.EXTRA_CALL_DESCRIPTION, description);

        	sendPortSipMessage(service,description, broadIntent);
       }

	}
	public boolean setMS(Context context,boolean val)
	{
		m_ms = val;
		return SettingConfig.setMS(context,val);

	}
	public boolean setSD(Context context,boolean val)
	{
		m_sd = val;
		return SettingConfig.setSD(context,val);

	}
	public boolean do_setZone()
	{
      	return SettingConfig.setZone(getApplicationContext(),m_zone);
	}	
	public boolean setZone(Context context,boolean[] val)
	{
		m_zone = val;
		return do_setZone();
	}
	public boolean setLockPasswd(Context context,String val)
	{
		m_str_passwd = val;
      	return SettingConfig.setLockPasswd(context,val);
	}
	public boolean setLockFromRemote(boolean val)
	{
		if(val == false)
		{
			unLockAlarm();
			//setLock(myContext,val);

			//return true;
		}
		boolean ret = setLock(myContext,val);

		if(ret)
		{
			//Toast.makeText(getApplicationContext(),"3 you have a mesaage ",Toast.LENGTH_LONG).show();
			Intent broadIntent = new Intent(PortSipService.AUTO_ONHOOK_ACTION);
			broadIntent.putExtra(PortSipService.EXTRA_ONHOOK, 0);
			sendBroadcast(broadIntent);
			
			//((MainActivity)myContext).loadConsoleActivity();	
			update_lock();
		}
	

		return ret;
	}
	public void update_lock()
	{
		Intent i = new Intent(MyApplication.LOCK_ACTION);
		
		sendBroadcast(i);		
		
	}
	public void update_mail()
	{
		Intent i = new Intent(MyApplication.MAIL_ACTION);
		
		sendBroadcast(i);		
		
	}	
	public boolean setLock(Context context,boolean val)
	{
		m_sd_timer = 0;
		if(val == true && m_sd)
		{
			m_sd_timer = MAX_DELAY_TIME;
			return true;
		}

       if(val)
		{
			int win = check_zone();

			if(m_sd == false && win >= 0)
			{
				m_message = getResources().getString(R.string.str_need_lock)+" "+String.valueOf(win+1);

				//showTips(getResources().getString(R.string.str_need_lock)+" "+String.valueOf(win+1));
				if(m_WaService != null)
					m_WaService.openBZShort();
				return false;
			}
			else
			{
				if(m_WaService != null)
					m_WaService.openBZLong();
			}

		}
	
		m_is_lock = val;
       	if(m_is_lock)
		{
			m_message = "lock ";
			openLED();
		}
		else
		{
			m_message = "unlock ";
			closeLED();
		}
		
		m_message = m_message+"設定成功";

		return SettingConfig.setLock(context,val);
	}
	public void setMyContext(Context context)
	{
		boolean is_first;

		is_first = false;
		if(myContext == null)
			is_first = true;

		myContext = context;

		if(is_first)
			m_ring_volume = Ring.getInstance(myContext).getRingVolume();
		
	}
  	public void setRegTips(int line,String tips)
	{
		mregtips[line] = tips;
	}
	public void ForwardCall(Session cur)
	{
		PortSipSdk mysdk;
    	mysdk = getVoipLine(cur.voip_line);
		//Log.i("myApp","ForwardCall");
		mysdk.forwardCall(cur.getSessionId(),getNumberFWD());
	}

	public void getConfigFWD()
	{
		misEnableFWD = SettingConfig.getEnableFWD(getApplicationContext());

		String data = SettingConfig.getFWD(getApplicationContext());

		if (data != null && !data.equals("")) {

			String[] arr_Str = data.split("-");

			Boolean ck_A,ck_B;
       		String num_A,num_B,num_C,num_Timeout;

			ck_A = Boolean.parseBoolean(arr_Str[0]);
			ck_B = Boolean.parseBoolean(arr_Str[4]);

			num_A = arr_Str[12].replace("#", "");
       		num_B = arr_Str[13].replace("#", "");
       		num_C = arr_Str[14].replace("#", "");
			num_Timeout = arr_Str[15].replace("#", "");

			if(ck_A)
			{
				mtypeFWD = ALL_FWD;
				mnumberFWD = num_A;
			}
			else if(ck_B)
			{
				mtypeFWD = BUSY_FWD;
				mnumberFWD = num_B;
			}
		 	else
			{
				mtypeFWD = TIMEOUT_FWD;
				mnumberFWD = num_C;
				mtimeoutFWD = Utility.getNumber(num_Timeout);
			}
		}
		else
		{
			mtypeFWD = ALL_FWD;
			mnumberFWD = "";
		}

	}

	public boolean getEnableFWD()
	{
		return misEnableFWD;

	}

	public int getTimeoutFWD()
	{
		if(getEnableFWD())
		    return mtimeoutFWD;

		return 0;

	}

	public String getNumberFWD()
	{
		String new_str;

		if(mnumberFWD.indexOf('.') >= 0)
		{
			new_str = "sip:"+mnumberFWD;

		}
		else
		{
			new_str = mnumberFWD;
		}

		return new_str;

	}

	public int getTypeFWD()
	{
		return mtypeFWD;

	}
	private void delete_file(String path)
	{
		File myfile = new File(path);
		if(myfile != null && myfile.exists())
		{
			Log.i(TAG,"delete_file "+path);
			myfile.delete();
		}
	}
	private void delete_files(String path)
	{
	  Log.i(TAG,path);
	  File myfile = new File(path);
  
	  File[] file_list = myfile.listFiles();    
	
	  Arrays.sort(file_list);
  
	  int i=0;
	  for(File file1:file_list){
		Log.i(TAG,file1.getName());
		//Toast.makeText(RecordActivity.this,file1.getName(), Toast.LENGTH_LONG)
		//.show();
		if(file1.getName().lastIndexOf("avi") < 0)    continue;
  
		if(i < 10)
		{
			file1.delete();
		}
		else 
			break;
  
		i++;
	  }   
	}    
   
	private boolean is_door_calling(Session session)
	{
		boolean ret;

		ret = false;
		if(m_monitor_enable == false)    return ret;

		String caller = Utility.getRemoteCaller(session.Remote);
	
		if(m_monitor_number.equals(caller))
		{
			ret = true;
		}

		return ret;
	
	}

	private void check_config()
	{
		byte[] arr = {

0x00, 0x00, 0x00, 0x02, 0x00, 0x0c, 0x69, 0x7D, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 
0x6e, 0x74, 0x00, 0x06, 0x53, 0x54, 0x41, 0x54, 0x49, 0x43, 0x00, 0x0b, 0x6c, 0x69, 0x6e, 0x6b,
0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x00, 0x0D, 0x31, 0x39, 0x32, 0x2e, 0x31, 0x36, 0x38,
0x2e, 0x30, 0x2e, 0x31, 0x32, 0x32, 0x00, 0x00, 0x00, 0x10, 0x00, 0x07, 0x67, 0x61, 0x74, 0x65, 
0x77, 0x61, 0x79, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x0b, 0x31, 0x39, 0x32, 
0x2e, 0x31, 0x36, 0x38, 0x2e, 0x30, 0x2e, 0x31, 0x00, 0x03, 0x64, 0x6e, 0x73, 0x00, 0x0b, 0x31, 
0x39, 0x32, 0x2e, 0x31, 0x36, 0x38, 0x2e, 0x30, 0x2e, 0x31, 0x00, 0x03, 0x64, 0x6e, 0x73, 0x00,
0x07, 0x30, 0x2e, 0x30, 0x2e, 0x30, 0x2e, 0x30, 0x00, 0x0d, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x53, 
0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x00, 0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x00, 0x02, 0x69, 0x64,
0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x65, 0x6f, 0x73

		};

		read_from_config(arr);

		Log.i(TAG,"config "+m_ipaddr);
		Log.i(TAG,"config "+m_netmask);
		Log.i(TAG,"config "+m_gateway);
		Log.i(TAG,"config "+m_dns);

	}
	private void read_from_config(byte[] arr)
	{
	   int index = 0;
	   
	   int len;
  
	   index = 5;
	   len = arr[index];
	   byte[] b_arr = new byte[30];
	   index++;

	   index = index+len+1;
  
	   len = arr[index];
	   Log.i(TAG,"config1 "+String.valueOf(len));
	   index++;
	   for(int i=0;i<len;i++)
	   {
		b_arr[i] = arr[index+i];
	   }
	   b_arr[len] = 0;
  
	   String type = new String(b_arr);
	   type = type.trim();
	   Log.i(TAG,"config2 "+type);
	   if(!type.equals("STATIC"))    return;
  
	   index = index+len+1;
	   len = arr[index];
	   Log.i(TAG,"config3 "+String.valueOf(len));
	   index++;
	   for(int i=0;i<len;i++)
	   {
		b_arr[i] = arr[index+i];
	   }
  
	  index = index+len+1;
	  len = arr[index];
	  index++;
	  for(int i=0;i<len;i++)
	  {
	   b_arr[i] = arr[index+i];
	  }
  
	  b_arr[len] = 0;
	  m_ipaddr = new String(b_arr);
	  m_ipaddr = m_ipaddr.trim();
	  Log.i(TAG,"config4 "+m_ipaddr);
	  index = index+len+3;
  
	  m_netmask = "*************";
	  if(arr[index] == 0x10)
	  {
		m_netmask = "***********";
	  }
	  Log.i(TAG,"config5 "+String.valueOf(index)+" "+String.valueOf(arr[index]));
	  index+=2;
	  len = arr[index];
	  index = index+len+1;
	  index+=9;
  
	  len = arr[index];
  
	  index++;
	  Log.i(TAG,"config6 "+String.valueOf(index)+" "+String.valueOf(len));

	  for(int i=0;i<b_arr.length;i++)
	  {
	   b_arr[i] = 0;
	  }
	  for(int i=0;i<len;i++)
	  {
	   b_arr[i] = arr[index+i];
	  }

	  m_gateway = new String(b_arr);
	  m_gateway = m_gateway.trim();
	  Log.i(TAG,"config7 "+m_gateway);
	  index = index+len+1;
	  
	  len = arr[index];
	  index = index+len+1;
	  index++;
  
	  len = arr[index];
	  index++;
	  Log.i(TAG,"config8 "+String.valueOf(len));

	  for(int i=0;i<b_arr.length;i++)
	  {
	   b_arr[i] = 0;
	  }

	  for(int i=0;i<len;i++)
	  {
	   b_arr[i] = arr[index+i];
	  }
  
	  b_arr[len] = 0;    
	  m_dns = new String(b_arr);
	  m_dns = m_dns.trim();
  
	  Log.i(TAG,"config9 "+m_dns);
	}
	private void do_save_fiile(byte[] arr)
	{
	 String path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath();
		 
	 String str = "ipconfig.txt";
 
	 try{
	   File file = new File(path, str);
	   FileOutputStream trace = new FileOutputStream(file);
 
	   trace.write(arr);
	   trace.close();
 
	   copy_to_ethernet(file.getPath());
	 }
	 catch(Throwable e1) {
	   // ...
	   //mainActivity.showTips("abcd "+e1.getMessage());
	 }
 
	}
 
	public void save_file(String ipaddr,String netmask,String gateway,String dns)
	{
	   int sz = 16*6+9+14+ipaddr.length()+gateway.length()+dns.length(); 
	 
	   if(TextUtils.isEmpty(dns))
	   {
		 sz = sz+gateway.length();
	   }
 
	   byte[] arr = new byte[sz];
 
	   byte[] head = {0x00,0x00,0x00,0x02,0x00,0x0c,0x69,0x70,0x41,0x73,
					  0x73,0x69,0x67,0x6e,0x6d,0x65,0x6e,0x74,0x00,0x06,
					  0x53,0x54,0x41,0x54,0x49,0x43,0x00,0x0b,0x6c,0x69,
					  0x6e,0x6b,0x41,0x64,0x64,0x72,0x65,0x73,0x73,0x00};
 
	   for(int i=0;i<head.length;i++)
	   {
		 arr[i] = head[i];
	   }
	   int index;
	   index = head.length;
	   
	   byte len = (byte)ipaddr.length();
	   
	   arr[index] = len;
	   index ++;
	   
	   byte[] ipaddr_bytes = ipaddr.getBytes();
	   for(int i=0;i<ipaddr_bytes.length;i++)
	   {
		 arr[index+i] = ipaddr_bytes[i];
	   }
	   index = index + ipaddr_bytes.length;
	   
	   byte [] b_arr = {0x00,0x00,0x00};
	 
	   for(int i=0;i<b_arr.length;i++)
	   {
		 arr[index+i] = b_arr[i];
	   }
	   index = index + b_arr.length;
	   
 
	   len = 0x18;
	   if(netmask.equals("***********"))
	   {
		 len = 0x10;
	   }
 
	   arr[index] = len;
 
	   index++;
	   byte[] b_arr1 = {0x00,0x07,0x67,0x61,0x74,0x65,0x77,0x61,0x79,0x00,
						0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00};
 
	 
	   for(int i=0;i<b_arr1.length;i++)
	   {
		 arr[index+i] = b_arr1[i];
	   }
	   index = index + b_arr1.length;
						
	   len = (byte)gateway.length();
	   arr[index] = len;
 
	   index++;
 
	   byte[] gateway_bytes = gateway.getBytes();
 
	   for(int i=0;i<gateway_bytes.length;i++)
	   {
		 arr[index+i] = gateway_bytes[i];
	   }
	   index = index + gateway_bytes.length;
	   byte[] b_arr2 = {0x00,0x03,0x64,0x6e,0x73,0x00};
	
 
	   for(int i=0;i<b_arr2.length;i++)
	   {
		 arr[index+i] = b_arr2[i];
	   }
	   index = index + b_arr2.length;
	   if(TextUtils.isEmpty(dns))
	   {
		 arr[index] = len;
 
		 index++;
   
		 for(int i=0;i<gateway_bytes.length;i++)
		 {
		   arr[index+i] = gateway_bytes[i];
		 }
		 index = index + gateway_bytes.length;  
		
	   }
	   else 
	   {
		 len = (byte)dns.length();
		 arr[index] = len;
 
		 index++;
   
 
		 byte[] dns_bytes = dns.getBytes();
 
		 for(int i=0;i<dns_bytes.length;i++)
		 {
		   arr[index+i] = dns_bytes[i];
		 }
		 index = index + dns_bytes.length;  
		
	   }
 
	   byte[] b_arr3 = {
	   0x00,0x03,0x64,0x6e,0x73,0x00,0x07,0x30,0x2e,0x30,
	   0x2e,0x30,0x2e,0x30,0x00,0x0d,0x70,0x72,0x6f,0x78,
	   0x79,0x53,0x65,0x74,0x74,0x69,0x6e,0x67,0x73,0x00,
	   0x04,0x4e,0x4f,0x4e,0x45,0x00,0x02,0x69,0x64,0x00,
	   0x00,0x00,0x00,0x00,0x03,0x65,0x6f,0x73};
	  
 
	   for(int i=0;i<b_arr3.length;i++)
	   {
		 arr[index+i] = b_arr3[i];
	   }
	   index = index + b_arr3.length;  
	   // transfer bytes from this buffer into the given destination array
	   
	 
	   //bf.get(arr, 0, arr.length);
 
	   do_save_fiile(arr);
	   //mainActivity.showTips(str);
	}
 
	public void read_ethernet_file()
	{
		m_ipaddr = "***********";
		m_netmask = "*************";
		m_gateway = "***********";
		m_dns = "***********";

	 	String path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath();
		 
	 	String str = "ipconfig.txt";
 
	 	try{
	   		File file = new File(path, str);
	   		FileInputStream trace = new FileInputStream(file);
 
	   		byte[] arr = new byte[255]; 
	   		trace.read(arr);
	   		trace.close();
 
	   		//mainActivity.showTips(arr);
 
	   		read_from_config(arr);
	 	}
	 	catch(Throwable e1) {
	   	// ...
	   	//mainActivity.showTips("abcd "+e1.getMessage());
			showTips_ex("ethernet "+e1.getMessage());
	 	}
  
	}
 
	private void copy_to_ethernet(String source_file)
	{
		// 2025-01-03 讓 ipconfig.txt 可以被 IpConfigStore 讀取
		// cp -a /storage/emulated/0/Download/ipconfig.txt /data/misc/ethernet
	 	final String[] commands = {
			"cp -a " + source_file + " /data/misc/ethernet/ipconfig.txt",
			"chmod 777 /data/misc/ethernet/ipconfig.txt",
		};
		execute_as_root(commands);
	}
	private void copy_file(String source_file,String det_file)
    {
        try{
            String cmd = "su -c cp "+source_file+" "+det_file;   

            //Toast.makeText(RecordActivity.this,cmd, Toast.LENGTH_LONG)
            //    .show();             
            Process p = Runtime.getRuntime().exec(cmd);
            p.waitFor();
        }
        catch( InterruptedException e)
        {
            //Toast.makeText(RecordActivity.this,e.getMessage(), Toast.LENGTH_LONG)
              //  .show();
            
        }
        catch(IOException e)
        {
            //Toast.makeText(RecordActivity.this,e.getMessage(), Toast.LENGTH_LONG)
              //  .show();
            
        }   
    }
	public String getTimeOffset()
	{
		return String.valueOf(m_time_offset);	
	}	
	public void setTimeOffset(String time)
	{
		Log.i(TAG,time);
		long int_time = Utility.getNumber(time);
		Date date = new Date();

		long long_time = date.getTime()/1000;

		m_time_offset = int_time - long_time;

		Log.i(TAG,String.valueOf(m_time_offset)+" "+String.valueOf(long_time));
	}
	private File exportFile(boolean is_show,File src, File dst) throws IOException {

		//if folder does not exist
		//if(true){
		if (!dst.exists()) {
			
			return null;
			
		}

		//Toast.makeText(getApplicationContext(), dst.getPath()+" "+src.getPath(), Toast.LENGTH_LONG).show();
		
		Date date = new Date();
		long long_time = date.getTime()/1000;
		long now_time = (long_time+m_time_offset)*1000;
		date.setTime(now_time);
		SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd_HHmmss");
		df.setTimeZone(TimeZone.getTimeZone("Asia/Taipei"));
		String timeStamp = df.format(date);
		String det_file = dst.getPath() + File.separator  + timeStamp + ".avi";
		if(is_show)
			showTips_ex(det_file);
		copy_file(src.getPath(),det_file);
		
		return null;
	}	
	
	private boolean is_extern_file_exist()
	{
		File[] files;
	   
		boolean ret = false;
  
		files = getExternalFilesDirs(Environment.MEDIA_MOUNTED);
		
		//files = getExternalFilesDirs(null);
		for(File file:files){
			if(file==null || !file.exists())    continue;
			String path = file.getAbsolutePath();
			Log.i(TAG,path);
			int index = path.lastIndexOf("/Android/data/");
			if (index > 0){
				path =  path.substring(0, index);
				//Toast.makeText(RecordActivity.this,"value path0: " + path, Toast.LENGTH_LONG)
				//.show();
				
			}

			//Toast.makeText(getApplicationContext(), path, Toast.LENGTH_LONG).show();

			if(path.equals("/storage/emulated/0") == true)    continue;

			m_usb_rec_path = path;
			Log.i(TAG,path);
			long sz = file.getUsableSpace();
			//Toast.makeText(RecordActivity.this,String.valueOf(sz), Toast.LENGTH_LONG)
			//    .show();
			Log.i(TAG,String.valueOf(sz));
			if(sz < MyApplication.MIN_SIZE)
			{
  
				delete_files(path);
			  
			}
  
			ret = true;
				
		}
  
		return ret;
		
	}	
	public void copy_to_usb(boolean is_show)
	{
		m_video_record_start = false;

		boolean is_exist = is_extern_file_exist();

		if(is_exist == false)    return;
		//if(is_exist)    return;
		
		//m_usb_rec_path = "/storage/emulated/0/Download/";
		File dst = new File(m_usb_rec_path);

		File src = new File("/storage/emulated/0/Download/"+"123.avi");
		try{
			exportFile(is_show,src, dst);	
		}
		catch(IOException e)
		{

		}			
	}
	public boolean checkAndStartRecord(Session session)
	{
		boolean ret;
		ret = false;
		if(m_video_record_enable == false)    return ret;
		if(m_video_record_start == true)    return ret;

		if(is_door_calling(session) == false)    return ret;
		delete_file("/storage/emulated/0/Download/"+"123.avi");
		
		m_video_record_start = true;
		PortSipSdk engine = getVoipLine(session.voip_line);
		String path,filename;
		path = "/storage/emulated/0/Download";
		filename = "123";
		engine.startRecord(session.getSessionId(),path,filename,false,
		PortSipEnumDefine.ENUM_AUDIO_FILE_FORMAT_WAVE,
		PortSipEnumDefine.ENUM_RECORD_MODE_BOTH,
		PortSipEnumDefine.ENUM_VIDEOCODEC_VP8,
		PortSipEnumDefine.ENUM_RECORD_MODE_RECV);
		ret = true;
		return ret;
		
	}
	public void stopRecord(Session session)
	{
		if(m_video_record_start)
		{
			m_video_record_start = false;
			PortSipSdk engine = getVoipLine(session.voip_line);
			engine.stopRecord(session.getSessionId());
			copy_to_usb(false);
		}
	}	
	public void setLocalVideoWindow(PortSipSdk portSipLib,PortSIPVideoRenderer localRenderScreen,boolean state, boolean mirror)
	{


		if(localRenderScreen != sdklocalRenderScreen)
		{
			sdklocalRenderScreen = localRenderScreen;
			portSipLib.setLocalVideoWindow(sdklocalRenderScreen);
		}
		
		
		if(state != sdkstate)
		{
			sdkstate = state;
			sdkmirror = mirror;
			portSipLib.displayLocalVideo(state,mirror);
		}	
	}
  	public void hangUp(Session session)
	{
		Log.i(TAG,"hangUp");
		PortSipSdk engine = getVoipLine(session.voip_line);

		//stopRecord(session);
		engine.setRemoteVideoWindow(session.SessionID, null);
      	engine.hangUp(session.getSessionId());
		setLocalVideoWindow(engine,null,false,false);
	  	//engine.displayLocalVideo(false,false);
		//engine.setLocalVideoWindow(null);
		
	}
	private String findContacts(String call)
	{
		int size = EditActivity.txl_list.size();
		String name = call;
		for (int i = 0; i < size; i++) {
			TongXunLu contact = EditActivity.txl_list.get(i);
			if(call.equals(contact.getHaoma()))
			{
				name = contact.getXingming();
				break;
			}

		}

		return name;
	}

    private boolean check_limitcall(String callTo)
	{
		if(TextUtils.isEmpty(allow))    return true;
	
		String[] range = allow.split(",");
		
		if(range == null || range.length == 0)    return true;

		if(TextUtils.isEmpty(range[0]))  return true;

        if(callTo.equals("128"))    return true;

        int int_call = Utility.getNumber(callTo); 

		for(int i=0;i<range.length;i++)
		{
			String[] number = range[i].split("-");
			if(number.length >= 2)
			{
				int start = Utility.getNumber(number[0]);
				int end = Utility.getNumber(number[1]);
                //showTips(String.valueOf(int_call)+" "+String.valueOf(start)+" "+String.valueOf(start));
				if(int_call >= start && int_call <= end)
				{
					Log.i(TAG,"range "+range[i]);
					return true;
				}

			}
		}

		return false;
	}

	private boolean check_nightcall()
	{
        if(night_enable == false )    return false;

		Date date = new Date();
		long long_time = date.getTime()/1000;
		long now_time = (long_time+m_time_offset)*1000;
		date.setTime(now_time);
		SimpleDateFormat df = new SimpleDateFormat("HHmm");
		df.setTimeZone(TimeZone.getTimeZone("Asia/Taipei"));
		String time = df.format(date);

        Log.i(TAG,"time "+time);
        
		int int_time = Utility.getNumber(time);
		if(int_time >= night_time[1] && int_time <= night_time[0])
		{
			return false;
		}
		else 
		{
			return true;
		}
	}
	public long outgoingCall(boolean isVideo,String callTo) {

        if(check_limitcall(callTo) == false)  
		{
		  showTips("限撥!");
		  return 0;
		}
        if(check_nightcall() == true)
		{
            callTo = night_call;
		}

		boolean ret = checkIPCam(callTo);

		if(ret) isVideo = false;
		
		Date date = new Date();
		long long_time = date.getTime()/1000;
		long now_time = (long_time+m_time_offset)*1000;
		date.setTime(now_time);
		SimpleDateFormat riqi1 = new SimpleDateFormat("yyyy-MM-dd ");
		SimpleDateFormat shijian1 = new SimpleDateFormat("HH:mm");

		riqi1.setTimeZone(TimeZone.getTimeZone("Asia/Taipei"));
		shijian1.setTimeZone(TimeZone.getTimeZone("Asia/Taipei"));
		String riqi = riqi1.format(date);
		String shijian = shijian1.format(date);

		//Date d = new Date();
		//String riqi = new SimpleDateFormat("yyyy-MM-dd ").format(d);
		//String shijian = new SimpleDateFormat("HH:mm").format(d);
		String str = null;
		String temp = SettingConfig.getRecord(this);
		int remote;

		String name = callTo;
		name = findContacts(callTo);
		TongXunJiLu t = new TongXunJiLu();
		t.setName(getResources().getString(R.string.str_callout));
		t.setJieTong(true);
		t.setOutgoing(true);
		t.setPhonenum(callTo);
		t.setName(name);
		t.setRiqi(riqi);
		t.setShijian(shijian);
		t.setVideo(isVideo);

		String json = JsonUtil.toJSon(t);
		if(TextUtils.isEmpty(json))
		{
			return doOutgoingCall(isVideo,callTo);
		}

		if(!TextUtils.isEmpty(temp)){
				str = json + "=" + temp;
		} else {
				str = json;
		}

		SettingConfig.setRecord(this,str);

		return doOutgoingCall(isVideo,callTo);

	}

	public String getLocalIP(boolean ipv6){
		m_local_ip = "";
		if(skip_network == false)
			m_local_ip = netmanager.getLocalIP(ipv6);
		return m_local_ip;
	}


	public String saveCallLog(Session session)
	{
		String msg;

      	String caller = session.Remote;

		if(TextUtils.isEmpty(caller))    return "";
      	int index = caller.indexOf("@");
		if(index < 0)    return "";

		if(session.voip_line == 0)
		{
          	msg = caller.substring(index+1);
		}
		else
		{
			msg = caller.substring(0,index);
           	msg = msg.replace("sip:","");

			if(session.voip_line == 2)
			{
               	msg = "2*"+msg;
			}
			else if(session.voip_line == 3)
			{
              	msg = "3*"+msg;
			}
		}

		session.showRemote = msg;
	    //Date d = new Date();
	    //String riqi = new SimpleDateFormat("yyyy-MM-dd ").format(d);
	    //String shijian = new SimpleDateFormat("HH:mm").format(d);

		Date date = new Date();
		long long_time = date.getTime()/1000;
		long now_time = (long_time+m_time_offset)*1000;
		date.setTime(now_time);
		SimpleDateFormat riqi1 = new SimpleDateFormat("yyyy-MM-dd ");
		SimpleDateFormat shijian1 = new SimpleDateFormat("HH:mm");

		riqi1.setTimeZone(TimeZone.getTimeZone("Asia/Taipei"));
		shijian1.setTimeZone(TimeZone.getTimeZone("Asia/Taipei"));
		String riqi = riqi1.format(date);
		String shijian = shijian1.format(date);
		
	    TongXunJiLu t = new TongXunJiLu();

      	String name = findContacts(msg);
	    t.setName(getApplicationContext().getResources().getString(R.string.str_incoming));
	    t.setJieTong(false);
      	t.setOutgoing(false);
	    t.setPhonenum(msg);
      	t.setName(name);
	    t.setRiqi(riqi);
	    t.setShijian(shijian);

		String temp = SettingConfig.getRecord(getApplicationContext());

      	String str;
		if(!TextUtils.isEmpty(temp)){
		      str = JsonUtil.toJSon(t) + "=" + temp;
	    } else {
		      str = JsonUtil.toJSon(t);
	    }

	    SettingConfig.setRecord(getApplicationContext(),str);
      	return msg;

	}

	public void setIPCam(String ipcam)
	{
		SettingConfig.setIPCam(getApplicationContext(),ipcam);
		ipcam_arr = ipcam.split("\n");
	}
	private void answerCallLog(Session line,boolean isVideo)
	{
		String str = SettingConfig.getRecord(getApplicationContext());

		if(!TextUtils.isEmpty(str)){
			
			String[] arrStr = str.split( "=");

			TongXunJiLu t = (TongXunJiLu) JsonUtil.fromJSon(arrStr[0],"TongXunJiLu");
			if(t == null)    return;

			t.setJieTong(true);
			t.setVideo(isVideo);
			
			Log.i(TAG,str);
			String data = JsonUtil.toJSon(t);

			if(arrStr.length > 1)
			{
				int index = str.indexOf("=");
				if(index > 0)    
					data = data + str.substring(index);
			}

		   	SettingConfig.setRecord(getApplicationContext(),data);
    	}
	}

	public long doOutgoingCall(boolean isVideo,String callTo)
	{
		if (TextUtils.isEmpty(callTo)) {
			showTips("The phone number is empty.");
			return 0;
		}

    	int voip_line;

		int index = callTo.indexOf('*');

		String new_str = callTo;
		if(index > 0)
		{
			String str_line = callTo.substring(0,2);
			if(!TextUtils.isEmpty(str_line))
			{
				voip_line = 0;
			}
        	else if(str_line.equals("1*"))
			{
				callTo = callTo.substring(2);
				voip_line = 1;
			}
			else if(str_line.equals("2*"))
			{
				callTo = callTo.substring(2);
				voip_line = 2;

			}
			else if(str_line.equals("3*"))
			{
				callTo = callTo.substring(2);
				voip_line = 3;

			}
        	else
			{
				voip_line = 0;
			}

        	new_str = "sip:"+callTo;
			new_str = new_str.replace("*",".");

		}
		else
		{
			index = callTo.indexOf('.');
			if(index > 0)
			{
				new_str = "sip:"+callTo;

				voip_line = 0;

			}
			else
			{
				voip_line = 1;
			}
		}

		if(isOnline(voip_line) == false)
		{
			showTips(getResources().getString(R.string.str_online_err));
			return 0;
		}

    	PortSipSdk myEngine = getVoipLine(voip_line);
		Session currentLine = CallManager.Instance().getCurrentSession();
		if (currentLine==null) {
			showTips("Current line is busy now, please switch a line.");
			return 0;
		}
		else if (!currentLine.IsIdle()) {
			showTips("Current line is busy now, please switch a line.");
			return 0;
		}

		// Ensure that we have been added one audio codec at least
		if (myEngine.isAudioCodecEmpty()) {
			showTips("Audio Codec Empty,add audio codec at first");
			return 0;
		}
        
		// Usually for 3PCC need to make call without SDP
		long sessionId = myEngine.call(new_str, true, isVideo);
		if (sessionId <= 0) {
			showTips("Call failure");
			return 0;
		}
		previous_call_status = 0;
		//default send video
		myEngine.sendVideo(sessionId, isVideo);

		currentLine.showRemote = getCallNumber(callTo);//callTo;

   	 	if(voip_line == 0)
		{
		    currentLine.intRemote = 1000;
		}
		else
	      currentLine.intRemote = Utility.getNumber(currentLine.showRemote);
    	
		currentLine.voip_line = voip_line;
		currentLine.SessionID = sessionId;
		currentLine.state = Session.CALL_STATE_FLAG.TRYING;
		currentLine.HasVideo = isVideo;
		currentLine.setIsSendVideo(isVideo);
		showTips(currentLine.LineName + ": Calling... "+String.valueOf(sessionId));

    	//adjust_mic(currentLine);
		return sessionId;

	}
 	public PortSipSdk getVoipLine(int line)
 	{
		PortSipSdk mysdk;

	 	switch(line)
	 	{
		case 0:
		 mysdk = mEngineP2P;
		 break;
		 case 2:
		 mysdk = mEngine2;
		 break;
		 case 3:
		 mysdk = mEngine3;
		 break;

		 default:
		 mysdk = mEngine;
		 break;
	 	}

	 	return mysdk;

	 }
 
	 public void setSdkService(int line,Service service)
	 {
		switch(line)
		{
	   	case 0:
	   	portsipServiceP2P = service;
		break;
		case 2:
		portsipService2 = service;
		break;
		case 3:
		portsipService3 = service;
		break;

		default:
		portsipService = service;
		break;
		}
		
	 }
	 public Service getSdkService(int line)
 	{
		Service service;

	 	switch(line)
	 	{
		case 0:
		service = portsipServiceP2P;
		 break;
		 case 2:
		 service = portsipService2;
		 break;
		 case 3:
		 service = portsipService3;
		 break;

		 default:
		 service = portsipService;
		 break;
	 	}

	 	return service;

	 }
	 public void checkConnect()
	 {
		if(!isOnline(1))
			reenableNetwork();
	 }
	public String getCallNumber(String caller)
 	{
		String msg;

	 	int index = caller.indexOf("@");
	 	if (index < 0)
	 	{
			msg = caller;
			 //num = 0;
	 	}
	 	else
	 	{
			msg = caller.substring(0,index);
			msg = msg.replace("sip:","");
			//num = Integer.parseInt(msg);
	 	}

	 	return msg;
 	}
	private void show_wawatchdog()
	{
		Intent intent = new Intent();
		intent.setClassName("com.weema.wawatchdog", "com.weema.wawatchdog.LocalCastielService");
		startService(intent);
	}
  	public boolean isMyConference()
	{
	    return CallManager.Instance().isMyConference();
	}
  	public boolean isOnline(int line)
	{
		if(line == 0) return true;

		return CallManager.Instance().getregist(line);
	}
	public boolean isMoreLine()
	{
		return CallManager.Instance().isMoreLine();
	}

	public boolean is2Line()
	{
		return CallManager.Instance().is2Line();
	}
  	public boolean isWaitLine(Session line)
	{
      return line == getWaitSession();
	}
	public boolean changeWaitLine()
	{
		return CallManager.Instance().changeWaitLine();
	}
	public boolean changeCurrentLine()
	{
      return CallManager.Instance().changeCurrentLine();
	}
	public Session getWaitSession()
	{
		return CallManager.Instance().getWaitSession();
	}
	public boolean isCurrentLine(Session line)
	{
    	return CallManager.Instance().isCurrentLine(line);
	}
	public void setCurrentLine(Session line)
	{
     	CallManager.Instance().setCurrentLine(line);
	}
	public void set2Line(boolean is2Line)
	{
    	 CallManager.Instance().set2Line(is2Line);
	}

	public void removeConf(PortSipSdk sdk)
	{
      	CallManager.Instance().removeConf(sdk);
	}

	public int useLineCount()
	{
		int index;
    	Session[] sessions = CallManager.Instance().sessions;

		index = 0;
		for (int i = 0; i < sessions.length; i++) {
			if(sessions[i].getRecvCallState() || sessions[i].getSessionConnected())
			{
				showTips("count 1 "+String.valueOf(i));
				index++;
			}
		}

    	showTips("count "+String.valueOf(index));
		return index;
	}

	public void hangUpAll()
	{
		Session[] sessions = CallManager.Instance().sessions;
      	int index;
		index = 0;
	
		for (Session session: sessions)
		{
			if (session.IsIdle() == false)
			{
				index++;
            	hangUp(session);
				session.Reset();
			}
		 }

     	showTips("hangup all "+String.valueOf(index));

    	set2Line(false);

	}

	private void addActiveSessionToConfrence(int lineid,PortSipSdk sdk)
	{
		int index;
		Session[] sessions = CallManager.Instance().sessions;

		index = 0;
		for (Session session : sessions)
		{
			if(session.voip_line == lineid && session.state == Session.CALL_STATE_FLAG.CONNECTED)
			{
				index++;

				int ret = sdk.joinToConference(session.SessionID);
				showTips("ID "+String.valueOf(session.SessionID)+" "+String.valueOf(ret));
					
              	session.setHoldState(false);
			}
		}

		showTips("add conf "+String.valueOf(index));
	}

	public void i2c_open_door()
  	{
		if(m_WaService != null)
	    	m_WaService.open_door();
  	}
	public void alarm_msg_send(String status,int id)
	{
		if(status.equals("3"))
			sendAppAlarm(id);
			
		if(m_alarm_msg_enable == false) return;

    	String msg;

		switch(id)
		{
			case 0:
			msg = "2";
			break;
			case 1:
			msg = "2";
			break;
			case 2:
			msg = "2";
			break;
			case 3:
			msg = "2";
			break;
			case 4:
			msg = "2";
			break;
			case 5:
			msg = "1";
			break;
			case 6:
			msg = "3";
			break;
			case 7:
			msg = "4";
			break;
			case WaService.DOOR_PIN:
			msg = "5";
			break;
			default:
			msg = "2";

			break;
		}
		SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

		String number = preferences.getString(PortSipService.USER_NAME, null);

		if(TextUtils.isEmpty(number))    return;

    	msg = "ALARM="+status+"-"+number+"-"+msg;
    	send_to_sip(m_alarm_msg_number,msg);

	}

	public void send_to_sip(String number,String msg)
	{
		if(isOnline(1))
		{
			SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

  	  		String sip_domain = preferences.getString(PortSipService.USER_DOMAIN, null);

			if(TextUtils.isEmpty(sip_domain))    return;

     	 	String sip_port = preferences.getString(PortSipService.SVR_PORT, "5060");

			

			String addr = "sip:"+number+"@"+sip_domain+":"+sip_port;
			String text = msg;
			byte[] bytes = text.getBytes();
			mEngine.sendOutOfDialogMessage(addr,"text","plain",false,bytes,bytes.length);
			Log.i(TAG,addr+" "+msg);
		}
		else
		{
			showTips("not register");

		}
	}

	public void open_door(Session session)
	{
		String text = "OPEN D1";
		byte[] bytes = text.getBytes();
		PortSipSdk myEngine = getVoipLine(session.voip_line);
		mEngine.sendOutOfDialogMessage(session.Remote,"text","plain",false,bytes,bytes.length);

		showTips("open door "+String.valueOf(session.voip_line));

	}
	
  	public String getPhoneNumber(int line,boolean is_online)
	{
		String str="";

		SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

    	boolean ret = CallManager.Instance().getregist(line);
    	if(ret == false && is_online == true)
		{
			if(line == 1)
				str =  "unregister " + mregtips[0];
			else
			{
				boolean ischeck=false;
			      
				if(line == 2)
				{
					ischeck = preferences.getBoolean(PortSipService.SIP_ENABLE2, false);
				}
				else if(line == 3)
				{
					ischeck = preferences.getBoolean(PortSipService.SIP_ENABLE3, false);

				}

				if(ischeck == true)
				{
					str =  "unregister " + mregtips[line-1];
				}
				else
				{
					str = "";
				}
			}
	  	}
    	else
		{
			str = preferences.getString(PortSipService.USER_NAME, "");
    	}

		return str;
	}

	public void setDND(boolean value)
	{
		misDND = value;
		SettingConfig.setDND(getApplicationContext(),value);

	}
	public boolean getDND()
	{
		return misDND;
	}
	public void cancel_notify()
	{

	}
	public boolean upperLineCount()
	{
		return CallManager.Instance().upperLineCount();
	}

	public void swapLine()
	{
    		CallManager.Instance().swapLine();
	}
	public Session findIdleLine()
	{
		return CallManager.Instance().findIdleSession();

	}
	public void setConf(int lineid,boolean isVideo,PortSIPVideoRenderer view)
	{
		int rt;

     	PortSipSdk mysdk = getVoipLine(lineid);
		//mysdk.createVideoConference(null,320,240,false);

		if(isVideo)
			rt = mysdk.createVideoConference(view,352, 288, false);
		else
			rt = mysdk.createAudioConference ();

		if (rt == 0) {
			showTips("Make conference succeeded");
			CallManager.Instance().addActiveSessionToConfrence(lineid,mysdk);

			setMyConferenceMode(true);
		} else {
			showTips("Failed to create conference");
			setMyConferenceMode(false);

		}

	}

  	public void setMyConferenceMode(boolean state)
	{
    	CallManager.Instance().setMyConferenceMode(state);
	}

	public void addCurrentLine(Session line)
	{
		CallManager.Instance().addCurrentLine(line);
	}
	public void showTips_ex(String id) {
		//mtips.setText(text);
		if(true)    return;
		Toast.makeText(getApplicationContext(), id, Toast.LENGTH_LONG).show();
	}
	
	public void showTips(int id) {
		//mtips.setText(text);
		if(true)    return;
		Toast.makeText(getApplicationContext(), id, Toast.LENGTH_LONG).show();
	}

	public void showTips(String text) {
		//mtips.setText(text);
		if(true)    return;
		Toast.makeText(getApplicationContext(), text, Toast.LENGTH_LONG).show();
	}
  	public boolean answerSessionCall(Session sessionLine, boolean videoCall)
	{
		if(sessionLine.getRecvCallState() == false)
	  	{
			//Toast.makeText(getApplicationContext(),sessionLine.LineName + "No incoming call on current line",Toast.LENGTH_SHORT);
			return false;
		}

    	//adjust_mic(sessionLine);

		//sessionLine.state = Session.CALL_STATE_FLAG.CONNECTED;
		//showTips("voip line "+String.valueOf(sessionLine.voip_line));
		PortSipSdk myEngine = getVoipLine(sessionLine.voip_line);
		myEngine.answerCall(sessionLine.getSessionId(),videoCall);
		Ring.getInstance(this).stopRingTone();
		Ring.getInstance(this).stopRingBackTone();

    	answerCallLog(sessionLine,sessionLine.getIsSendVideo());

    	return true;
	}

	public void adjust_mic_vol(Session session)
	{
		PortSipSdk mysdk = getVoipLine(session.voip_line);
		Log.i(TAG,"adjust_mic_vol mic "+mCurMicVol+", "+m_mic_scaling[mCurMicVol]);
		Log.i(TAG,"adjust_mic_vol vol "+m_volume+", "+m_spk_scaling[m_volume]);

		mysdk.setChannelInputVolumeScaling(session.getSessionId(),m_mic_scaling[mCurMicVol]);
		mysdk.setChannelOutputVolumeScaling(session.getSessionId(),m_spk_scaling[m_volume]);
		//mysdk.setChannelInputVolumeScaling(session.getSessionId(),session.mic_value);
		
		
	}
	public void adjust_spk(Session session)
	{
		PortSipSdk mysdk = getVoipLine(session.voip_line);
		setVolume(2);
		mysdk.setChannelOutputVolumeScaling(session.getSessionId(),m_spk_scaling[m_volume]);
		//mysdk.setChannelInputVolumeScaling(session.getSessionId(),session.mic_value);
		
		
	}

	public int getVolume()
	{

		if(myContext != null)
        	return Ring.getInstance(myContext).getVolume();
		else 
			return 1;
	}

	public void setVolume(int val)
	{
		m_volume = val;
		SettingConfig.setVolume(getApplicationContext(),m_volume);

	}
	
	public void adjust_spk(Session session,int direct)
	{
      	if(direct == 0)
		{
			m_volume--;
		}
		else
		{
			m_volume++;
		}

		if(m_volume < 0)
			m_volume = 0;
		else if(m_volume > 10)
			m_volume = 10;

		PortSipSdk mysdk = getVoipLine(session.voip_line);

		mysdk.setChannelOutputVolumeScaling(session.getSessionId(),m_spk_scaling[m_volume]);
		//session.mic_value = m_mic_scaling[m_volume];
		SettingConfig.setVolume(getApplicationContext(),m_volume);

      	showTips(Integer.toString(m_volume));

	}
	
	public void set_mic(int val){
		mCurMicVol = val;
		SettingConfig.setMIC(getApplicationContext(),mCurMicVol);

      	showTips(Integer.toString(mCurMicVol));
	}
	public void adjust_mic(Session session,int direct)
	{
      	if(direct == 0)
		{
			mCurMicVol--;
		}
		else
		{
			mCurMicVol++;
		}

		if(mCurMicVol < 0)
			mCurMicVol = 0;
		else if(mCurMicVol > 10)
			mCurMicVol = 10;

		PortSipSdk mysdk = getVoipLine(session.voip_line);

		mysdk.setChannelInputVolumeScaling(session.getSessionId(),m_mic_scaling[mCurMicVol]);
		session.mic_value = m_mic_scaling[mCurMicVol];
		
		set_mic(mCurMicVol);

	}

  	public void setScale(Session session)
	{

	}
	public void keepCpuRun(boolean keepRun) {
			PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
			if (keepRun == true) { //open
					if (mCpuLock == null) {
							if ((mCpuLock = powerManager.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK, "SipSample:CpuLock.")) == null) {
									return;
							}
							mCpuLock.setReferenceCounted(false);
					}

					synchronized (mCpuLock) {
							if (!mCpuLock.isHeld()) {
									mCpuLock.acquire();
							}
					}
			} else {//close
					if (mCpuLock != null) {
							synchronized (mCpuLock) {
									if (mCpuLock.isHeld()) {
											mCpuLock.release();
									}
							}
					}
			}
	}

  	public Boolean inCall()
	{
		if(myContext == null) return false;
		return ((BaseMainActivity)myContext).inCall();

	}
	public void sendSIPMessage(int line,String to,String msg)
	{
		if(isOnline(line) == false) return;

		PortSipSdk mysdk;
		mysdk = getVoipLine(line);

		mysdk.sendOutOfDialogMessage(to, "text", "plain",false, msg.getBytes(), msg.getBytes().length);

	}
  	private int in_mdoor_list(String number)
	{
		int ret;

		ret = -1;
		int len=myList.size();
    	for(int i=0; i<len; i++) {
	      if (myList.get(i).equals(number)) {
			 // Do something ...
            ret = i;
			break;

	      }
    	}

		return ret;
	}
	private void broadcast_msg(String msg)
	{
		if(myContext == null)    return;
		if(inCall())    return;
		String[] arrStr = msg.split("&");
		Log.i(TAG,"broadcast_msg "+String.valueOf(arrStr.length));
		if(arrStr.length < 3)    return;

		bc_content = msg;

        Intent activityIntent;
        
        activityIntent = new Intent(myContext, MainActivity.class);
        //activityIntent.putExtra("incomingSession",sessionId);
        activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        activityIntent.putExtra(MyApplication.BC_ACTION, 1);
        myContext.startActivity(activityIntent);

	}

	private void msg_callout(int line_id,String callee,String ipMsg,String domain)
	{
	    PortSipSdk mysdk;
		mysdk = getVoipLine(line_id);

		String msg = "sip:"+callee+'@'+domain;
		    
        mysdk.sendOutOfDialogMessage(msg, "text", "plain",false, ipMsg.getBytes(), ipMsg.getBytes().length);

		long ret = outgoingCall(true,callee);

		Intent activityIntent = new Intent(getApplicationContext(), MainActivity.class);
		//activityIntent.putExtra("incomingSession",sessionId);
		activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		activityIntent.putExtra(PortSipService.EXTRA_CALL_SEESIONID, ret);
		startActivity(activityIntent);

	}
    public void recvMessage(int line_id,String from,String to,String message)
	{
		Log.i(TAG,from+" "+to+" "+message);

        if(myContext == null) return;

		if(message.equals("APPGETSTATE"))
		{
			replyAppState(line_id,from);
			return;
		}

		if(message.startsWith("UNLOCK "))
		{
			checkUnlock(line_id,from,message);
			return;
		}
		if(message.startsWith("SETGAS "))
		{
			setGas(line_id,from,message);
			return;
		}		
		if(message.startsWith("LOCK"))
		{
			checkLock(line_id,from);
			return;
		}		        
		if(m_is_mdoor != false && message.equals("GETMDOOR"))
	    {
            sendSIPMessage(line_id,from,"SETMDOOR&"+String.valueOf(m_door_state));

	        if(!TextUtils.isEmpty(from) && in_mdoor_list(from) == -1)
	        {

		        add_list(from);
	        }
	  
	        return;
	    }

	    if(message.startsWith("bc&"))
	    {
            broadcast_msg(message);
		    return;
	    }

	    if(message.startsWith("CALL_ANSWER"))
	    {
			if(serviceWork.can_answer(from,to)){
		    	Intent broadIntent = new Intent(PortSipService.ANSWER_ACTION);
            	sendBroadcast(broadIntent);
			}

            return;
	    }

	    String[] arrStr = message.split("&");
	    if(arrStr.length >= 2)
	    {
		     Log.i(TAG,message+" "+from+" "+m_mdoor_send);

		    if(arrStr[0].equals("SETMDOOR"))
		    {
			    int state = Utility.getNumber(arrStr[1]);
			    doorAction(state);
			    if(from.equals(m_mdoor_send))
			    {
				    Log.i(TAG,"m_mdoor_send_ret = 1");
				    m_mdoor_send_ret = 1;
			    }

			    return;
		    }
			else if(arrStr[0].equals("SETDOOR"))
		    {
			    int state = Utility.getNumber(arrStr[1]);
			    doorActionCall(state);

			    return;
		    }

	    }
		
	    Long tsLong = System.currentTimeMillis()/1000;
        if(tsLong < (m_tsLong+MAX_TIMESECOND))
        {
	      return;
        }

	    m_tsLong = tsLong;
	    if(((BaseMainActivity)myContext).isReady() == false && ((BaseMainActivity)myContext).inCall() == false)
		    return;

		if(message.equals("OPEN D1"))
	    {
		    showTips("OPEN D1");

			if(m_WaService != null)
		     	m_WaService.open_door();

            return;
	    }

		if(arrStr.length == 3)
		{
		    String[] callee = arrStr[0].split("=");
		    String[] ipMsg = arrStr[1].split("=");
            String[] domain = arrStr[2].split("=");

		    //Toast.makeText(getApplicationContext(),"callee: "+callee[1],Toast.LENGTH_LONG).show();
			if(callee.length < 2 ||
			  ipMsg.length < 2 ||
			  domain.length < 2)
			  return;

			if(callee[0].equals("callee"))  
			    msg_callout(line_id,callee[1],ipMsg[1],domain[1]);
	
		}
        else if(arrStr.length > 3)
	    {
		    Toast.makeText(getApplicationContext(),"you have a mesaage from: "+message,Toast.LENGTH_SHORT).show();
            Log.i(TAG,arrStr[2]);
		
		    String[] callee = arrStr[2].split("=");
		    String[] ipMsg = arrStr[3].split("=");
            String[] domain = arrStr[5].split("=");

		    //Toast.makeText(getApplicationContext(),"callee: "+callee[1],Toast.LENGTH_LONG).show();
			if(callee.length < 2 ||
			  ipMsg.length < 2 ||
			  domain.length < 2)
			  return;

		    if(callee[1].equals("ON_HOOK"))
		    {
		        //Toast.makeText(getApplicationContext(),"3 you have a mesaage ",Toast.LENGTH_LONG).show();
			    Intent broadIntent = new Intent(PortSipService.AUTO_ONHOOK_ACTION);
		        sendBroadcast(broadIntent);

		       return;
		    }

			msg_callout(line_id,callee[1],ipMsg[1],domain[1]);

	    }

	}
	private void checkUnlock(int line_id,String from,String message)
	{
		String[] str_arr = message.split(" ");
		if(str_arr.length < 2)    return;

		boolean ret = do_unlock(str_arr[1]);
		String str_reply="";

		if(ret == false)
		{
			str_reply = "REPLY,密碼錯誤";
			sendSIPMessage(line_id,from,str_reply);
		}

		send_update();
	}

	private void checkLock(int line_id,String from)
	{
		boolean ret = do_lock();
		String str_reply="";

		if(ret == false)
		{
			str_reply = "REPLY,"+m_message;
			sendSIPMessage(line_id,from,str_reply);
		}

		send_update();

	}	
	public boolean do_unlock(String key)
	{
		boolean ret;
		if (key.equals(m_str_passwd)) // CORRECT
		{
		  
		  setLockFromRemote(false);
  
		  ret = true;
		  
		}else 
		{
		  ret = false;
		}

		return ret;
	}

    public boolean do_lock()
    {
      boolean ret;

      if(m_is_lock)    
      {
          
          ret = true;
      }
      else 
      {
        ret = toggleLock();

        if(ret)
        {
        
          update_lock();
		  do_replyAppState();
           
        }
       
      }  

      return ret; 
    }     

	public void do_replyAppState()
	{
		int line_id = 1;
		if(TextUtils.isEmpty(m_app_number))    return;

		SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

		String domain = preferences.getString(PortSipService.USER_DOMAIN, "");

		if(TextUtils.isEmpty(domain))    return;

		String from = m_app_number+"@"+domain;		
		String str = m_WaService.do_getstate1();
		Log.i(TAG,str);
		sendSIPMessage(line_id,from,str);
	}	
	private void replyAppState(int line_id,String from)
	{
		String str = m_WaService.do_getstate1();
		Log.i(TAG,str);
		sendSIPMessage(line_id,from,str);
	}

	public void sendAppAlarm(int id)
	{
		int line_id = 1;
		
		if(TextUtils.isEmpty(m_app_number))    return;

		SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

		String domain = preferences.getString(PortSipService.USER_DOMAIN, "");

		if(TextUtils.isEmpty(domain))    return;

		String from = m_app_number+"@"+domain;
		String msg="";
		Log.i(TAG,"sendAppAlarm");
		switch(id)
		{
			case 0:
			msg = "門窗警報1";
			break;
			case 1:
			msg = "門窗警報2";
			break;
			case 2:
			msg = "門窗警報3";
			break;
			case 3:
			msg = "門窗警報4";
			break;
			case 4:
			msg = "門窗警報5";
			break;
			case 5:
			msg = "求救警報";
			break;
			case 6:
			msg = "瓦斯警報";
			break;
			case 7:
			msg = "煙霧警報";
			break;
			default:
			msg ="求救警報";
			break;
		}
		sendSIPMessage(line_id,from,msg);

		replyAppState(line_id,from);
	}
	public boolean setAppNumber(Context context,String val)
	{
		m_app_number = val;
		return SettingConfig.setAppNumber(context,val);

	}
	private void setGas(int line_id,String from,String message)
	{
		String[] str_arr = message.split(" ");
		if(str_arr.length < 2)    return;

		do_setGas(str_arr[1]);
		String str_reply;

		str_reply = "REPLY,成功";
	
		replyAppState(line_id,from);
		sendSIPMessage(line_id,from,str_reply);
		update_gas();
		
	}
	private void update_gas()
	{
		Intent i = new Intent(MyApplication.GAS_ACTION);
		
		sendBroadcast(i);		
		
	}

	public boolean do_setGas(String key)
	{
		if(key.equals("1"))
		{
		  m_ctrl_gas = true;
		  
		}
		else
		{
		  m_ctrl_gas = false;
		  
		}
  
		if(m_WaService != null)
			m_WaService.ctrlGasAction(m_ctrl_gas);


		return true;	
		
	}

	public void onInviteBeginingForward(String forwardTo) {
      
    }

    public void onReceivedSignaling(long sessionId, String signaling) {

    }
    public void onInviteSessionProgress(
            long sessionId,
            String audioCodecNames,
            String videoCodecNames,
            boolean existsEarlyMedia,
            boolean existsAudio,
            boolean existsVideo,
            String sipMessage) {
        
    }
	public void onInviteRinging(long sessionId, String statusText, int statusCode, String sipMessage) {
       sipStore.putRinging(true);
    }

}
