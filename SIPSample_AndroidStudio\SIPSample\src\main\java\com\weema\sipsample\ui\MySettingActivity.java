
package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.widget.CheckBox;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.os.Bundle;
import android.widget.Button;

import android.widget.RelativeLayout;
import android.widget.TextView;

import android.view.LayoutInflater;

import android.view.View;
import android.view.ViewGroup;

import android.app.Fragment;

import com.weema.R;
import android.util.Log;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.widget.EditText;

public class MySettingActivity extends Fragment {

    private final String TAG="MySettingActivity";
    private final String PASSWORD="22466899";
    private MainActivity mainActivity;
    private TextView m_text_version;

    private CheckBox  checkB_delay;
    private CheckBox  checkB_msg;
    private TextView ip_tv;
    
    private MyApplication myApp;
    private boolean m_is_delay,m_is_msg;

    private int m_back_timer = 0;

    private static final int MAX_BACK_TIME = 60;

    private String m_version;

    private int setting_id;

    private CheckBox checkB_yuyin;

    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  		Bundle savedInstanceState) {
  		// TODO Auto-generated method stub

  		super.onCreateView(inflater, container, savedInstanceState);

  		View rootView = inflater.inflate(R.layout.setting_new, null);
  		  //initView(rootView);
        mainActivity = (MainActivity) getActivity();
        myApp = (MyApplication) mainActivity.getApplicationContext();
        m_version= "APP ("+MyApplication.VERSION+") "+String.valueOf(myApp.m_is_sos_mode);

  		return rootView;
  	}

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initView(view);
        onHiddenChanged(false);
    }

    private void initView(View view) {

    	Button no = (Button) view.findViewById(R.id.setting_button);
		no.setOnClickListener(viewDoListener);

        m_text_version= (TextView)view.findViewById(R.id.setting_version);

        m_version = m_version + " "+String.valueOf(myApp.keyRet);

        checkB_delay = (CheckBox)view.findViewById(R.id.setting_checkB_baoquan);
        checkB_msg = (CheckBox)view.findViewById(R.id.setting_checkB_xunxi);

        checkB_yuyin = (CheckBox)view.findViewById(R.id.setting_checkB_yuyin);

        ip_tv = (TextView)view.findViewById(R.id.setting_text_ip);

        ip_tv.setText(myApp.getLocalIP(false));
        
        initOnClickListener(view);

    }
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);

        m_is_active = !hidden;

        if (!hidden){
            mainActivity = (MainActivity)getActivity();

            mainActivity.receiver.broadcastReceiver = null;
            ip_tv.setText(myApp.getLocalIP(false));
            
            String str;
            str = m_version;
            if(myApp.m_ring_enable)
            {
              str = str + " 門口機";
            }

            str = str+" "+myApp.getMDoorState();
            m_text_version.setText(str);
            m_is_delay = myApp.m_sd;

            m_is_msg = myApp.m_ms;

            checkB_delay.setChecked(m_is_delay);

            checkB_msg.setChecked(m_is_msg);

            checkB_yuyin.setChecked(myApp.isDaoLan);
            m_back_timer = MAX_BACK_TIME;
          
        }
        else
        {
          
          m_back_timer = 0;

        }
    }

    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout()
    {
        if(m_is_active == false)    return;

        if(m_back_timer > 0)
        {
            Log.i(TAG,"timeout");
            m_back_timer--;
            if(m_back_timer == 0)
            {
                postMessage(MyApplication.GOTO_BACK);
            }
        }        
    }
    private void check_passwd()
    {
        final View textEntryView = View.inflate(mainActivity, R.layout.alert_dialog_text_entry, null);

        AlertDialog.Builder builder = new AlertDialog.Builder(mainActivity);
        builder.setTitle(R.string.MSG_setting);
        builder.setView(textEntryView)
               .setCancelable(false)
               .setPositiveButton(R.string.yes, new DialogInterface.OnClickListener() {

	    public void onClick(DialogInterface dialog, int id) {

		EditText passEdit = (EditText) textEntryView.findViewById(R.id.password_edit);
                if (!passEdit.getText().toString().equals(PASSWORD)) {
                    mainActivity.showTips(R.string.warnning);
                    //finish();
                } else {
		           
                 dialog.cancel();

                 if(setting_id == R.id.setting_ring)
                     mainActivity.loadRingSettingActivity();
                 else
                     mainActivity.loadVideoCallActivity();

               }

	       }

               })
           .setNegativeButton(R.string.no, new DialogInterface.OnClickListener() {
               public void onClick(DialogInterface dialog, int id) {
                    dialog.cancel();
               }
           }).show();

    }

    private void initOnClickListener(View view) {

        int my_ids[] = {

            R.id.setting_unlock,
            R.id.setting_sos,
            R.id.setting_zone,
            R.id.setting_rel_yanhou,
            R.id.setting_rel_tishi,
            R.id.setting_mdoor,
            R.id.set_register,
            R.id.setting_config,
            R.id.setting_volume,
            R.id.setting_ring,
            R.id.setting_alarm_msg,
            R.id.setting_video_call,
            R.id.setting_center_ip,  
            R.id.setting_other,  
            R.id.setting_network,   
            R.id.show_video,      
            R.id.setting_rel_yuyin,        

        };

        RelativeLayout b = null;
        for( int i=0 ; i< my_ids.length ; ++i )
            if( ( b = (RelativeLayout)view.findViewById( my_ids[i]) ) != null )
                b.setOnClickListener(viewDoListener);

    }


    private View.OnClickListener viewDoListener=new View.OnClickListener(){
        @Override
	public void onClick(View v)
        {
            m_back_timer = MAX_BACK_TIME;
	    switch(v.getId())
	    {
	    case R.id.setting_button:
	        mainActivity.loadConsoleActivity();

		    break;
        case R.id.set_register:
            mainActivity.loadLoginActivity();

            break;
        case R.id.setting_config:
            mainActivity.loadSettingFragment();

            break;
        case R.id.setting_volume:
            mainActivity.loadVolumeActivity();

            break;

        case R.id.setting_unlock:

            mainActivity.loadOptSOSActivity();

		    break;
        case R.id.setting_ring:
            setting_id = R.id.setting_ring;
            check_passwd();

		    break;

        case R.id.setting_sos:
	        mainActivity.loadSOSActivity();

		    break;
	    case R.id.setting_zone:
		    mainActivity.loadZoneActivity();
		    break;

	    case R.id.setting_mdoor:
            mainActivity.loadMDoorActivity();

		    break;

        case R.id.setting_rel_yanhou:

		    m_is_delay = !m_is_delay;
		    myApp.setSD(mainActivity,m_is_delay);
            checkB_delay.setChecked(m_is_delay);
		    
		    mainActivity.showTips(R.string.str_setting_ok);

		    break;
	    case R.id.setting_rel_tishi:

            m_is_msg = !m_is_msg;

 		    myApp.setMS(mainActivity,m_is_msg);

            checkB_msg.setChecked(m_is_msg);
 		    
 		    mainActivity.showTips(R.string.str_setting_ok);

		    break;
        case R.id.setting_alarm_msg:

            mainActivity.loadAlarmMsgActivity();

            break;
        case R.id.setting_video_call:
            setting_id = R.id.setting_video_call;
            check_passwd();

            break;
        case R.id.setting_center_ip:
         
            mainActivity.loadCenterIPActivity();
            break;    
        case R.id.setting_other:
            mainActivity.loadSettingOtherActivity(); 
        
            break;     
        case R.id.setting_network:
            mainActivity.loadSettingNetworkActivity(); 
        
            break;     

        case R.id.show_video:
            
            mainActivity.loadVideoActivity(); 
        
            break;        
        case R.id.setting_rel_yuyin:
            
            myApp.setVoice();
            
            checkB_yuyin.setChecked(myApp.isDaoLan);
          
            break;                      
	    }

        }
    };


  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<MySettingActivity> mActivity;
    public StaticHandler(MySettingActivity activity)
    {
      mActivity = new WeakReference<MySettingActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      MySettingActivity activity = mActivity.get();
      if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
            activity.mainActivity.loadConsoleActivity();
            break;

        }

    }
  }

}
