
package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.CheckBox;

import android.widget.Button;

import android.widget.RelativeLayout;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;


public class OptSOSActivity extends Fragment {
    private static final String TAG="OptSOSActivity";
    MainActivity mainActivity;
    //private MyReceiver myReceiver;
    private CheckBox  m_checkB_state;

    private int m_back_timer = 0;

    private MyApplication myApp;

    private static final int MAX_BACK_TIME = 20;

    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
  		// TODO Auto-generated method stub

  			super.onCreateView(inflater, container, savedInstanceState);

  		  View rootView = inflater.inflate(R.layout.opt_sos, null);
  		  //initView(rootView);
        mainActivity = (MainActivity) getActivity();
        myApp = (MyApplication) mainActivity.getApplicationContext();
  		  return rootView;
  	}

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initView(view);
        onHiddenChanged(false);
    }
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);

        m_is_active = !hidden;
        if (!hidden){
            
            mainActivity.receiver.broadcastReceiver = null;

            Boolean isLock = myApp.m_is_lock;

            if (isLock) {
                m_checkB_state.setChecked(true);
            } else {
                m_checkB_state.setChecked(false);

            }

            m_back_timer = MAX_BACK_TIME;
            //setOnlineStatus();
            Log.i(TAG,"onHiddenChanged");
           
        }
        else {
         
          m_back_timer = 0;

        }
    }


    private void initView(View view) {

    	  Button no = (Button) view.findViewById(R.id.opt_sos_button);

	      no.setOnClickListener(viewDoListener);

    	  m_checkB_state = (CheckBox) view.findViewById(R.id.opt_sos_checkB_state);

	      initOnClickListener(view);

    }

    private void initOnClickListener(View view) {

        int my_ids[] = {
               R.id.opt_sos_start,R.id.opt_sos_unlock,R.id.opt_sos_set_unlock
        };

        RelativeLayout b = null;
        for( int i=0 ; i< my_ids.length ; ++i )
                if( ( b = (RelativeLayout)view.findViewById( my_ids[i]) ) != null )
                        b.setOnClickListener(viewDoListener);
    }

    private View.OnClickListener viewDoListener=new View.OnClickListener(){
        @Override
	      public void onClick(View v)
        {
	          switch(v.getId())
	          {
	          case R.id.opt_sos_button:
		            mainActivity.loadMySettingFragment();
		        break;
	          case R.id.opt_sos_start:
                boolean isLock = myApp.m_is_lock;
                if(isLock == false)
                {
                    boolean ret = myApp.setLock(mainActivity,true);
                    if(ret != false)
                    {
                        m_checkB_state.setChecked(true);
                        mainActivity.showTips(R.string.str_setting_ok);
                    }
                    else 
                    {
                        mainActivity.showTips(myApp.m_message);
                    }
                }
                else
	                  mainActivity.showTips(R.string.str_setting_ok);

		        break;
	          case R.id.opt_sos_unlock:

                mainActivity.loadUnLockActivity();

		        break;
	          case R.id.opt_sos_set_unlock:
		           mainActivity.loadSetUnlockActivity();
		        break;
	          }
        }
    };

     private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout() {
        // TODO Auto-generated method stub
        if(m_is_active == false)    return;
        Log.i(TAG,"timertask");

         if(m_back_timer > 0)
         {
           m_back_timer--;
           if(m_back_timer == 0)
           {
            postMessage(MyApplication.GOTO_BACK);
           }
         }
    }

   public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<OptSOSActivity> mActivity;
    public StaticHandler(OptSOSActivity activity)
    {
      mActivity = new WeakReference<OptSOSActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      	OptSOSActivity activity = mActivity.get();
      	if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
            activity.mainActivity.loadMySettingFragment();

             break;

        }
    }
  }
   
}
