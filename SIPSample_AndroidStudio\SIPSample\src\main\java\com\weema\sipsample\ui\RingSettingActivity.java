package com.weema.sipsample.ui;


import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.CheckBox;

import android.widget.Button;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;
import com.weema.sipsample.util.SettingConfig;
import android.widget.EditText;
import com.weema.sipsample.receiver.PortMessageReceiver;
import android.content.Intent;
import 	android.text.TextUtils;

import android.widget.SeekBar;

public class RingSettingActivity extends Fragment  implements PortMessageReceiver.BroadcastListener{

    private static final String TAG="RingSettingActivity";
    private MainActivity mainActivity;

    private int m_back_timer = 0;
    private int m_wait_timer = 0;

    private MyApplication myApp;
    
    private static final int MAX_BACK_TIME = 60;
    private static final int MAX_WAIT_TIME = 10;

    private EditText m_edit_ipaddr;
    private EditText m_edit_phone_ipaddr;

    private EditText m_edit_number;

    private SeekBar volumeControl = null;
    private SeekBar volumeControl_mic  = null;

    private String m_ring_set_ipaddr;
    private String m_ring_set_phone_ipaddr;

    private String m_ring_set_number;
    private boolean m_ring_set_enable;

    private int m_ring_set_mic;
    private int m_ring_set_volume;
 
    private CheckBox m_check;

    private boolean m_is_active=false;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
          // TODO Auto-generated method stub

            super.onCreateView(inflater, container, savedInstanceState);

            View rootView = inflater.inflate(R.layout.set_ring, null);
            //initView(rootView);
            mainActivity = (MainActivity) getActivity();
            myApp = (MyApplication) mainActivity.getApplicationContext();
            return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       reloadConfig();
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);
       m_is_active = !hidden;
       if (!hidden){

           mainActivity = (MainActivity)getActivity();
           mainActivity.receiver.broadcastReceiver = this;

           m_edit_ipaddr.setText(m_ring_set_ipaddr);
           m_edit_phone_ipaddr.setText(m_ring_set_phone_ipaddr);           
           m_edit_number.setText(m_ring_set_number);

           m_check.setChecked(m_ring_set_enable);

            volumeControl_mic.setProgress(m_ring_set_mic);
           volumeControl.setProgress(m_ring_set_volume);

           m_back_timer = MAX_BACK_TIME;
           //setOnlineStatus();
           Log.i(TAG,"onHiddenChanged");
       
       }
       else {
         
         m_back_timer = 0;

       }
   }

    private void reloadConfig()
    {
        Log.i(TAG,myApp.m_ring_config);
        String[] str_arr = myApp.m_ring_config.split("&");

        if(str_arr.length < 6)    return;

        m_ring_set_ipaddr = str_arr[0];
        m_ring_set_phone_ipaddr = str_arr[1];
        m_ring_set_number = str_arr[2];
        m_ring_set_enable = Boolean.parseBoolean(str_arr[3]);
        m_ring_set_mic = Integer.parseInt(str_arr[4]);
        m_ring_set_volume = Integer.parseInt(str_arr[5]);

    }
   private void initView(View view) {

        m_edit_ipaddr = (EditText)view.findViewById(R.id.ring_set_edit_ipaddr);
        m_edit_phone_ipaddr = (EditText)view.findViewById(R.id.ring_set_edit_phone_ipaddr);

        m_edit_number = (EditText)view.findViewById(R.id.ring_set_edit_number);

        m_check = (CheckBox)view.findViewById(R.id.checkbox_is_enable);

        Button btn_ok= (Button)view.findViewById(R.id.ring_set_button_ok);
        btn_ok.setOnClickListener(btnDoListerner);

        Button btn_esc= (Button)view.findViewById(R.id.ring_set_button_esc);
        btn_esc.setOnClickListener(btnDoListerner);

        Button btn_get= (Button)view.findViewById(R.id.ring_set_button_get);
        btn_get.setOnClickListener(btnDoListerner);

		volumeControl = (SeekBar) view.findViewById(R.id.volume_bar);
	
		volumeControl_mic = (SeekBar) view.findViewById(R.id.volume_bar1);
   
   }

    private void saveConfig()
    {
  		StringBuilder buffer = new StringBuilder();
		
		buffer.append(m_ring_set_ipaddr);
        buffer.append("&").append(m_ring_set_phone_ipaddr);
        buffer.append("&").append(m_ring_set_number);
        buffer.append("&").append(String.valueOf(m_ring_set_enable));
        buffer.append("&").append(String.valueOf(m_ring_set_mic));
        buffer.append("&").append(String.valueOf(m_ring_set_volume));
         
        String joined = buffer.toString();
        Log.i(TAG,joined);
        myApp.setRingConfig(joined);

    }
    private Button.OnClickListener btnDoListerner=new Button.OnClickListener(){
        @Override
	public void onClick(View v)
        {
	      switch(v.getId())
	      {
	      case R.id.ring_set_button_ok:

            m_ring_set_enable = m_check.isChecked();

            m_ring_set_ipaddr = m_edit_ipaddr.getText().toString();
            m_ring_set_phone_ipaddr = m_edit_phone_ipaddr.getText().toString();

            m_ring_set_number = m_edit_number.getText().toString();

            m_ring_set_mic = volumeControl_mic.getProgress();
            m_ring_set_volume = volumeControl.getProgress();

            saveConfig();
            if (TextUtils.isEmpty(m_ring_set_number)) {
                
                m_ring_set_number = "0";
            }

            if(MyApplication.is_valid(m_ring_set_ipaddr) == false)
            {
                mainActivity.showTips(R.string.str_ip_error);
                break;
            }
            
            if(!TextUtils.isEmpty(m_ring_set_number) && MyApplication.is_valid(m_ring_set_phone_ipaddr) == false)
            {
                mainActivity.showTips(R.string.str_ip_error);
                break;
            }            
            
            m_wait_timer = MAX_WAIT_TIME;
            m_back_timer = MAX_BACK_TIME;

            String[] str_arr = new String[6];

            str_arr[0] = m_ring_set_ipaddr;
            str_arr[1] = Boolean.toString(m_ring_set_enable);
            str_arr[2] = m_ring_set_number;
            str_arr[3] = String.valueOf(m_ring_set_mic);
            str_arr[4] = String.valueOf(m_ring_set_volume);
            str_arr[5] = m_ring_set_phone_ipaddr;            
            mainActivity.setRingNumber(str_arr);

            mainActivity.showTips(R.string.str_waiting);
            
		   break;
	     case R.id.ring_set_button_esc:
           m_back_timer = 1;

		      break;
       case R.id.ring_set_button_get:

            m_ring_set_ipaddr = m_edit_ipaddr.getText().toString();
           if(MyApplication.is_valid(m_ring_set_ipaddr) == false)
           {
               mainActivity.showTips(R.string.str_ip_error);
               break;
           }
           
            m_wait_timer = MAX_WAIT_TIME;
            m_back_timer = MAX_BACK_TIME;

            mainActivity.getRingNumber(m_ring_set_ipaddr);

            mainActivity.showTips(R.string.str_waiting);
           
   		    break;
	     }
       }
    };

   private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
   public void timeout() {
    // TODO Auto-generated method stub

    if(m_is_active == false)    return;
    Log.i(TAG,"timertask");

    if(m_wait_timer > 0)
    {
        m_wait_timer--;
        if(m_wait_timer == 0)
        {
            postMessage(MyApplication.WAIT_TIMEOUT);
            
        }
    }
    if(m_back_timer > 0)
    {
        m_back_timer--;
        if(m_back_timer == 0)
        {
             postMessage(MyApplication.GOTO_BACK);

        }
    }
}
  

   public final StaticHandler mHandler = new StaticHandler(this);
    private  static class StaticHandler extends Handler{
    private final WeakReference<RingSettingActivity> mActivity;
    public StaticHandler(RingSettingActivity activity)
    {
      mActivity = new WeakReference<RingSettingActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
        RingSettingActivity activity = mActivity.get();
        if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
            activity.mainActivity.loadMySettingFragment();

            //Toast.makeText(OptSOSActivity.this, R.string.str_no_respose, Toast.LENGTH_LONG).show();
            break;
            case MyApplication.WAIT_TIMEOUT:

            activity.mainActivity.showTips(R.string.str_no_respose);

            break;
           case MyApplication.SEND_OK:

            activity.mainActivity.showTips(R.string.str_setting_ok);

            break;
          case MyApplication.GET_OK:

            activity.mainActivity.showTips(R.string.str_setting_ok);

            activity.m_edit_phone_ipaddr.setText(activity.m_ring_set_phone_ipaddr);  

            activity.m_edit_number.setText(activity.m_ring_set_number);

            activity.m_check.setChecked(activity.m_ring_set_enable);

            activity.volumeControl_mic.setProgress(activity.m_ring_set_mic);
            activity.volumeControl.setProgress(activity.m_ring_set_volume);
            break;
        }

    }
  }    
      
    public int OnBroadcastReceiver(Intent intent) {
        int ret = -1;
        String action = intent == null ? "" : intent.getAction();

        if(m_is_active == false)    return ret;

        if(MyApplication.RING_SET_REPLY.equals(action))
        {
            postMessage(MyApplication.SEND_OK);
           
            m_wait_timer = 0;
            m_back_timer = 1;
            ret = 0;
        }
        else if(MyApplication.RING_GET_REPLY.equals(action))
        {
            postMessage(MyApplication.GET_OK);
            
            m_wait_timer = 0;
            ret = 0;

            String enable = intent.getStringExtra(MyApplication.EXTRA_RING_ENABLE);
            m_ring_set_enable = Boolean.parseBoolean(enable);
            m_ring_set_number = intent.getStringExtra(MyApplication.EXTRA_RING_NUMBER);
            String val = intent.getStringExtra(MyApplication.EXTRA_RING_MIC);
            m_ring_set_mic = Integer.valueOf(val);
            val = intent.getStringExtra(MyApplication.EXTRA_RING_VOLUME);
            m_ring_set_volume = Integer.valueOf(val);

            m_ring_set_phone_ipaddr = intent.getStringExtra(MyApplication.EXTRA_PHONE_IP);

            saveConfig();
        }

        return ret;
    }


}
