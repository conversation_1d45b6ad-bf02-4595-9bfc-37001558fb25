package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.CheckBox;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;

import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.Button;
public class SOSActivity extends Fragment {
    private static final String TAG="SOSActivity";
    MainActivity mainActivity;
   
    private int m_back_timer = 0;

    private MyApplication myApp;
    
    private static final int MAX_BACK_TIME = 60;
    private static final int MAX_FINISH_TIME = 1;

    private EditText[] m_editText = new EditText[5];
    private CheckBox[] m_checkBox = new CheckBox[5];

    private RadioButton[] m_radioButtonVoip1 = new RadioButton[5];
    private RadioButton[] m_radioButtonVoip2 = new RadioButton[5];
    private RadioButton[] m_radioButtonVoip3 = new RadioButton[5];

    private Button m_button_prev;
    private Button m_button_ok;
    private int m_index;

    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
          // TODO Auto-generated method stub

            super.onCreateView(inflater, container, savedInstanceState);

            View rootView = inflater.inflate(R.layout.set_sos, null);
            //initView(rootView);
            mainActivity = (MainActivity) getActivity();
            myApp = (MyApplication) mainActivity.getApplicationContext();
            return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);
       m_is_active = !hidden;
       if (!hidden){

           mainActivity.receiver.broadcastReceiver = null;

           getSOSConfig();
           m_back_timer = MAX_BACK_TIME;
           //setOnlineStatus();
           Log.i(TAG,"onHiddenChanged");
         
       }
       else {
        
         m_back_timer = 0;

       }
   }


   private void getSOSConfig()
   {
       boolean[] is_enable = myApp.m_sos_enable;
       int[] select_line = myApp.m_sos_select_line;
       String[] number = myApp.m_sos_number;

       for(int i=0;i<5;i++)
       {
           m_checkBox[i].setChecked(is_enable[i]);
           m_editText[i].setText(number[i]);
           if(select_line[i] == 0)
   		     {
   		          m_radioButtonVoip1[i].setChecked(true);
   		          m_radioButtonVoip2[i].setChecked(false);
   		          m_radioButtonVoip3[i].setChecked(false);
   		     }
   		     else if(select_line[i] == 1)
   		     {
   		         m_radioButtonVoip1[i].setChecked(false);
   		         m_radioButtonVoip2[i].setChecked(true);
   		         m_radioButtonVoip3[i].setChecked(false);
   		     }
   		     else
   		     {
   		         m_radioButtonVoip1[i].setChecked(false);
   		         m_radioButtonVoip2[i].setChecked(false);
   		         m_radioButtonVoip3[i].setChecked(true);
   		     }


       }

   }

   private void initView(View view) {

     m_editText[0]= (EditText)view.findViewById(R.id.editText1);
     m_editText[1]= (EditText)view.findViewById(R.id.editText2);
     m_editText[2]= (EditText)view.findViewById(R.id.editText3);
     m_editText[3]= (EditText)view.findViewById(R.id.editText4);
     m_editText[4]= (EditText)view.findViewById(R.id.editText5);

     m_checkBox[0]= (CheckBox)view.findViewById(R.id.checkB_enable1);
     m_checkBox[1]= (CheckBox)view.findViewById(R.id.checkB_enable2);
     m_checkBox[2]= (CheckBox)view.findViewById(R.id.checkB_enable3);
     m_checkBox[3]= (CheckBox)view.findViewById(R.id.checkB_enable4);
     m_checkBox[4]= (CheckBox)view.findViewById(R.id.checkB_enable5);

     m_radioButtonVoip2[0]= (RadioButton)view.findViewById(R.id.voip2_1);
     m_radioButtonVoip2[1]= (RadioButton)view.findViewById(R.id.voip2_2);
     m_radioButtonVoip2[2]= (RadioButton)view.findViewById(R.id.voip2_3);
     m_radioButtonVoip2[3]= (RadioButton)view.findViewById(R.id.voip2_4);
     m_radioButtonVoip2[4]= (RadioButton)view.findViewById(R.id.voip2_5);

     m_radioButtonVoip1[0]= (RadioButton)view.findViewById(R.id.voip1_1);
     m_radioButtonVoip1[1]= (RadioButton)view.findViewById(R.id.voip1_2);
     m_radioButtonVoip1[2]= (RadioButton)view.findViewById(R.id.voip1_3);
     m_radioButtonVoip1[3]= (RadioButton)view.findViewById(R.id.voip1_4);
     m_radioButtonVoip1[4]= (RadioButton)view.findViewById(R.id.voip1_5);

     m_radioButtonVoip3[0]= (RadioButton)view.findViewById(R.id.voip3_1);
     m_radioButtonVoip3[1]= (RadioButton)view.findViewById(R.id.voip3_2);
     m_radioButtonVoip3[2]= (RadioButton)view.findViewById(R.id.voip3_3);
     m_radioButtonVoip3[3]= (RadioButton)view.findViewById(R.id.voip3_4);
     m_radioButtonVoip3[4]= (RadioButton)view.findViewById(R.id.voip3_5);

     for(int i=0;i<5;i++)
     {
         m_radioButtonVoip1[i].setChecked(true);
         m_radioButtonVoip2[i].setChecked(false);
         m_radioButtonVoip3[i].setChecked(false);
     }

     m_button_prev = (Button)view.findViewById(R.id.set_sos_button_prev);

     m_button_ok = (Button)view.findViewById(R.id.set_sos_button_ok);

       initOnClickListener(view);

   }

  

    private void initOnClickListener(View view) {

        int my_ids[] = {
          R.id.set_sos_button_prev,R.id.set_sos_button_ok
        };


        Button b = null;
        for( int i=0 ; i< my_ids.length ; ++i )
                if( ( b = (Button)view.findViewById( my_ids[i]) ) != null )
                        b.setOnClickListener(btnDoListener);
    }



    private Button.OnClickListener btnDoListener=new Button.OnClickListener(){
        @Override
	      public void onClick(View v)
        {
	          switch(v.getId())
	          {
	          case R.id.set_sos_button_prev:
		            m_back_timer = MAX_FINISH_TIME;
		        break;
	          case R.id.set_sos_button_ok:

		        send_sos_cmd();
		        mainActivity.showTips(R.string.str_setting_ok);
		        m_back_timer = MAX_FINISH_TIME;
		        break;

	         }
        }
    };

    private void send_sos_cmd()
    {
	    boolean[] is_enable = new boolean[5];
        int[] select_line = new int[5];
        String[] number = new String[5];

        for(int i=0;i<5;i++)
        {
            is_enable[i] = m_checkBox[i].isChecked();
            number[i] = m_editText[i].getText().toString();
            if(m_radioButtonVoip1[i].isChecked())
            {
                select_line[i] = 0;
            }else if(m_radioButtonVoip2[i].isChecked())
            {
                select_line[i] = 1;
            }
            else
            {
                select_line[i] = 2;
            }

        }

        myApp.setSOS(mainActivity,is_enable,select_line,number);

    }

 public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<SOSActivity> mActivity;
    public StaticHandler(SOSActivity activity)
    {
      mActivity = new WeakReference<SOSActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
        SOSActivity activity = mActivity.get();
        if(activity == null) return;

	    switch(msg.what)
	    {
	        case MyApplication.GOTO_BACK:
                activity.mainActivity.loadMySettingFragment();

		        //Toast.makeText(OptSOSActivity.this, R.string.str_no_respose, Toast.LENGTH_LONG).show();
		        break;
	   
	    }
    }
  }
  
    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
   public void timeout() {
        // TODO Auto-generated method stub
        if(m_is_active == false)    return;
        Log.i(TAG,"timertask");

         if(m_back_timer > 0)
         {
           m_back_timer--;
           if(m_back_timer == 0)
           {
            postMessage(MyApplication.GOTO_BACK);
           }
         }
    }

}
