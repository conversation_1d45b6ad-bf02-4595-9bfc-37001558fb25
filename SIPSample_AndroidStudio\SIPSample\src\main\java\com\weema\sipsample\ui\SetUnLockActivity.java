package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.ImageButton;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;
import android.widget.EditText;
import android.graphics.Color;
public class SetUnLockActivity extends Fragment {

    private static final String TAG="SetUnLockActivity";
    MainActivity mainActivity;

    private int m_back_timer = 0;

    private MyApplication myApp;
    
    private static final int MAX_BACK_TIME = 20;
    private static final int MAX_FINISH_TIME = 1;

    private EditText old_editText;
    private EditText new_editText;

    private String m_str_title;
    private String m_str_old_title;
    private String m_str;
    private String m_str_old;
    private Boolean m_is_new;
    private String m_str_passwd;

    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
          // TODO Auto-generated method stub

            super.onCreateView(inflater, container, savedInstanceState);

            View rootView = inflater.inflate(R.layout.set_unlock, null);
            //initView(rootView);
            mainActivity = (MainActivity) getActivity();
            myApp = (MyApplication) mainActivity.getApplicationContext();
            return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
       //activity.receiver.broadcastReceiver = null;
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;
       if (!hidden){
           
           m_str = "";
           m_str_old = "";
           enter_new_text(false);
           m_str_passwd = myApp.m_str_passwd;

           mainActivity.receiver.broadcastReceiver = null;

           m_back_timer = MAX_BACK_TIME;
           //setOnlineStatus();
           Log.i(TAG,"onHiddenChanged");
           
       }
       else {
         
         m_back_timer = 0;

       }
   }


   private void initView(View view) {

       m_str_title = getResources().getString(R.string.str_new_passwd);
       m_str_old_title = getResources().getString(R.string.str_old_passwd);

       old_editText = (EditText)view.findViewById(R.id.set_unlock_old_editText);
       old_editText.setOnClickListener(textDoListener);

       new_editText = (EditText)view.findViewById(R.id.set_unlock_new_editText);
       new_editText.setOnClickListener(textDoListener);     initOnClickListener(view);

       initOnClickListener(view);
   }

    private void initOnClickListener(View view) {

        int my_ids[] = {
            R.id.set_unlock_dialButton_0,R.id.set_unlock_dialButton_1,R.id.set_unlock_dialButton_2,
            R.id.set_unlock_dialButton_3,R.id.set_unlock_dialButton_4,R.id.set_unlock_dialButton_5,
            R.id.set_unlock_dialButton_6,R.id.set_unlock_dialButton_7,R.id.set_unlock_dialButton_8,
            R.id.set_unlock_dialButton_9,R.id.set_unlock_dialButton_esc,R.id.set_unlock_dialButton_ok
        };


        ImageButton b = null;
        for( int i=0 ; i< my_ids.length ; ++i )
                if( ( b = (ImageButton)view.findViewById( my_ids[i]) ) != null )
                        b.setOnClickListener(btnDoListener);
    }

    private View.OnClickListener textDoListener=new View.OnClickListener(){
        @Override
	      public void onClick(View v)
	      {
	          switch(v.getId())
	          {
	          case R.id.set_unlock_old_editText:
		        enter_new_text(false);

		        break;
	          case R.id.set_unlock_new_editText:
		        enter_new_text(true);

		        break;

	          }
	      }
    };

    private void enter_new_text(Boolean is_new)
    {
	      if(is_new)
	      {
	          m_is_new = true;
	          new_editText.setTextColor(Color.CYAN);
	          new_editText.setText(m_str_title+m_str);

	          old_editText.setTextColor(Color.WHITE);
	          old_editText.setText(m_str_old_title+m_str_old);

	      }
	      else
	      {
	          m_is_new = false;
	          old_editText.setTextColor(Color.CYAN);
	          old_editText.setText(m_str_old_title+m_str_old);

	          new_editText.setTextColor(Color.WHITE);
	          new_editText.setText(m_str_title+m_str);

	      }
    }

    private View.OnClickListener btnDoListener=new View.OnClickListener(){
        @Override
	      public void onClick(View v)
        {
            m_back_timer = MAX_BACK_TIME;
	          String str="";
	          switch(v.getId())
	          {
	          case R.id.set_unlock_dialButton_0:

		        str = "0";
		        break;

	          case R.id.set_unlock_dialButton_1:
		        str = "1";
		        break;
            case R.id.set_unlock_dialButton_2:
                str = "2";
		        break;
            case R.id.set_unlock_dialButton_3:
                str = "3";
		        break;
            case R.id.set_unlock_dialButton_4:
		        str = "4";
		        break;
            case R.id.set_unlock_dialButton_5:
		        str = "5";
		        break;
            case R.id.set_unlock_dialButton_6:
		        str = "6";
		        break;
            case R.id.set_unlock_dialButton_7:
		        str = "7";
		        break;
            case R.id.set_unlock_dialButton_8:
		        str = "8";
		        break;
            case R.id.set_unlock_dialButton_9:
		        str = "9";
		        break;
            case R.id.set_unlock_dialButton_esc:
		        str ="";
		        if(m_is_new)
		            m_str = "";
		        else
		            m_str_old = "";

		        break;
            case R.id.set_unlock_dialButton_ok:
		        break;
	      }

	      if(v.getId() == R.id.set_unlock_dialButton_ok)
	      {
		        if(m_is_new)
	 	        {
		            if(m_str.length() != 4)
		            {
		                mainActivity.showTips(R.string.str_4_word);
		            }
	  	          else
		            {
                    if(m_str_old.equals(m_str_passwd))
                    {
                        myApp.setLockPasswd(mainActivity,m_str);
                        mainActivity.showTips(R.string.str_setting_ok);
                        m_back_timer = MAX_FINISH_TIME;

                    }
                    else
                    {
		                    mainActivity.showTips(R.string.str_passwd_error);
                        enter_new_text(false);
                    }

		            }
	           }
	           else
	           {
		              enter_new_text(true);
	           }
	      }
	      else
	      {
		        if(m_is_new)
		        {
		            m_str += str;
	              new_editText.setText(m_str_title+m_str);
	          }
	          else
	          {
		             m_str_old += str;
	               old_editText.setText(m_str_old_title+m_str_old);

	          }
	       }
	     }
    };

    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout() {
        // TODO Auto-generated method stub
        if(m_is_active == false)    return;
        Log.i(TAG,"timertask");

         if(m_back_timer > 0)
         {
           m_back_timer--;
           if(m_back_timer == 0)
           {
            postMessage(MyApplication.GOTO_BACK);
           }
         }
    }

   public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<SetUnLockActivity> mActivity;
    public StaticHandler(SetUnLockActivity activity)
    {
      mActivity = new WeakReference<SetUnLockActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      	SetUnLockActivity activity = mActivity.get();
      	if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
            activity.mainActivity.loadOptSOSActivity();

             break;

        }
    }
  }

}
