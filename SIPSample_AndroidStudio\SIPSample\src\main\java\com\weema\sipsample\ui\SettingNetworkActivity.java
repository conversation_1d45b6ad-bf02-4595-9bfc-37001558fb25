package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.EditText;
import android.widget.CheckBox;

import android.widget.Button;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;

import android.content.Intent;
import 	android.text.TextUtils;
import android.widget.RadioButton;
import android.util.Patterns;
import 	java.nio.ByteBuffer;
import android.os.Environment;
import java.io.File;
import java.io.FileOutputStream;
import 	java.io.FileInputStream;
public class SettingNetworkActivity extends Fragment {

    private static final String TAG="SettingNetworkActivity";
    private MainActivity mainActivity;

    private int m_back_timer = 0;
    private int m_wait_timer = 0;

    private MyApplication myApp;
    private static final int MAX_BACK_TIME = 60;
    private static final int MAX_WAIT_TIME = 10;

    EditText edit_ipaddr;
    EditText edit_netmask;

    EditText edit_gateway;
    EditText edit_dns;

    private boolean m_is_active;
    private boolean m_need_reboot;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
          // TODO Auto-generated method stub

            super.onCreateView(inflater, container, savedInstanceState);

            View rootView = inflater.inflate(R.layout.set_network, null);
            //initView(rootView);
            mainActivity = (MainActivity) getActivity();
            myApp = (MyApplication) mainActivity.getApplicationContext();
            return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;

       m_need_reboot = false;
       if (!hidden){


          myApp.read_ethernet_file();

          edit_ipaddr.setText(myApp.m_ipaddr);
          edit_netmask.setText(myApp.m_netmask);
          edit_gateway.setText(myApp.m_gateway);
          edit_dns.setText(myApp.m_dns);
          mainActivity.receiver.broadcastReceiver = null;
           
           m_back_timer = MAX_BACK_TIME;
           //setOnlineStatus();
           Log.i(TAG,"onHiddenChanged");
          
       }
       else {
         
         m_back_timer = 0;

       }
   }

 
   private void initView(View view) {

        edit_ipaddr= (EditText)view.findViewById(R.id.ipaddr);
        edit_netmask= (EditText)view.findViewById(R.id.netmask);
 
        edit_gateway= (EditText)view.findViewById(R.id.gateway);
        edit_dns= (EditText)view.findViewById(R.id.dns);
         
        Button btn_ok= (Button)view.findViewById(R.id.ring_set_button_ok);
        btn_ok.setOnClickListener(btnDoListerner);

        Button btn_esc= (Button)view.findViewById(R.id.ring_set_button_esc);
        btn_esc.setOnClickListener(btnDoListerner);

        onHiddenChanged(false);
   }

   private boolean save_setting()
   {
      boolean ret;
      String ipaddr = edit_ipaddr.getText().toString();
      ipaddr = ipaddr.trim();
      String netmask = edit_netmask.getText().toString();
      netmask = netmask.trim();
      String gateway = edit_gateway.getText().toString();
      gateway = gateway.trim();
      String dns = edit_dns.getText().toString();
      dns = dns.trim();

      boolean valid_ipaddr = Patterns.IP_ADDRESS.matcher(ipaddr).matches();
      boolean valid_gateway = Patterns.IP_ADDRESS.matcher(gateway).matches();
      boolean valid_dns = Patterns.IP_ADDRESS.matcher(dns).matches();

      String [] netmask_arr = netmask.split(".");

      ret = true;

      if(!valid_ipaddr)
      {
        mainActivity.showTips("ipaddr error");

        ret = false;
      }
      if(!valid_gateway)
      {
        mainActivity.showTips("gatewy error");

        ret = false;
      }

      if(!TextUtils.isEmpty(dns) && !valid_dns)
      {
        mainActivity.showTips("dns error");

        ret = false;
      }

      //save_file("**********","*************","***********","***********");

      if(ret)    
        myApp.save_file(ipaddr,netmask,gateway,dns);
      return ret;
    
   }
    private Button.OnClickListener btnDoListerner=new Button.OnClickListener(){
        @Override
	public void onClick(View v)
        {
	      switch(v.getId())
	      {
	      case R.id.ring_set_button_ok:
          boolean ret = save_setting();
          if(ret)
          {
            m_back_timer = 2;
            m_need_reboot = true;
            mainActivity.showTips("reboot");

          }
          
		   break;
	     case R.id.ring_set_button_esc:
           m_back_timer = 1;

		      break;
     
	     }
       }
    };

   private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout() {
      // TODO Auto-generated method stub
      if(m_is_active == false)    return;
      Log.i(TAG,"timertask");

      if(m_back_timer > 0)
      {
        m_back_timer--;
        if(m_back_timer == 0)
        {
          postMessage(MyApplication.GOTO_BACK);
          
        }
      }
    }
  
  public void goBack()
  {
    if(m_need_reboot)
    {
      myApp.reboot();  
    }
    mainActivity.loadMySettingFragment();

  }
  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<SettingNetworkActivity> mActivity;
    public StaticHandler(SettingNetworkActivity activity)
    {
      mActivity = new WeakReference<SettingNetworkActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
        SettingNetworkActivity activity = mActivity.get();
        if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
                activity.goBack();
                  
            break;
        }

    }
  }    

}
