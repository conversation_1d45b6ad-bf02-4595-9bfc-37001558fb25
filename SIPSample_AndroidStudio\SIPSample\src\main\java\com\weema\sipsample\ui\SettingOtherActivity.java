package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.CheckBox;

import android.widget.Button;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;

import android.content.Intent;
import 	android.text.TextUtils;
import android.widget.RadioButton;
public class SettingOtherActivity extends Fragment {

    private static final String TAG="SettingOtherActivity";
    private MainActivity mainActivity;

    private int m_back_timer = 0;
    private int m_wait_timer = 0;

    private MyApplication myApp;
    private static final int MAX_BACK_TIME = 60;
    private static final int MAX_WAIT_TIME = 10;

    private CheckBox m_check;
    private CheckBox m_notify_check;
    private CheckBox m_record_check;
    private CheckBox m_sostalk_check;
    private CheckBox m_can_force_mgr_check;
    private CheckBox m_debug_check;

    private RadioButton[] m_radio = new RadioButton[3];
   

    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
          // TODO Auto-generated method stub

            super.onCreateView(inflater, container, savedInstanceState);

            View rootView = inflater.inflate(R.layout.set_other, null);
            //initView(rootView);
            mainActivity = (MainActivity) getActivity();
            myApp = (MyApplication) mainActivity.getApplicationContext();
            return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;

       if (!hidden){

           mainActivity.receiver.broadcastReceiver = null;
          get_setting_val();
           do_set_ctrl_bz();
           
           m_back_timer = MAX_BACK_TIME;
           //setOnlineStatus();
           Log.i(TAG,"onHiddenChanged");
          
       }
       else {
         
         m_back_timer = 0;

       }
   }

    private void get_setting_val(){

      m_can_force_mgr_check.setChecked(myApp.settingModel.m_can_force_mgr);
    }
   private void do_set_ctrl_bz()
   {
    m_check.setChecked(myApp.is_record_enable);
    m_notify_check.setChecked(myApp.m_is_door_notify);
    m_record_check.setChecked(myApp.m_video_record_enable);
    m_sostalk_check.setChecked(myApp.m_sostalk_enable);
    m_debug_check.setChecked(myApp.m_is_debug);

    int val = myApp.m_ctrl_bz;
    if(val == 0)
    {
      m_radio[0].setChecked(true);
      m_radio[1].setChecked(false);
      m_radio[2].setChecked(false);
    }
    else if(val == 1)
    {
      m_radio[0].setChecked(false);
      m_radio[1].setChecked(true);
      m_radio[2].setChecked(false);
    }
    else 
    {
      m_radio[0].setChecked(false);
      m_radio[1].setChecked(false);
      m_radio[2].setChecked(true);          
    }
           
   }
   private void initView(View view) {

         m_check = (CheckBox)view.findViewById(R.id.checkbox_is_enable);
         m_notify_check = (CheckBox)view.findViewById(R.id.checkbox_notify_enable);
         m_record_check = (CheckBox)view.findViewById(R.id.checkbox_record_enable);
         m_sostalk_check = (CheckBox)view.findViewById(R.id.checkbox_sostalk_enable);   
         m_can_force_mgr_check = (CheckBox)view.findViewById(R.id.checkbox_can_force_mgr_enable);         
         m_debug_check = (CheckBox)view.findViewById(R.id.checkbox_debug_enable);         

         m_radio[0]= (RadioButton)view.findViewById(R.id.bz_0);
         m_radio[1]= (RadioButton)view.findViewById(R.id.bz_1);
         m_radio[2]= (RadioButton)view.findViewById(R.id.bz_2);
          
         Button btn_ok= (Button)view.findViewById(R.id.ring_set_button_ok);
         btn_ok.setOnClickListener(btnDoListerner);

         Button btn_esc= (Button)view.findViewById(R.id.ring_set_button_esc);
         btn_esc.setOnClickListener(btnDoListerner);

         onHiddenChanged(false);
   }
 
  private void do_ctrl_bz()
  {
    int val = 1;

    for(int i=0;i<3;i++)
    {
      if(m_radio[i].isChecked())
      {
        val = i;
      }

    }

    myApp.setCtrlBZ(val);

  }
    private Button.OnClickListener btnDoListerner=new Button.OnClickListener(){
        @Override
	public void onClick(View v)
        {
	      switch(v.getId())
	      {
	      case R.id.ring_set_button_ok:
           myApp.settingModel.setCanForceMgr(m_can_force_mgr_check.isChecked());
           myApp.setRecordEnable(m_check.isChecked());
           myApp.setDoorNotify(m_notify_check.isChecked());
           myApp.setDoorRecord(m_record_check.isChecked());
           myApp.setSOSTalkEnable(m_sostalk_check.isChecked());
           myApp.m_is_debug = m_debug_check.isChecked();
           
           do_ctrl_bz();
           m_back_timer = 1;
		   break;
	     case R.id.ring_set_button_esc:
           m_back_timer = 1;

		      break;
     
	     }
       }
    };

   private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout() {
      // TODO Auto-generated method stub
      if(m_is_active == false)    return;
      Log.i(TAG,"timertask");

      if(m_wait_timer > 0)
      {
        m_wait_timer--;
        if(m_wait_timer == 0)
        {
          postMessage(MyApplication.WAIT_TIMEOUT);
      
        }
      }

      if(m_back_timer > 0)
      {
        m_back_timer--;
        if(m_back_timer == 0)
        {
          postMessage(MyApplication.GOTO_BACK);
          
        }
      }
    }
    private void handle_wait_timeout()
    {
        mainActivity.showTips(R.string.str_no_respose);

    }
  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<SettingOtherActivity> mActivity;
    public StaticHandler(SettingOtherActivity activity)
    {
      mActivity = new WeakReference<SettingOtherActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
        SettingOtherActivity activity = mActivity.get();
        if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
                activity.mainActivity.loadMySettingFragment();

                break;
            case MyApplication.WAIT_TIMEOUT:
                activity.handle_wait_timeout();
                

            break;
        }

    }
  }    

}
