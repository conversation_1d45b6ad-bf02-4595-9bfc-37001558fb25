package com.weema.sipsample.ui;

import com.weema.R;

import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.Toast;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import com.weema.sipsample.util.SettingConfig;
import android.util.Log;
import android.widget.ImageButton;
import android.text.TextUtils ;

public class TranSetActivity extends Fragment implements OnClickListener{

	private MainActivity mainActivity;
  	private int m_back_timer = 0;
	private final int MAX_BACK_TIME = 60;

	private CheckBox cb_A, cb_B, cb_C;
	private CheckBox cb_A1, cb_B1, cb_C1;
	private CheckBox cb_A2, cb_B2, cb_C2;
	private CheckBox cb_A3, cb_B3, cb_C3;

	private EditText ed_A, ed_B, ed_C;
	private EditText ed_timeout;

	private Boolean m_is_active = false;

	View rootView;

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
							 Bundle savedInstanceState) {
		// TODO Auto-generated method stub
		super.onCreateView(inflater, container, savedInstanceState);
		mainActivity = (MainActivity)getActivity();

		rootView = inflater.inflate(R.layout.tran_setting, null);
		initView(rootView);
		onHiddenChanged(false);
			
      	return rootView;
	}
	@Override
	public void onClick(View v) {
		switch (v.getId()) {
			case R.id.btoffline:
				//offline();
				break;
			case R.id.btonline:
				//online();
				break;
			default:
				break;
		}

	}

	public void timeout()
	{
		if(m_is_active == false)    return;
		if(m_back_timer > 0)
		{
			m_back_timer--;

			if(m_back_timer == 0){
				postMessage(MyApplication.GOTO_BACK);

		    }

		}
	}
	private void show_config()
	{
		String data = SettingConfig.getFWD(mainActivity);
	
		if(TextUtils.isEmpty(data))  
		{
			cb_A.setChecked(true);

			ed_timeout.setText("40");
			return;

		}

      	Log.i("myapp",data);
		String[] arr_Str = data.split( "-");
		for (String token:arr_Str) {
	        Log.i("myapp",token);
      	}
		cb_A.setChecked(Boolean.parseBoolean(arr_Str[0]));
		cb_A1.setChecked(Boolean.parseBoolean(arr_Str[1]));
		cb_A2.setChecked(Boolean.parseBoolean(arr_Str[2]));
		cb_A3.setChecked(Boolean.parseBoolean(arr_Str[3]));
		cb_B.setChecked(Boolean.parseBoolean(arr_Str[4]));
		cb_B1.setChecked(Boolean.parseBoolean(arr_Str[5]));
		cb_B2.setChecked(Boolean.parseBoolean(arr_Str[6]));
		cb_B3.setChecked(Boolean.parseBoolean(arr_Str[7]));
		cb_C.setChecked(Boolean.parseBoolean(arr_Str[8]));
		cb_C1.setChecked(Boolean.parseBoolean(arr_Str[9]));
		cb_C2.setChecked(Boolean.parseBoolean(arr_Str[10]));
		cb_C3.setChecked(Boolean.parseBoolean(arr_Str[11]));
		ed_A.setText(arr_Str[12].replace("#", ""));
		ed_B.setText(arr_Str[13].replace("#", ""));
		ed_C.setText(arr_Str[14].replace("#", ""));
		ed_timeout.setText(arr_Str[15].replace("#", ""));
		
	}
	public void initView(View view)
	{
		ImageButton ok = (ImageButton) view.findViewById(R.id.yes);
		ok.setOnClickListener(btnDoListener);

		ImageButton no = (ImageButton) view.findViewById(R.id.no);
		no.setOnClickListener(btnDoListener);

		ed_A = (EditText) view.findViewById(R.id.edit_1);
		ed_B = (EditText) view.findViewById(R.id.edit_2);
		ed_C = (EditText) view.findViewById(R.id.edit_3);

    	ed_timeout = (EditText) view.findViewById(R.id.edit_timeout);

		cb_A = (CheckBox) view.findViewById(R.id.checkB_1);
		cb_B = (CheckBox) view.findViewById(R.id.checkB_2);
		cb_C = (CheckBox) view.findViewById(R.id.checkB_3);

		cb_A1 = (CheckBox) view.findViewById(R.id.checkC_11);
		cb_A2 = (CheckBox) view.findViewById(R.id.checkC_12);
		cb_A3 = (CheckBox) view.findViewById(R.id.checkC_13);
		cb_B1 = (CheckBox) view.findViewById(R.id.checkC_21);
		cb_B2 = (CheckBox) view.findViewById(R.id.checkC_22);
		cb_B3 = (CheckBox) view.findViewById(R.id.checkC_23);
		cb_C1 = (CheckBox) view.findViewById(R.id.checkC_31);
		cb_C2 = (CheckBox) view.findViewById(R.id.checkC_32);
		cb_C3 = (CheckBox) view.findViewById(R.id.checkC_33);

	  cb_A1.setVisibility(View.GONE);
		cb_A2.setVisibility(View.GONE);
		cb_A3.setVisibility(View.GONE);
	  cb_B1.setVisibility(View.GONE);
		cb_B2.setVisibility(View.GONE);
		cb_B3.setVisibility(View.GONE);
	  cb_C1.setVisibility(View.GONE);
		cb_C2.setVisibility(View.GONE);
		cb_C3.setVisibility(View.GONE);

		cb_A.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_B.setChecked(false);
					cb_C.setChecked(false);
					if (!cb_A1.isChecked() && !cb_A2.isChecked()
							&& !cb_A3.isChecked()) {
						cb_A1.setChecked(true);
					}
				}
				else
				{

				}
			}
		});


		cb_A1.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_A2.setChecked(false);
					cb_A3.setChecked(false);
				} else {

				}
			}
		});
		cb_A2.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_A1.setChecked(false);
					cb_A3.setChecked(false);
				} else {

				}
			}
		});
		cb_A3.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_A2.setChecked(false);
					cb_A1.setChecked(false);
				} else {

				}
			}
		});
		cb_B.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_A.setChecked(false);
					cb_C.setChecked(false);
					if (!cb_B1.isChecked() && !cb_B2.isChecked()
							&& !cb_B3.isChecked()) {
						cb_B1.setChecked(true);
					}
				}
			}
		});
		cb_B1.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_B2.setChecked(false);
					cb_B3.setChecked(false);
				} else {

				}
			}
		});
		cb_B2.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_B1.setChecked(false);
					cb_B3.setChecked(false);
				} else {

				}
			}
		});
		cb_B3.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_B2.setChecked(false);
					cb_B1.setChecked(false);
				} else {

				}
			}
		});
		cb_C.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_A.setChecked(false);
					cb_B.setChecked(false);
					if (!cb_C1.isChecked() && !cb_C2.isChecked()
							&& !cb_C3.isChecked()) {
						cb_C1.setChecked(true);
					}
				}
			}
		});
		cb_C1.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_C2.setChecked(false);
					cb_C3.setChecked(false);
				} else {

				}
			}
		});
		cb_C2.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_C1.setChecked(false);
					cb_C3.setChecked(false);
				} else {

				}
			}
		});
		cb_C3.setOnCheckedChangeListener(new OnCheckedChangeListener() {

			@Override
			public void onCheckedChanged(CompoundButton buttonView,
					boolean isChecked) {
				if (isChecked) {
					cb_C2.setChecked(false);
					cb_C1.setChecked(false);
				} else {

				}
			}
		});

	}
	
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);

        m_is_active = !hidden;

        if (!hidden){
            mainActivity = (MainActivity)getActivity();

            mainActivity.receiver.broadcastReceiver = null;
           
            m_back_timer = MAX_BACK_TIME;
          
			show_config();
        }
        else
        {
          
          m_back_timer = 0;

        }
    }
	
	private void btn_yes()
	{
	    String num1 = (ed_A.getText().toString()).replace(" ", "");
		  String num2 = (ed_B.getText().toString()).replace(" ", "");
		  String num3 = (ed_C.getText().toString()).replace(" ", "");
			String numTimeout = (ed_timeout.getText().toString()).replace(" ", "");

		  if(num1== null || num1.equals(""))
				  num1 = "";
		  if(num2== null || num2.equals(""))
				  num2 = "";

		  if(num3== null || num3.equals(""))
				  num3 = "";

			if(numTimeout== null || numTimeout.equals(""))
					 numTimeout = "40";

		  String str_enable = "0";
		  String str_type = "0";
		  String str_prefix = "1*";

		  if (cb_A.isChecked() || cb_B.isChecked() || cb_C.isChecked()) {
				  str_enable = "1";
				//Data.writePreferences(TranSetActivity.this, "isTranSet",
				//			"1");

		  if(cb_A.isChecked())
			{
			    str_type = "0";
				  if (cb_A1.isChecked()) {
					    str_prefix = "1*";

			    } else if (cb_A2.isChecked()) {
					    str_prefix = "2*";

			    } else if (cb_A3.isChecked()) {
					    str_prefix = "0*";

			    }else {
					    str_prefix = "1*";

			    }

		 }
		 else if(cb_B.isChecked())
		 {
			   str_type = "1";

				 if (cb_B1.isChecked()) {
					   str_prefix = "1*";

		     } else if (cb_B2.isChecked()) {
				   	  str_prefix = "2*";

			   } else if (cb_B3.isChecked()) {
					    str_prefix = "0*";

			    }else {
					    str_prefix = "1*";

			    }

			}
			else
			{
			    str_type = "2";

				  if (cb_C1.isChecked()) {
					    str_prefix = "1*";

			    } else if (cb_C2.isChecked()) {
					    str_prefix = "2*";

			    } else if (cb_C3.isChecked()) {
					   str_prefix = "0*";

			    }else {
					    str_prefix = "1*";

			    }

		  }
		}
		else
		{
				//Data.writePreferences(TranSetActivity.this, "isTranSet",
				//			"0");
				str_enable = "0";
				str_type = "0";

				if (cb_A1.isChecked()) {
			      str_prefix = "1*";

				} else if (cb_A2.isChecked()) {
			      str_prefix = "2*";

				} else if (cb_A3.isChecked()) {
			     str_prefix = "0*";

				}else {
			     str_prefix = "1*";
				}
		}

		StringBuffer sb = new StringBuffer();
		sb.append(cb_A.isChecked());
		sb.append("-");
		sb.append(cb_A1.isChecked());
		sb.append("-");
		sb.append(cb_A2.isChecked());
		sb.append("-");
		sb.append(cb_A3.isChecked());
		sb.append("-");
		sb.append(cb_B.isChecked());
		sb.append("-");
		sb.append(cb_B1.isChecked());
		sb.append("-");
		sb.append(cb_B2.isChecked());
		sb.append("-");
		sb.append(cb_B3.isChecked());
		sb.append("-");
		sb.append(cb_C.isChecked());
		sb.append("-");
		sb.append(cb_C1.isChecked());
		sb.append("-");
		sb.append(cb_C2.isChecked());
		sb.append("-");
		sb.append(cb_C3.isChecked());
		sb.append("-");
		sb.append(num1 + "#");
		sb.append("-");
		sb.append(num2 + "#");
		sb.append("-");
		sb.append(num3 + "#");
		sb.append("-");
		sb.append(numTimeout + "#");

    	Log.i("myapp",sb.toString());
    	SettingConfig.setFWD(mainActivity,sb.toString());


	}
    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }

  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<TranSetActivity> mActivity;
    public StaticHandler(TranSetActivity activity)
    {
      mActivity = new WeakReference<TranSetActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      	TranSetActivity activity = mActivity.get();
      	if(activity == null) return;

 		switch(msg.what)
		{
			case MyApplication.GOTO_BACK:

				activity.mainActivity.loadConsoleActivity();
				break;
			default:
				Toast.makeText(activity.mainActivity, "error", Toast.LENGTH_LONG).show();

				break;
		}

    }
  }

	private View.OnClickListener btnDoListener=new View.OnClickListener(){
		@Override
		public void onClick(View v)
		{
			switch(v.getId())
			{
				case R.id.yes:
        		btn_yes();
				MyApplication myApp = ((MyApplication) mainActivity.getApplicationContext());

				myApp.getConfigFWD();
				mainActivity.loadConsoleActivity();

				break;
				case R.id.no:

        		mainActivity.loadConsoleActivity();
				break;

			}

		}
	};


}
