package com.weema.sipsample.ui;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.AdapterView.OnItemLongClickListener;
import android.widget.BaseAdapter;
import android.widget.Button;

import android.widget.ListView;
import android.widget.TextView;

import android.app.Fragment;
import android.content.Context;

import com.weema.sipsample.util.SideBar;
import com.weema.sipsample.util.SideBar.OnTouchingLetterChangedListener;
import com.weema.sipsample.util.TongXunLu;
import com.weema.sipsample.util.CharacterParser;
import com.weema.sipsample.util.PinyinComparator;
import com.weema.sipsample.ui.ClearEditText;

import com.weema.R;

public class TxlActivity extends Fragment {

	private ListView txl_view;
	private SideBar sideBar;
	private TextView dialog;
	private MyAdapter adapter;
 	private Context mContext;
	private MyApplication myApplication;
	/**
	 * 汉字转换成拼音的类
	 */
	private CharacterParser characterParser;

	/**
	 * 根据拼音来排列ListView里面的数据类
	 */
	private PinyinComparator pinyinComparator;

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {

		super.onCreateView(inflater, container, savedInstanceState);

    	mContext = getActivity();
		myApplication = ((MyApplication) mContext.getApplicationContext());

		View	myView = inflater.inflate(R.layout.txl, container, false);

		Button editBT = (Button) myView.findViewById(R.id.yes);
		editBT.setOnClickListener(new View.OnClickListener() {

			@Override
			public void onClick(View v) {
				((MainActivity)mContext).loadEditActivity();
			}
		});
		Button no = (Button) myView.findViewById(R.id.no);
		no.setOnClickListener(new View.OnClickListener() {

			@Override
			public void onClick(View v) {
				((MainActivity)mContext).loadConsoleActivity();

			}
		});

		adapter = new MyAdapter(EditActivity.txl_list);
		txl_view = (ListView) myView.findViewById(R.id.txllistView);
		txl_view.setAdapter(adapter);

		txl_view.setOnItemClickListener(new OnItemClickListener() {

			@Override
			public void onItemClick(AdapterView<?> arg0, View arg1, int arg2,
					long arg3) {
				myApplication.outgoingCall(false,adapter.list.get(arg2).getHaoma());
			}
		});
		txl_view.setOnItemLongClickListener(new OnItemLongClickListener() {

			@Override
			public boolean onItemLongClick(AdapterView<?> arg0, View arg1,
					int arg2, long arg3) {

				EditActivity.Curr_TongXunLu = adapter.list.get(arg2);

				((MainActivity)mContext).loadEditActivity();

      			return false;
			}
		});

		// 实例化汉字转拼音类
		characterParser = CharacterParser.getInstance();

		pinyinComparator = new PinyinComparator();

		sideBar = (SideBar) myView.findViewById(R.id.sidrbar);
		dialog = (TextView) myView.findViewById(R.id.dialog);
		sideBar.setTextView(dialog);

		// 设置右侧触摸监听
		sideBar.setOnTouchingLetterChangedListener(new OnTouchingLetterChangedListener() {

		@Override
		public void onTouchingLetterChanged(String s) {

			// 该字母首次出现的位置
			int position = adapter.getPositionForSection(s.charAt(0));
			if (position != -1) {
				txl_view.setSelection(position);
			}

		}
		});

		ClearEditText mClearEditText = (ClearEditText) myView.findViewById(R.id.filter_edit);

		// 根据输入框输入值的改变来过滤搜索
		mClearEditText.addTextChangedListener(new TextWatcher() {

		@Override
		public void onTextChanged(CharSequence s, int start, int before,
			int count) {

			// 当输入框里面的值为空，更新为原来的列表，否则为过滤数据列表
			filterData(s.toString());
		}

		@Override
		public void beforeTextChanged(CharSequence s, int start, int count,
					int after) {

		}

		@Override
			public void afterTextChanged(Editable s) {

		}
		});

   		return myView;
	}

	@Override
	public void onResume() {
		super.onResume();

		// 根据a-z进行排序源数据
		Collections.sort(adapter.list, pinyinComparator);
		adapter.updateListView(adapter.list);
	}

	/**
	 * 根据输入框中的值来过滤数据并更新ListView
	 *
	 * @param filterStr
	 */
	private void filterData(String filterStr) {

		List<TongXunLu> filterDateList = new ArrayList<TongXunLu>();

		if (TextUtils.isEmpty(filterStr)) {
			filterDateList = EditActivity.txl_list;
		} else {
			filterDateList.clear();
			for (TongXunLu sortModel : EditActivity.txl_list) {
				String nc = sortModel.getXingming();
				if (nc.indexOf(filterStr.toString()) != -1
						|| characterParser.getSelling(nc).startsWith(
								filterStr.toString())) {
					filterDateList.add(sortModel);
				}
			}
		}

		// 根据a-z进行排序
		Collections.sort(filterDateList, pinyinComparator);
		adapter.updateListView(filterDateList);
	}

	private class MyAdapter extends BaseAdapter {

		private List<TongXunLu> list = null;
		HashMap<Integer, View> map = new HashMap<Integer, View>();

		public MyAdapter(List<TongXunLu> list) {
			if (list == null) {
				this.list = new ArrayList<TongXunLu>();
			} else {
				this.list = list;
			}
		}

		/**
		 * 当ListView数据发生变化时,调用此方法来更新ListView
		 *
		 * @param list
		 */
		public void updateListView(List<TongXunLu> list) {

			this.list = list;
			notifyDataSetChanged();
		}

		@Override
		public int getCount() {

			return list.size();
		}

		@Override
		public TongXunLu getItem(int position) {

			return list.get(position);
		}

		@Override
		public long getItemId(int position) {

			return position;
		}

		@Override
		public boolean isEnabled(int position) {

			return super.isEnabled(position);
		}

		@Override
		public View getView(int position, View convertView, ViewGroup parent) {

			ViewHolder holder = null;
			View view1 = convertView;

			if (map.get(position) == null) {
				view1 = LayoutInflater.from(mContext.getApplicationContext()).inflate(
						R.layout.txl_item, null);
				holder = new ViewHolder();

				//holder.touxiang = (ImageView) view1.findViewById(R.id.touxiang);
				holder.xingming = (TextView) view1.findViewById(R.id.xingming);
				holder.haoma = (TextView) view1.findViewById(R.id.haoma);
				holder.beizhu = (TextView) view1.findViewById(R.id.beizhu);
				holder.tvLetter = (TextView) view1.findViewById(R.id.catalog);
				view1.setTag(holder);
				map.put(position, view1);
			} else {
				view1 = map.get(position);
				holder = (ViewHolder) view1.getTag();
			}

			TongXunLu txl = getItem(position);

			// 根据position获取分类的首字母的Char ascii值
			int section = getSectionForPosition(position);

			// 如果当前位置等于该分类首字母的Char的位置 ，则认为是第一次出现
			if (position == getPositionForSection(section)) {
				holder.tvLetter.setVisibility(View.VISIBLE);
				holder.tvLetter.setText(txl.getSortLetters());
			} else {
				holder.tvLetter.setVisibility(View.GONE);
			}

			holder.xingming.setText(txl.getXingming());
			holder.haoma.setText(txl.getHaoma());
			holder.beizhu.setText(txl.getBeizhu());

			return view1;
		}

		/**
		 * 根据ListView的当前位置获取分类的首字母的Char ascii值
		 */
		public int getSectionForPosition(int position) {

			return list.get(position).getSortLetters().charAt(0);
		}

		/**
		 * 根据分类的首字母的Char ascii值获取其第一次出现该首字母的位置
		 */
		public int getPositionForSection(int section) {

			for (int i = 0; i < getCount(); i++) {
				String sortStr = list.get(i).getSortLetters();
				char firstChar = sortStr.toUpperCase().charAt(0);
				if (firstChar == section) {
					return i;
				}
			}

			return -1;
		}

	}

	private class ViewHolder {

		TextView xingming;
		TextView haoma;
		TextView beizhu;
		TextView tvLetter;
	}

	@Override
	public void onDestroy() {
		EditActivity.Curr_TongXunLu = null;

		super.onDestroy();
	}
}
