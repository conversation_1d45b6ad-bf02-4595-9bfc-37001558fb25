package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.ImageButton;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;

import android.widget.EditText;
public class UnLockActivity extends Fragment {

    private final String TAG="UnLockActivity";

    MainActivity  mainActivity;
    
    private int m_back_timer = 0;

    private MyApplication myApp;
    
    private static final int MAX_BACK_TIME = 20;
    private static final int MAX_FINISH_TIME = 2;

    private EditText new_editText;

    private String m_str_title;
    private String m_str_passwd;
    private String m_str;
    private boolean m_is_active;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  		Bundle savedInstanceState) {
        // TODO Auto-generated method stub

        super.onCreateView(inflater, container, savedInstanceState);

        View rootView = inflater.inflate(R.layout.unlock, null);
         //initView(rootView);
        mainActivity = (MainActivity) getActivity();
        myApp = (MyApplication) mainActivity.getApplicationContext();
        return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
       //activity.receiver.broadcastReceiver = null;
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;

       if (!hidden){

           clear_screen();
        
           m_str_passwd = myApp.m_str_passwd;
           mainActivity.receiver.broadcastReceiver = null;

           m_back_timer = MAX_BACK_TIME;
        
           Log.i(TAG,"onHiddenChanged");
          
       }
       else {
         
         m_back_timer = 0;

       }
   }


   private void initView(View view) {

   	  m_str_title = getResources().getString(R.string.str_passwd);

      new_editText = (EditText)view.findViewById(R.id.unlock_new_editText);

       initOnClickListener(view);

   }

    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout() {
        // TODO Auto-generated method stub

        if(m_is_active == false)    return;
        Log.i(TAG,"timertask");

        if(m_back_timer > 0)
        {
          m_back_timer--;
          if(m_back_timer == 0)
          {
            postMessage(MyApplication.GOTO_BACK);

          }
        }
   }

    private void initOnClickListener(View view) {

        int my_ids[] = {
            R.id.unlock_dialButton_0,R.id.unlock_dialButton_1,R.id.unlock_dialButton_2,
            R.id.unlock_dialButton_3,R.id.unlock_dialButton_4,R.id.unlock_dialButton_5,
            R.id.unlock_dialButton_6,R.id.unlock_dialButton_7,R.id.unlock_dialButton_8,
            R.id.unlock_dialButton_9,R.id.unlock_dialButton_esc,R.id.unlock_dialButton_ok
        };


        ImageButton b = null;
        for( int i=0 ; i< my_ids.length ; ++i )
            if( ( b = (ImageButton)view.findViewById( my_ids[i]) ) != null )
                 b.setOnClickListener(btnDoListener);
    }


    private View.OnClickListener btnDoListener=new View.OnClickListener(){
        @Override
	      public void onClick(View v)
        {
            m_back_timer = MAX_BACK_TIME;
	          String str="";
	          switch(v.getId())
	          {
	          case R.id.unlock_dialButton_0:

		        str = "0";
		        break;

	          case R.id.unlock_dialButton_1:
		        str = "1";
		        break;
            case R.id.unlock_dialButton_2:
            str = "2";
		        break;
            case R.id.unlock_dialButton_3:
            str = "3";
		        break;
            case R.id.unlock_dialButton_4:
		        str = "4";
		        break;
            case R.id.unlock_dialButton_5:
		        str = "5";
		        break;
            case R.id.unlock_dialButton_6:
		        str = "6";
		        break;
            case R.id.unlock_dialButton_7:
		        str = "7";
		        break;
            case R.id.unlock_dialButton_8:
		        str = "8";
		        break;
            case R.id.unlock_dialButton_9:
		        str = "9";
		        break;
            case R.id.unlock_dialButton_esc:

		        str ="";
		        m_str = "";

		        break;
            case R.id.unlock_dialButton_ok:
		        break;
	        }

	        if(v.getId() == R.id.unlock_dialButton_ok)
	        {
		          if(m_str.length() < 1)
		          {
		              mainActivity.showTips(R.string.str_1_word);

		          }
	  	        else
		          {
                    if (m_str.equals(m_str_passwd)) // CORRECT
            	    {
            		    myApp.setLock(mainActivity,false);

                    postMessage(MyApplication.MAIN_MENU);
            		  
                        myApp.send_update();  
            	    }
            	    else
            	    {
            		    mainActivity.showTips(R.string.str_passwd_error);
                        clear_screen();

            	    }

            	}      

	        }
	        else
	        {
		        m_str += str;
	            new_editText.setText(m_str_title+m_str);
	        }
	     }
    };
    private void clear_screen()
    {
        m_str = "";
	    new_editText.setText(m_str_title);

    }

  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<UnLockActivity> mActivity;
    public StaticHandler(UnLockActivity activity)
    {
      mActivity = new WeakReference<UnLockActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      	UnLockActivity activity = mActivity.get();
      	if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
            activity.mainActivity.loadOptSOSActivity();

             break;
             case MyApplication.MAIN_MENU:
             activity.mainActivity.showTips(R.string.str_setting_unlock_ok);

             activity.mainActivity.loadConsoleActivity();
 
              break;
        }
    }
  }

}
