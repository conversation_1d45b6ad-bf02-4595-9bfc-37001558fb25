
package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.Button;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;

import android.widget.EditText;
import android.widget.TextView;
import android.text.method.ScrollingMovementMethod;
import android.content.Intent;
import com.weema.sipsample.receiver.PortMessageReceiver;
import 	java.lang.StringBuffer;

public class VideoActivity extends Fragment implements PortMessageReceiver.BroadcastListener{
    private static final String TAG="VideoActivity";
    private  MainActivity mainActivity;

	private Button m_btn_get;
	private Button m_btn_exit;
	private EditText m_editText;
    private TextView m_textView;
    private TextView m_videoVer;

    private int m_back_timer = 0;

    private MyApplication myApp;
    
    private static final int MAX_BACK_TIME = 60;

    private String m_str_ipcam;
    private boolean m_is_active=false;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
  		// TODO Auto-generated method stub

  		super.onCreateView(inflater, container, savedInstanceState);

  		View rootView = inflater.inflate(R.layout.video_data, null);
  		//initView(rootView);
        mainActivity = (MainActivity) getActivity();
        myApp = (MyApplication) mainActivity.getApplicationContext();
  		return rootView;
  	}

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initView(view);
        onHiddenChanged(false);
    }
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        m_is_active = !hidden;

        if (!hidden){
            mainActivity = (MainActivity)getActivity();
            mainActivity.receiver.broadcastReceiver = this;

          
            String str_ip = myApp.m_video_ip;
	    
            m_editText.setText(str_ip);
            m_btn_get.setEnabled(true);
            show_ipcam();
            m_back_timer = MAX_BACK_TIME;
            //setOnlineStatus();
            Log.i(TAG,"onHiddenChanged");
           
        }
        else {
          
          m_back_timer = 0;

        }
    }

    private void show_ipcam()
    {
      StringBuffer sb = new StringBuffer();
       for(String str : myApp.ipcam_arr) {
         sb.append(str+"\n");
      }
      String str = sb.toString();   
      m_str_ipcam = str;    
      m_textView.setText(m_str_ipcam);

    }
    private void initView(View view) {

	    m_textView = (TextView) view.findViewById(R.id.textVideo);

	    m_textView.setMovementMethod(new ScrollingMovementMethod());
	    
	    m_videoVer = (TextView) view.findViewById(R.id.video_ver);
	    
	    m_editText = (EditText) view.findViewById(R.id.editVideoIP);

	    m_btn_get = (Button) view.findViewById(R.id.video_button_get);
        
	    m_btn_exit = (Button) view.findViewById(R.id.video_button_exit);
    
        initOnClickListener(view);

	
	    
	    //show_video_ver();
	    //show_video_file();
	

    }

    private void initOnClickListener(View view) {

        int my_ids[] = {
            R.id.video_button_get,R.id.video_button_exit,
        };

        Button b = null;
        for( int i=0 ; i< my_ids.length ; ++i )
                if( ( b = (Button)view.findViewById( my_ids[i]) ) != null )
                        b.setOnClickListener(viewDoListener);
    }

    private View.OnClickListener viewDoListener=new View.OnClickListener(){
        @Override
	    public void onClick(View v)
        {
	    switch(v.getId())
	    {
	    case R.id.video_button_exit:
            m_back_timer = 1;
		    
		    break;
	    case R.id.video_button_get:

	        // 啟動一個Thread(執行緒)，將要傳送的資料放進Runnable中，讓Thread執行
		    String str_ip = m_editText.getText().toString();
		
	        if(str_ip != null)
	        {
                myApp.setVideoIP(str_ip);
               
                m_btn_get.setEnabled(false);

                mainActivity.do_get_ipcam();

                mainActivity.showTips(R.string.str_waiting);

	        }
	        else
	        {
                mainActivity.showTips(R.string.str_video_ip_fail);

	        }
	        
	        break;
	    }
        }
    };

  public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<VideoActivity> mActivity;
    public StaticHandler(VideoActivity activity)
    {
      mActivity = new WeakReference<VideoActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
        VideoActivity activity = mActivity.get();
        if(activity == null) return;

	    switch(msg.what)
	    {
	        case MyApplication.GOTO_BACK:
                activity.mainActivity.loadMySettingFragment();

		        //Toast.makeText(OptSOSActivity.this, R.string.str_no_respose, Toast.LENGTH_LONG).show();
		        break;
	        case MyApplication.SEND_OK:
                
                activity.m_textView.setText(activity.m_str_ipcam);
                activity.mainActivity.showTips(R.string.str_setting_ok);
                activity.m_btn_get.setEnabled(true);
		        //Toast.makeText(OptSOSActivity.this, R.string.str_no_respose, Toast.LENGTH_LONG).show();
		        break;

	    }
    }
  }
  
    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
   public void timeout() {
        // TODO Auto-generated method stub
        if(m_is_active == false)    return;
        Log.i(TAG,"timertask");

         if(m_back_timer > 0)
         {
           m_back_timer--;
           if(m_back_timer == 0)
           {
            postMessage(MyApplication.GOTO_BACK);
           }
         }
    }
   
    public int OnBroadcastReceiver(Intent intent) {
        int ret = -1;
        String action = intent == null ? "" : intent.getAction();
        String ipcam  = intent.getStringExtra(MyApplication.EXTRA_IPCAM);

        Log.i(TAG,"OnBroadcastReceiver "+ipcam);
        if(MyApplication.IPCAM_ACTION.equals(action))
        {
            if(m_is_active)
            {
                m_str_ipcam = ipcam;
                postMessage(MyApplication.SEND_OK);
 
            }
            ret = 0;
        }
     
        return ret;
    }

}
