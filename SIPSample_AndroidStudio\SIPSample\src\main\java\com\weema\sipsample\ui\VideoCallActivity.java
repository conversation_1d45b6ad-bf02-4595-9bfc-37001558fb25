package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.content.Context;

import android.os.Bundle;
import android.view.View;

import android.widget.CheckBox;

import android.widget.Button;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;

import android.widget.EditText;

public class VideoCallActivity extends Fragment {

    private static final String TAG="VideoCallActivity";
    private MainActivity mainActivity;

    private int m_back_timer = 0;

    private MyApplication myApp;

    private static final int MAX_BACK_TIME = 60;

    private CheckBox m_checkB;

    private EditText m_edittext;
    private Button btn_ok;
    private Button btn_esc;

    private String m_monitor_number;
    private boolean m_monitor_enable;

    private boolean m_is_active;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
          // TODO Auto-generated method stub

            super.onCreateView(inflater, container, savedInstanceState);

            View rootView = inflater.inflate(R.layout.set_video_call, null);
            //initView(rootView);
            mainActivity = (MainActivity) getActivity();
            myApp = (MyApplication) mainActivity.getApplicationContext();
            return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;

       if (!hidden){

           mainActivity.receiver.broadcastReceiver = null;

           m_monitor_number = myApp.m_monitor_number;
           m_monitor_enable = myApp.m_monitor_enable;

           m_checkB.setChecked(m_monitor_enable);

           m_edittext.setText(m_monitor_number);
           m_back_timer = MAX_BACK_TIME;
           //setOnlineStatus();
           Log.i(TAG,"onHiddenChanged");
          
       }
       else {
       
         m_back_timer = 0;

       }
   }


   private void initView(View view) {
         m_checkB = (CheckBox)view.findViewById(R.id.video_call_checkB);

         m_edittext = (EditText)view.findViewById(R.id.video_call_editText1);

         btn_ok= (Button)view.findViewById(R.id.video_call_button_ok);
         btn_ok.setOnClickListener(btnDoListerner);

         btn_esc= (Button)view.findViewById(R.id.video_call_button_esc);
         btn_esc.setOnClickListener(btnDoListerner);
   }


    private Button.OnClickListener btnDoListerner=new Button.OnClickListener(){
        @Override
	public void onClick(View v)
        {
	      switch(v.getId())
	      {
	      case R.id.video_call_button_ok:

            m_monitor_enable = m_checkB.isChecked();
            m_monitor_number = m_edittext.getText().toString();
		    myApp.setMonitor(mainActivity,m_monitor_enable,m_monitor_number);

            mainActivity.showTips(R.string.str_setting_ok);

            m_back_timer = 1;

		   break;
	     case R.id.video_call_button_esc:
           m_back_timer = 1;

		      break;

	     }
       }
    };

   private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout() {
        // TODO Auto-generated method stub
        if(m_is_active == false)    return;
        Log.i(TAG,"timertask");

         if(m_back_timer > 0)
         {
           m_back_timer--;
           if(m_back_timer == 0)
           {
            postMessage(MyApplication.GOTO_BACK);
           }
         }
    }

   public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<VideoCallActivity> mActivity;
    public StaticHandler(VideoCallActivity activity)
    {
      mActivity = new WeakReference<VideoCallActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      	VideoCallActivity activity = mActivity.get();
      	if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
            activity.mainActivity.loadMySettingFragment();

             break;

        }
    }
  }
   

}
