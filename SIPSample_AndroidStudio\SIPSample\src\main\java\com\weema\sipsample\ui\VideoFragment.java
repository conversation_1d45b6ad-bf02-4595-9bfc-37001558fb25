package com.weema.sipsample.ui;

import com.portsip.PortSipErrorcode;
import com.portsip.PortSipSdk;
import com.portsip.PortSipEnumDefine;
import com.weema.R;

import android.media.AudioManager;
import android.content.Intent;

import android.content.DialogInterface;
import android.os.Bundle;
import android.app.Fragment;
import android.util.Log;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import android.view.WindowManager;

import android.widget.Button;
import android.view.Window;
import android.app.Dialog;
import android.app.AlertDialog;
import android.widget.EditText;

import android.widget.Toast;

import com.portsip.PortSIPVideoRenderer;
import com.weema.sipsample.receiver.PortMessageReceiver;
import com.weema.sipsample.service.PortSipService;
import com.weema.sipsample.util.CallManager;
import com.weema.sipsample.util.Session;
import com.weema.sipsample.util.Ring;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;
import java.util.Timer;
import java.util.TimerTask;
import android.content.DialogInterface.OnDismissListener;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.view.SurfaceView;
import com.pedro.vlc.VlcListener;
import com.pedro.vlc.VlcVideoLibrary;

import java.util.Arrays;
import org.videolan.libvlc.MediaPlayer;
import android.content.Context;
import 	java.lang.NumberFormatException;
import com.portsip.PortSipEnumDefine;
import android.widget.ImageView;
import com.weema.sipsample.service.WaService;
import io.reactivex.disposables.CompositeDisposable;
public class VideoFragment extends Fragment implements View.OnClickListener ,PortMessageReceiver.BroadcastListener,VlcListener{
  private static final String TAG="VideoFragment";
  private final int DEF_VOIP = -1;
	private MyApplication myApp;
    private static final int FORWARD_CALL = 1;
    private static final int REFER_CALL = 2;
    private static final int ONHOOK_CALL = 3;
	private static final int LOADCONSOLEACTIVITY = 4;
	private static final int ANSWER_ACTION = 5;
	private static final int AUTO_ONHOOK_ACTION = 6;

	
	private static final int CALL_CHANGE_ACTION_INCOMING = 7;
	private static final int CALL_CHANGE_ACTION_CONNECTED = 8;
	private static final int CALL_CHANGE_ACTION_CLOSED = 9;
	private static final int CALL_CHANGE_ACTION_FAILED = 10;
	private static final int PRESSKEY_ACTION = 11;
	private static final int DOOR_ACTION_CALL = 12;

  private static final int MAX_SOS_ONHOOK_TIME = 30;

  private static MyApplication sApp;
	private MainActivity activity;
	private static MainActivity sActivity;
  private static VideoFragment sthis;

	private PortSIPVideoRenderer remoteRenderScreen = null;
	private PortSIPVideoRenderer localRenderScreen = null;

	private PortSIPVideoRenderer.ScalingType scalingType = PortSIPVideoRenderer.ScalingType.SCALE_ASPECT_FILL;// SCALE_ASPECT_FIT or SCALE_ASPECT_FILL;
	//private PortSIPVideoRenderer.ScalingType scalingType = PortSIPVideoRenderer.ScalingType.SCALE_ASPECT_BALANCED;// SCALE_ASPECT_FIT or SCALE_ASPECT_FILL;
	private ImageButton imgSwitchCamera = null;
	private ImageButton imgScaleType = null;

	private TextView tv_call_number= null;
	private ImageButton bt_video= null;

  private int m_start_fwd;

  private EditText new_editText=null;

 private Dialog dialog=null;
 private  AlertDialog alertDialog=null;
 private String m_str="";
 private View rootView=null;


  private PortSipSdk mEngine;
  //private Timer timer=null;
  //private TimerTask timerTask=null;
	private TextView m_volume;
  private int m_onhook_timer=0;
  private int m_sos_onhook_timer=0;
  private final int MAX_ONHOOK_TIME = 30;

  private VlcVideoLibrary vlcVideoLibrary=null;

  private String[] options = new String[]{":fullscreen"};

private SurfaceView surfaceView ;

private boolean isVLCPlaying;
private int m_rec_timer;
private final int MAX_REC_TIME=35;
private boolean m_is_active = false;
private ImageView m_door_info;
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        activity = (MainActivity)getActivity();
        sActivity = activity;

        myApp = (MyApplication)activity.getApplication();
        sApp = myApp;

        sthis = this;
        rootView = inflater.inflate(R.layout.videoview, container, false);
        return rootView;
		}

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
		
		m_door_info = (ImageView) view.findViewById(R.id.door_info);

		imgSwitchCamera = (ImageButton)view.findViewById(R.id.ibcamera);
		imgScaleType = (ImageButton)view.findViewById(R.id.ibscale);

		imgScaleType.setOnClickListener(this);
		imgSwitchCamera.setOnClickListener(this);

		localRenderScreen = (PortSIPVideoRenderer)view.findViewById(R.id.local_video_view);
		remoteRenderScreen = (PortSIPVideoRenderer)view.findViewById(R.id.remote_video_view);

		tv_call_number = (TextView)view.findViewById(R.id.tv_call_number);
		bt_video = view.findViewById(R.id.bt_video);

    initOnClickListener(view);

    m_volume = (TextView)view.findViewById(R.id.volume);

    show_volume();
		activity.receiver.broadcastReceiver =this ;

		View view1 = view.findViewById(R.id.bt_mic_jia);
		//view1.setVisibility(View.GONE);

		view1 = view.findViewById(R.id.bt_mic_jian);
		//view1.setVisibility(View.GONE);

    imgScaleType.setVisibility(View.GONE);
    imgSwitchCamera.setVisibility(View.GONE);

    remoteRenderScreen.setScalingType(scalingType);

	surfaceView = (SurfaceView) view.findViewById(R.id.surfaceView);

	surfaceView.setVisibility(View.GONE);


	isVLCPlaying = false;

    onHiddenChanged(false);
    m_start_fwd = 0;

	}
	@Override
	public void onComplete() {
	  //Toast.makeText(this, "Playing", Toast.LENGTH_SHORT).show();
	}	
	@Override
	public void onError() {
	  //Toast.makeText(this, "Error, make sure your endpoint is correct", Toast.LENGTH_SHORT).show();
		if(vlcVideoLibrary != null)
			vlcVideoLibrary.stop();
	  //bStartStop.setText(getString(R.string.start_player));
	}
	@Override
	public void onBuffering(MediaPlayer.Event event) {
	
	}
  
  private void show_volume()
  {
    int speaker_vol;
    int mic_vol;
	// 喇叭音量
    speaker_vol = myApp.m_volume;
    // MIC
	mic_vol = myApp.mCurMicVol;
	// 喇叭音量硬體
	int speaker_vol_hw = Ring.getInstance(getActivity()).getVolume();

	Log.i(TAG, "show_volume mic: " + mic_vol + ", sp: " + speaker_vol + ", sp(hw): " + speaker_vol_hw);

    m_volume.setText(
		"mic: " + Integer.toString(mic_vol) +
		"\nsp: "+Integer.toString(speaker_vol) +
		"\nsp(hw): "+Integer.toString(speaker_vol_hw)
	);

  }
  private void ringing(Boolean value){
	m_onhook_timer = MAX_ONHOOK_TIME;
  }
  private final CompositeDisposable viewSubscriptions = new CompositeDisposable();

  @Override
  public void onHiddenChanged(boolean hidden) {
	  super.onHiddenChanged(hidden);
	  Log.i(TAG,"onHiddenChanged "+String.valueOf(hidden));
	  
	  show_volume();

	  m_is_active = !hidden;
	  m_rec_timer = 0;
	  if (hidden) {
		  viewSubscriptions.clear();
		  if(myApp.m_video_record_start)
		  {
			  myApp.copy_to_usb(true);
		  }
		
		  m_start_fwd = 0;

		  localRenderScreen.setVisibility(View.INVISIBLE);
		  localRenderScreen.setZOrderOnTop(false);
		  remoteRenderScreen.setVisibility(View.GONE);

		  surfaceView.setVisibility(View.GONE);

		  m_onhook_timer = 0;
		  m_sos_onhook_timer = 0;

		  if(isVLCPlaying)
		  {
			isVLCPlaying = false;
			if(vlcVideoLibrary != null)
	  			vlcVideoLibrary.stop();
		  }	
	  }
	  else
	  {
		viewSubscriptions.add(myApp.sipStore.getStream().subscribe(this::ringing));
		
		m_door_info.setVisibility(View.GONE);
		//initTimer();
		  myApp.m_video_record_start = false;
		  //m_is_offhook = false;
		  //m_is_onhook = false;
		  activity.stop_record();
		  
		  updateVideo();

		  rootView = getView();

		  activity.receiver.broadcastReceiver = this;
		  localRenderScreen.setVisibility(View.VISIBLE);
		  
		  localRenderScreen.setZOrderOnTop(true);

		  updateVideo1();

		  myApp.checkIPCam(tv_call_number.getText().toString());
		  
		  //myApp.m_is_ipcam = true;
		  //myApp.m_path = "rtsp://*************:554/video0.sdp";

		  if(myApp.m_is_ipcam)
		  {
			  showTips("ipcam "+myApp.m_path);
		  
			  isVLCPlaying = true;
			  if(vlcVideoLibrary == null)
			  {
			  	vlcVideoLibrary = new VlcVideoLibrary((Context)activity, this, surfaceView);
			  	vlcVideoLibrary.setOptions(Arrays.asList(options));
			  }

			  vlcVideoLibrary.play(myApp.m_path);
			  remoteRenderScreen.setVisibility(View.GONE);
			  surfaceView.setVisibility(View.VISIBLE);
	  
		  }
		  else if(myApp.m_is_sos_mode == false)
		  {
		  	remoteRenderScreen.setVisibility(View.GONE);
			surfaceView.setVisibility(View.GONE);
		

		  }
		  else 
		  {
			  surfaceView.setVisibility(View.GONE);

			  remoteRenderScreen.setVisibility(View.VISIBLE);
		  }
	  }
  }

    @Override
	public void onClick(View v)
	{
		PortSipSdk portSipLib = mEngine;
		switch (v.getId())
		{
			case R.id.ibcamera:
				myApp.mUseFrontCamera = !myApp.mUseFrontCamera;
				SetCamera(portSipLib, myApp.mUseFrontCamera);
				break;
			case R.id.ibscale:
				if (scalingType == PortSIPVideoRenderer.ScalingType.SCALE_ASPECT_FIT)
				{
					imgScaleType.setImageResource(R.drawable.aspect_fill);
					scalingType = PortSIPVideoRenderer.ScalingType.SCALE_ASPECT_FILL;
				}
				else if (scalingType == PortSIPVideoRenderer.ScalingType.SCALE_ASPECT_FILL)
				{
					imgScaleType.setImageResource(R.drawable.aspect_balanced);
					scalingType = PortSIPVideoRenderer.ScalingType.SCALE_ASPECT_BALANCED;
				}
				else
				{

					imgScaleType.setImageResource(R.drawable.aspect_fit);
					scalingType = PortSIPVideoRenderer.ScalingType.SCALE_ASPECT_FIT;
				}
				updateVideo();
				break;
		}
	}

  @Override
  public void onResume()
  {
    super.onResume();

  }

  @Override
	public void onPause() {
		Log.d(TAG, "onPause");
		m_start_fwd = 0;
	
		super.onPause();
	}

	public void forward_all()
	{
		Session cur = CallManager.Instance().getCurrentSession();
       	if(cur != null && !cur.IsIdle())
       	{
          	myApp.ForwardCall(cur);
          	onHook();

       	}

	}
	public void refer_all()
	{
        if(myApp.isTransfer)
        {
            myApp.isTransfer = false;
			Session currentLine;

			currentLine = CallManager.Instance().getCurrentSession();
            ReferOnHook(currentLine);
          	
        }
	}

	private void onHook()
	{
		
		Session currentLine = CallManager.Instance().getCurrentSession();
		onHook(currentLine);
	}
	private void do_hook()
	{
		Session currentLine = CallManager.Instance().getCurrentSession();
		if(currentLine == null)
				return;

		if(currentLine.state == Session.CALL_STATE_FLAG.INCOMING)		
			offhook(true);
		else 
			onHook(currentLine);	
			
	}
	private void show_door_action_call(int state)
	{
		//showTips("show_door_action_call "+String.valueOf(state));
  		m_door_info.setVisibility(View.VISIBLE);
  		switch(state)
  		{
    	case WaService.CLOSE_STATE:
      		m_door_info.setImageResource(R.drawable.close);
      
      	break;
    	case WaService.OPEN_STATE:
      		m_door_info.setImageResource(R.drawable.dooropen);
     
      	break;
    	default:
      
      		m_door_info.setVisibility(View.GONE);
     
      		break;
  		}

	}

  public final StaticHandler mHandler = new StaticHandler(this);
  private static class StaticHandler extends Handler{
    private final WeakReference<VideoFragment> mActivity;
    public StaticHandler(VideoFragment activity)
    {
      mActivity = new WeakReference<VideoFragment>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      VideoFragment myactivity = mActivity.get();
      if(myactivity == null) return;

      
	  switch(msg.what)
	  {
	case DOOR_ACTION_CALL:{
		int key = msg.getData().getInt("key");
        myactivity.show_door_action_call(key);
		}
        break;

	case VideoFragment.FORWARD_CALL:
		
		Log.i(TAG,"handle");
		myactivity.forward_all();
		break;
	case VideoFragment.REFER_CALL:
		myactivity.refer_all();

		   break;
		case VideoFragment.ONHOOK_CALL:
		
		  myactivity.onHook();
		   
		break;   
	   case VideoFragment.LOADCONSOLEACTIVITY:
		if(myactivity.m_is_active)
		  myactivity.activity.loadConsoleActivity();
		   
		break;   

 
	case VideoFragment.ANSWER_ACTION:
	{
	  Session currentLine = CallManager.Instance().getCurrentSession();
		if(currentLine != null)
			myactivity.offhook(currentLine.HasVideo);
	}
	  break;  
   case VideoFragment.AUTO_ONHOOK_ACTION:
	  
		myactivity.onHook();
		 
	  break;  
	case VideoFragment.CALL_CHANGE_ACTION_INCOMING:
	  
	if(myactivity.myApp.is2Line())
		myactivity.updateVideo1();
	else
		myactivity.updateVideo();
	   
	break;  
	case VideoFragment.CALL_CHANGE_ACTION_CONNECTED:
	  {
		  /*
		Session currentLine = CallManager.Instance().getCurrentSession();
		if(currentLine != null)
		{
			myactivity.myApp.adjust_mic(currentLine);
		}
		*/
		myactivity.updateVideo();
		myactivity.updateVideo1();
	  }
	break;  
	case VideoFragment.CALL_CHANGE_ACTION_CLOSED:
	  	{
		Session currentLine = CallManager.Instance().getCurrentSession();
		if(currentLine!=null)
			myactivity.SessionClose(currentLine);
		}
	break;  		
	case VideoFragment.CALL_CHANGE_ACTION_FAILED:
	  	{
		Session currentLine = CallManager.Instance().getCurrentSession();
		if(currentLine!=null)
			myactivity.SessionFailed(currentLine);
		}
	break;  		
	case VideoFragment.PRESSKEY_ACTION:
	  	
			int key = msg.getData().getInt("key");
			if(key == MyApplication.KEY_SPK)
			{
				myactivity.do_hook();

			}			

		break;  								
	  }

    }
  }

	private void SetCamera(PortSipSdk portSipLib,boolean userFront)
	{
		if (userFront)
		{
			portSipLib.setVideoDeviceId(1);
		}
		else
		{
			portSipLib.setVideoDeviceId(0);
		}
	}

	private void stopVideo(PortSipSdk portSipLib)
	{
		Session cur = CallManager.Instance().getCurrentSession();
		myApp.setLocalVideoWindow(portSipLib,null,false,false);

		//portSipLib.displayLocalVideo(false,false);
		//portSipLib.setLocalVideoWindow(null);

		if (cur != null
				&& !cur.IsIdle()
				&& cur.SessionID != PortSipErrorcode.INVALID_SESSION_ID
				&& cur.HasVideo)
		{
			portSipLib.setRemoteVideoWindow(cur.SessionID, null);
		}
	}

	private void WaitingCallAnswer()
	{
    //showTips("WaitingCallAnswer");
		int my_ids1[] = {

						 R.id.bt_audio,R.id.bt_audio,R.id.bt_video,//R.id.bt_offoo,
						 R.id.bt_hold,R.id.bt_tran,R.id.bt_conf,
						 R.id.bt_open,R.id.bt_dial,R.id.bt_door_talk,
		 };

     ImageButton b = null;

     String name = PortSipService.getSipName(getActivity());

     if(!TextUtils.isEmpty(name))
     {
         int number = getNumber(name);
         if(number >= 120 && number <= 200)
         //if(true)
         {
             if( ( b = (ImageButton)rootView.findViewById( R.id.bt_answer )) != null )
                 b.setVisibility(View.VISIBLE);
         }
         else
         {
             my_ids1[0]=R.id.bt_answer;
         }
     }
     else
     {
         my_ids1[0]=R.id.bt_answer;
     }


		 for( int i=0 ; i< my_ids1.length ; ++i )
				 if( ( b = (ImageButton)rootView.findViewById( my_ids1[i]) ) != null )
						 b.setVisibility(View.GONE);


     m_onhook_timer = MAX_ONHOOK_TIME;
	}
	private int getNumber(String str)
	{
		int number = 1000;

		try{
			number = Integer.parseInt(str);
		}catch(NumberFormatException ex)
		{

		}
		return number;
	}
	private void CallConnected(Session cur,boolean isVideo)
	{
		 ImageButton b = null;

		 int my_ids2[] = {

						R.id.bt_video,R.id.bt_audio,R.id.bt_offoo,
						R.id.bt_hold,R.id.bt_tran,R.id.bt_conf,
						R.id.bt_open,R.id.bt_dial,
			 };

			 for( int i=0 ; i< my_ids2.length ; ++i )
						if( ( b = (ImageButton)rootView.findViewById( my_ids2[i]) ) != null )
								b.setVisibility(View.VISIBLE);

			int my_ids3[] = {

					R.id.bt_audio,R.id.bt_answer,
			};

			if(isVideo)
			{
					my_ids3[0] = R.id.bt_video;
			}

			for( int i=0 ; i< my_ids3.length ; ++i )
					if( ( b = (ImageButton)rootView.findViewById( my_ids3[i]) ) != null )
						 b.setVisibility(View.GONE);

      if(myApp.m_is_monitor_call)
      {
          Log.i(TAG,"m_is_monitor_call");
          if( ( b = (ImageButton)rootView.findViewById( R.id.bt_dial) ) != null )
              b.setVisibility(View.GONE);
          if( ( b = (ImageButton)rootView.findViewById( R.id.bt_door_talk) ) != null )
          {
              b.setOnClickListener(btnDoListener);
              b.setVisibility(View.VISIBLE);
          }
      }

       int number;

       if(cur.voip_line == 0)
           number = 1000;
       else
           number = getNumber(PortSipService.getSipName(getActivity()));
		   //Integer.parseInt(PortSipService.getSipName(getActivity()));

      if(number <= 200 || number >= 1000)
			    return ;

      //showTips("CallConnected "+String.valueOf(number));
			int my_ids4[] = {
						R.id.bt_hold,R.id.bt_tran,R.id.bt_conf,
						R.id.bt_open,R.id.bt_dial,
			};


			for( int i=0 ; i< my_ids4.length ; ++i )
					if( ( b = (ImageButton)rootView.findViewById( my_ids4[i]) ) != null )
							b.setVisibility(View.GONE);

	}

	private void IncomingCall(boolean isVideo)
	{
		 ImageButton b = null;

		 int my_ids1[] = {

						R.id.bt_audio,R.id.bt_offoo,
						//R.id.bt_hold,R.id.bt_tran,R.id.bt_conf,
						//R.id.bt_open,R.id.bt_dial,
			 };

			 for( int i=0 ; i< my_ids1.length ; ++i )
						if( ( b = (ImageButton)rootView.findViewById( my_ids1[i]) ) != null )
								b.setVisibility(View.VISIBLE);

		 int my_ids2[] = {

						//R.id.bt_video,R.id.bt_offoo,
						R.id.bt_video,R.id.bt_hold,R.id.bt_tran,R.id.bt_conf,
						R.id.bt_open,R.id.bt_dial,R.id.bt_answer,R.id.bt_door_talk,

			 };

			 for( int i=0 ; i< my_ids2.length ; ++i )
						if( ( b = (ImageButton)rootView.findViewById( my_ids2[i]) ) != null )
								b.setVisibility(View.GONE);

			m_start_fwd = myApp.getTimeoutFWD();


	}

	public void updateVideo1() {

			Session cur = CallManager.Instance().getCurrentSession();
			if (cur != null
							&& cur.getSessionId() != PortSipErrorcode.INVALID_SESSION_ID)
			{

				  tv_call_number.setText(cur.showRemote);
					if(cur.getRecvCallState())
					{
						IncomingCall(cur.HasVideo);
            if(myApp.m_ring_enable)
            {
              Log.i(TAG,"offhook");
              offhook(true);

            }
					}
					else if(cur.getSessionConnected())
					{
						CallConnected(cur,cur.getIsSendVideo());

            myApp.muteSession(cur,true);
			if(myApp.checkAndStartRecord(cur))
			{
				m_rec_timer = MAX_REC_TIME;
			}
			

					}
					else
					{

						WaitingCallAnswer();
					}

			}

	}

	private void updateVideo()
	{
		Session cur = CallManager.Instance().getCurrentSession();

    	if(cur!=null && cur.IsIdle())
        	return;

		
		if(myApp.isMyConference())    return;

		PortSipSdk portSipLib = myApp.getVoipLine(cur.voip_line);

		//Session cur = CallManager.Instance().getCurrentSession();
		if (cur.SessionID != PortSipErrorcode.INVALID_SESSION_ID)
		{

      		int number;

      		if(cur.voip_line == 0)
          		number = 1000;
      		else
          		//number = Integer.parseInt(PortSipService.getSipName(getActivity()));
				number = getNumber(PortSipService.getSipName(getActivity()));

			if(cur.HasVideo)
			{	
				if((number > 200 && number < 1000) && (cur.intRemote > 5000))
      			{
          			//Toast.makeText(application.getApplicationContext(), "null", Toast.LENGTH_LONG).show();

					portSipLib.setRemoteVideoWindow(cur.SessionID, null);
      			}
      			else
      			{
        			//Toast.makeText(application.getApplicationContext(), "remoteRenderScreen", Toast.LENGTH_LONG).show();

					portSipLib.setRemoteVideoWindow(cur.SessionID, remoteRenderScreen);
      			}
			}

			if(cur.isSendVideo)
			{
				myApp.setLocalVideoWindow(portSipLib,localRenderScreen,cur.getIsSendVideo(),false);
		  
			}
			else 
			{
				myApp.setLocalVideoWindow(portSipLib,null,false,false);
		 
			}
	  		
			//portSipLib.setLocalVideoWindow(localRenderScreen);
			//portSipLib.displayLocalVideo(cur.getIsSendVideo(),false); // display Local video
			portSipLib.sendVideo(cur.SessionID, cur.getIsSendVideo());

    		//Toast.makeText(application.getApplicationContext(), String.valueOf(cur.getIsSendVideo()), Toast.LENGTH_LONG).show();


		}
		else
		{
			myApp.setLocalVideoWindow(portSipLib,null,false,false);
			
			//portSipLib.displayLocalVideo(false,false);
			//portSipLib.setLocalVideoWindow(null);
		}
	}
	private void initOnClickListener1() {

			int my_ids[] = {
					R.id.dialButton_audio,R.id.dialButton_video,R.id.dialButton_back,
					R.id.dialButton_0,R.id.dialButton_1,R.id.dialButton_2,
					R.id.dialButton_3,R.id.dialButton_4,R.id.dialButton_5,
					R.id.dialButton_6,R.id.dialButton_7,R.id.dialButton_8,
					R.id.dialButton_9,R.id.dialButton_xing,R.id.dialButton_jing,

			};


			View b = null;
			for( int i=0 ; i< my_ids.length ; ++i )
							if( ( b = (View)dialog.findViewById( my_ids[i]) ) != null )
											b.setOnClickListener(btnDoListener1);
	}
	private View.OnClickListener btnDoListener1=new View.OnClickListener(){
			@Override
public void onClick(View v)
			{
				boolean isdial = false;
				Session currentLine = CallManager.Instance().getCurrentSession();

				if ((myApp.is2Line() == false) && (currentLine != null) && currentLine.getHoldState())
				{
						isdial = true;
				}

    PortSipSdk myEngine = myApp.getVoipLine(currentLine.voip_line);
		int ch=-1;
		switch(v.getId())
		{
			case R.id.dialButton_audio:
			AnotherCall(m_str,false);
			break;
			case R.id.dialButton_video:
			AnotherCall(m_str,true);
			break;
			case R.id.dialButton_back:
			if (m_str != null && m_str.length() > 0) {
					ch ='b';
					m_str = m_str.substring(0, m_str.length() - 1);
			}
			break;
			case R.id.dialButton_0:
			ch = 0;
			m_str += "0";
			break;

			case R.id.dialButton_1:
			ch = 1;
			m_str += "1";
			break;
			case R.id.dialButton_2:
			ch = 2;
			m_str += "2";
			break;
			case R.id.dialButton_3:
			ch = 3;
			m_str += "3";
			break;
			case R.id.dialButton_4:
			ch = 4;
			m_str += "4";
			break;
			case R.id.dialButton_5:
			ch = 5;
			m_str += "5";
			break;
			case R.id.dialButton_6:
			ch = 6;
			m_str += "6";
			break;
			case R.id.dialButton_7:
			ch = 7;
			m_str += "7";
			break;
			case R.id.dialButton_8:
			ch = 8;
			m_str += "8";
			break;
			case R.id.dialButton_9:
			ch = 9;
			m_str += "9";
			break;
			case R.id.dialButton_xing:
			ch = 10;
			m_str += ".";
			break;
			case R.id.dialButton_jing:
			if((isdial == true) && m_str.length() > 0)
			{
					AnotherCall(m_str,false);
			}
			else
			{
				ch = 11;
			}
			break;

		}

		if(ch != -1)
		{
				new_editText.setText(m_str);
        if(activity.is_alarm_state())
        {
          boolean ret;
          ret = myApp.onRecvDtmfTone(currentLine.voip_line,currentLine.getSessionId(),ch);
          if(ret)
          {
            dialog.dismiss();
            dialog = null;
          }
        }
				if((isdial == false) && ch != 'b')
				{
						myEngine.sendDtmf(currentLine.getSessionId(),PortSipEnumDefine.ENUM_DTMF_MOTHOD_RFC2833,ch,160,true);

				}
		}

	 }
	};

	private void initOnClickListener(View view) {

        int my_ids[] = {
            R.id.bt_mic_jia,R.id.bt_vol_jia,R.id.bt_mic_jian,
            R.id.bt_vol_jian,R.id.bt_video,R.id.bt_audio,R.id.bt_offoo,
            R.id.bt_hold,R.id.bt_tran,R.id.bt_conf,
            R.id.bt_open,R.id.bt_dial,R.id.top_view,R.id.bt_answer
        };


        View b = null;

        for( int i=0 ; i< my_ids.length ; ++i )
              if( ( b = (View)view.findViewById( my_ids[i]) ) != null )
                b.setOnClickListener(btnDoListener);

    }
	
    private View.OnClickListener btnDoListener=new View.OnClickListener(){
      @Override
	    public void onClick(View v)
      {
          Session currentLine;

					currentLine = CallManager.Instance().getCurrentSession();
          if(currentLine == null)
					    return;

		  PortSipSdk myEngine = myApp.getVoipLine(currentLine.voip_line);

		  switch(v.getId())
	        {
	        case R.id.bt_mic_jia:
          myApp.adjust_mic(currentLine,1);

		      break;
	        case R.id.bt_vol_jia:
				myApp.adjust_spk(currentLine,1);
					//Ring.getInstance(getActivity()).adjust_call_volume(AudioManager.ADJUST_RAISE);

		      break;
	        case R.id.bt_mic_jian:
          myApp.adjust_mic(currentLine,0);
		      break;
	        case R.id.bt_vol_jian:
				myApp.adjust_spk(currentLine,0);
					//Ring.getInstance(getActivity()).adjust_call_volume(AudioManager.ADJUST_LOWER);

		      break;
					case R.id.bt_audio:
           offhook(false);/*
           int line1 = CallManager.Instance().useLineCount();
           showTips("line"+String.valueOf(line1));*/
					break;
	        case R.id.bt_video:
           offhook(true);

		      break;
	        case R.id.bt_offoo:

					if(myApp.isRefer)
					    ReferOnHook(currentLine);
					else
					    onHook(currentLine);

		      break;
	        case R.id.bt_hold:

					if (myApp.isMoreLine())
					{
						showTips("more line");
						break;
					}
					if (!currentLine.getSessionConnected())
					{
						  showTips("getSessionConnected failed.");
					    break;

					}


          if(myApp.is2Line())
					{
						if(myApp.isMyConference() == false)
						{
							int rt = myEngine.hold(currentLine.getSessionId());
							currentLine.setHoldState(true);

              Session waitLine = myApp.getWaitSession();

							rt = myEngine.unHold(waitLine.getSessionId());
							waitLine.setHoldState(false);

              tv_call_number.setText(waitLine.showRemote);
              myApp.swapLine();
						}

						break;
					}

          if(currentLine.getHoldState())
					{
						int rt = myEngine.unHold(currentLine.getSessionId());
						if (rt != 0) {
								showTips("unhold operation failed.");
								return;
						}
            showTips("unhold");
						currentLine.setHoldState(false);

					}
					else
					 {

					    int rt = myEngine.hold(currentLine.getSessionId());
					    if (rt != 0) {
						      showTips("hold operation failed.");
						      return;
					    }
              showTips("hold");
					    currentLine.setHoldState(true);
				  }

		      break;
	        case R.id.bt_tran:

					if (myApp.isMoreLine())
					{
						showTips("more line");
						break;
					}

					if(myApp.is2Line() && (myApp.isMyConference() == false))
          {
						if (currentLine.getRecvCallState()) {
			          showTips("trans fail.");
			          break;
		        }

						Session waitLine = myApp.getWaitSession();

            if(waitLine.getHoldState())
						{
							myEngine.unHold(waitLine.getSessionId());
							waitLine.setHoldState(false);
						}
            Log.i("bt_tran",currentLine.callNumber);
						int rt = myEngine.attendedRefer(
								waitLine.getSessionId(),
								currentLine.getSessionId(), currentLine.callNumber);

						if (rt != 0) {
							showTips(currentLine.getLineName()
									+ ": failed to Attend transfer");
						} else {
							showTips(currentLine.getLineName()
									+ ": Transferring");

						   //ReferOnHook(currentLine);

              myApp.isRefer = true;
						}

          }

		      break;
	        case R.id.bt_conf:

          if(myApp.is2Line() == false || myApp.isMyConference())
					{
						if(myApp.is2Line() == false)
						    showTips("only one line.");
						else
								showTips("conf line.");

						break;
					}

					show_dialog_conf();

          break;

	        case R.id.bt_open:
          myApp.open_door(currentLine);
		     break;
         case R.id.bt_door_talk:
         if (!currentLine.getSessionConnected())
         {
             showTips("getSessionConnected failed.");
             break;

         }

        myApp.m_is_monitor_call = false;
        myApp.muteSession(currentLine,false);

         break;
	       case R.id.bt_dial:

				 if (!currentLine.getSessionConnected())
				 {
						 showTips("getSessionConnected failed.");
						 break;

				 }

         if(myApp.is2Line())
		     {
					  if(myApp.upperLineCount() || myApp.isMyConference())
						{
					      showTips("more line.");
					      break;
					  }
				 }

         myEngine.hold(currentLine.getSessionId());
         currentLine.setHoldState(true);
         show_dial();

		     break;
         case R.id.top_view:
         if(currentLine.getHoldState() == true)
         {
             myEngine.unHold(currentLine.getSessionId());
             currentLine.setHoldState(false);
         }

         break;
         case R.id.bt_answer:
        {
          Session cur = CallManager.Instance().getCurrentSession();
          if(cur!= null && cur.voip_line == 1)
          {
              SharedPreferences prefences = PreferenceManager.getDefaultSharedPreferences(activity);

              String domain = prefences.getString(PortSipService.USER_DOMAIN, "");
              //String port = prefences.getString(SVR_PORT, "5060");

              String str = "sip:"+cur.showRemote+"@"+domain;

              //showTips(str);
              myApp.sendSIPMessage(cur.voip_line,str,"CALL_ANSWER");
          }
        }

         break;
      }

      show_volume();
	}
	};

	private void startConf(boolean isVideo)
	{
		Session currentLine;

		currentLine = CallManager.Instance().getCurrentSession();
		if(currentLine == null)
				return;

		if(myApp.isMyConference())
		{
			showTips("conf line.");

			return;
		}

    myApp.setConf(currentLine.voip_line,isVideo,remoteRenderScreen);

	}

	private void show_dial()
	{
	    if(dialog == null)
	    {
          dialog = new Dialog(getActivity(),R.style.MyDialog);//指定自定義樣式

          dialog.setContentView(R.layout.dial);//���芸�蝢始ayout

          new_editText = (EditText)dialog.findViewById(R.id.new_editText);

          initOnClickListener1();
	    }


      Window dialogWindow = dialog.getWindow();

      WindowManager.LayoutParams lp = dialogWindow.getAttributes();

      lp.alpha = 0.7f;// 透明度

      m_str = "";

      new_editText.setText(m_str);

      dialog.setOnDismissListener(new OnDismissListener() {

                  @Override
                  public void onDismiss(DialogInterface dialog) {
                       //处理监听事件

                       if(myApp.is2Line() != false) return;

                       Session currentLine = CallManager.Instance().getCurrentSession();
						if(currentLine == null) return;
                    PortSipSdk myEngine = myApp.getVoipLine(currentLine.voip_line);
                    if(currentLine.getHoldState() == true)
                    {
                        myEngine.unHold(currentLine.getSessionId());
                        currentLine.setHoldState(false);
                    }
                  }
              });
      dialog.show();

	}

	private void show_dialog_conf()
	{

    if(alertDialog == null)
		    alertDialog = new AlertDialog.Builder(getActivity()).create();

		alertDialog.setTitle(getResources().getString(R.string.str_confrence));
		alertDialog.setMessage(getResources().getString(R.string.str_confrence));

		alertDialog.setButton(DialogInterface.BUTTON_POSITIVE,"    ",
						new DialogInterface.OnClickListener() {
								@Override
								public void onClick(DialogInterface arg0, int arg1) {
                     startConf(false);

								}
						});
		alertDialog.setButton(DialogInterface.BUTTON_NEUTRAL,"    ",
						new DialogInterface.OnClickListener() {
								@Override
								public void onClick(DialogInterface arg0, int arg1) {
										//Answer Video call
										startConf(true);

								}
						});
		alertDialog.setButton(DialogInterface.BUTTON_NEGATIVE,"    ",
						new DialogInterface.OnClickListener() {
								@Override
								public void onClick(DialogInterface arg0, int arg1) {
										//Reject call

								}
						});

		// Showing Alert Message
		alertDialog.show();

		Button email = alertDialog.getButton(DialogInterface.BUTTON_POSITIVE);
    email.setBackgroundResource(R.drawable.audio);
		email = alertDialog.getButton(DialogInterface.BUTTON_NEUTRAL);
    email.setBackgroundResource(R.drawable.video);
		email = alertDialog.getButton(DialogInterface.BUTTON_NEGATIVE);
    email.setBackgroundResource(R.drawable.off);


	}

	private void ReferOnHook(Session currentLine)
	{
		Log.i(TAG,"ReferOnHook");
		Ring.getInstance(activity).stopRingTone();
		Ring.getInstance(activity).stopRingBackTone();

		if(currentLine == null)    return;
    PortSipSdk myEngine = myApp.getVoipLine(currentLine.voip_line);

		if(myApp.is2Line())
		{
				myApp.set2Line(false);
				Session waitLine = myApp.getWaitSession();

				myEngine.hangUp(waitLine.getSessionId());

				waitLine.Reset();

     }

		 myEngine.hangUp(currentLine.getSessionId());
		 currentLine.Reset();

    myApp.isRefer = false;


		activity.loadConsoleActivity();


  }

	private void offhook(boolean isVideo)
	{
		Session currentLine;
    m_start_fwd = 0;

    m_onhook_timer = 0;
		currentLine = CallManager.Instance().getCurrentSession();
		if(currentLine == null)
				return;

     if(myApp.isMyConference())
		 {
			 showTips("in conference .");

			 return;
		 }

			else if(currentLine.getSessionConnected())
				{
					if(isVideo == true && currentLine.getIsSendVideo() == false)
					{
						  PortSipSdk myEngine = myApp.getVoipLine(currentLine.voip_line);
							int ret = myEngine.updateCall(currentLine.getSessionId(),true,true);
							Log.i(TAG,"updatecall "+Integer.toString(ret));

							currentLine.setVideoState(true);

					}

          currentLine.setIsSendVideo(isVideo);

					updateVideo();
					updateVideo1();
					return;
				}

			 if (!currentLine.getRecvCallState()) {
					 //showTips("No incoming call on current line, please switch a line.");
					 return;
			 }

			 //currentLine.setRecvCallState(false);

			 currentLine.setVideoState(currentLine.HasVideo);
			 currentLine.setIsSendVideo(isVideo);

			 myApp.answerSessionCall(currentLine,currentLine.HasVideo);

       if(myApp.is2Line())
       {
         Ring.getInstance(activity).stopCallWaitingTone();

         Session waitLine = myApp.getWaitSession();
         PortSipSdk myEngine = myApp.getVoipLine(waitLine.voip_line);
         myEngine.hold(waitLine.getSessionId());
         waitLine.setHoldState(true);

       }

	}
  private void onHook(Session currentLine)
	{
		myApp.m_is_monitor_call = false;
		Ring.getInstance(activity).stopRingTone();

		Ring.getInstance(activity).stopRingBackTone();

    Ring.getInstance(activity).stopCallWaitingTone();
/*
		int ret = CallManager.Instance().onHook(currentLine,mEngine);
		if(ret == 0)
		{
			((MainActivity)getActivity()).loadConsoleActivity();

		}
*/
	if(currentLine == null)    
	{
		myApp.hangUpAll();
		postMessage(LOADCONSOLEACTIVITY);
		return;
	}
    PortSipSdk myEngine = myApp.getVoipLine(currentLine.voip_line);

    if(currentLine.getTryingState())
		{
			if(myApp.isMoreLine())
			{
			    myApp.changeCurrentLine();

				  myEngine.unHold(CallManager.Instance().getCurrentSession().getSessionId());
				  
					myApp.hangUp(currentLine);
					currentLine.Reset();

          updateVideo1();
			}
			else

			if(myApp.is2Line() != false)
			{
				myEngine.unHold(myApp.getWaitSession().getSessionId());
				myApp.setCurrentLine(myApp.getWaitSession());
				myApp.set2Line(false);

				myApp.hangUp(currentLine);
				currentLine.Reset();

        updateVideo1();
			}
      else
			{
          myApp.hangUp(currentLine);
			    currentLine.Reset();

				postMessage(LOADCONSOLEACTIVITY);
					//((MainActivity)getActivity()).loadConsoleActivity();

		  }

			return;
		}
    else if(myApp.isMyConference() || myApp.isMoreLine())
		{
			  if(myApp.isMyConference())
            myApp.removeConf(myEngine);

		}

		else if(myApp.is2Line())
		{
				myApp.set2Line(false);
				Session waitLine = myApp.getWaitSession();

				if (currentLine.getRecvCallState()) {
					myEngine.rejectCall(currentLine.getSessionId(), 486);

					showTips(currentLine.getLineName()
								+ ": Rejected call");

				}
				else
				{

					 myEngine.hangUp(currentLine.getSessionId());
				}

				currentLine.Reset();

				myEngine.unHold(waitLine.getSessionId());

				myApp.setCurrentLine(waitLine);

        updateVideo1();
				return;

		}

		myApp.hangUpAll();
		postMessage(LOADCONSOLEACTIVITY);
		//((MainActivity)getActivity()).loadConsoleActivity();

	}

 

	void showTips(String text) {
		//mtips.setText(text);
		//spinnerAdapter.notifyDataSetChanged();
		Log.i(TAG,text);
		Toast.makeText(myApp.getApplicationContext(), text, Toast.LENGTH_SHORT).show();
	}

	private void AnotherCall(String callTo,boolean isVideo)
 {
			if (callTo == null || callTo.length() <= 0) {
					showTips("The phone number is empty.");
					return;
			}

			Session cur = CallManager.Instance().getCurrentSession();

			if(cur != null && !cur.IsIdle())
			{
					if(cur.voip_line == 0)
					{
						callTo = callTo.replace("*",".");
						callTo = "sip:"+callTo;
					}
			}
      else
      {
        showTips("current line empty");
        return;
      }

			Log.i(TAG,"anotherCall " + callTo);

			Session currentLine = myApp.findIdleLine();

      PortSipSdk myEngine = myApp.getVoipLine(cur.voip_line);
						// Ensure that we have been added one audio codec at least
			if (myEngine.isAudioCodecEmpty()) {
					showTips("Audio Codec Empty,add audio codec at first");
					return;
			}

			//SaveCallLog(callTo);
			// Usually for 3PCC need to make call without SDP
			long sessionId = myEngine.call(callTo,true, isVideo);
			if (sessionId <= 0) {
						showTips("Call failure");
						return;
			}

			if (!cur.getHoldState())
			{
				  myEngine.hold(cur.getSessionId());
					cur.setHoldState(true);
			}

			currentLine.setSessionId(sessionId);
			currentLine.setVideoState(isVideo);
      currentLine.setIsSendVideo(isVideo);
			currentLine.callNumber = callTo;
			currentLine.Remote = myApp.getCallNumber(callTo);
      if(currentLine.voip_line == 0)
          currentLine.intRemote = 1000;
      else
          currentLine.intRemote = getNumber(currentLine.showRemote);
      currentLine.setTryingState();
			currentLine.voip_line = cur.voip_line;

			myApp.addCurrentLine(currentLine);
			showTips(currentLine.getLineName() + ": Calling... "+String.valueOf(sessionId));

			Ring.getInstance(activity).startRingBackTone();

			updateVideo1();


			tv_call_number.setText(callTo);

			dialog.dismiss();
      dialog = null;



	}

private void SessionFailed(Session tempSession)
{
	SessionClose(tempSession);
}
private void SessionClose(Session tempSession)
{
	Log.i(TAG,"SessionClose");

	if(tempSession == null)    return;

    PortSipSdk myEngine = myApp.getVoipLine(tempSession.voip_line);
	if(myApp.isMyConference())
	{
		if (tempSession.getSessionConnected()) {
			myEngine.removeFromConference(tempSession.getSessionId());
      	}
	}

	if(myApp.isMoreLine())
	{
		if(myApp.isCurrentLine(tempSession))
		{
        	myApp.changeCurrentLine();
			Session cur = CallManager.Instance().getCurrentSession();
          	if(cur!=null && cur.getHoldState())
          	{
				myEngine.unHold(cur.getSessionId());
              	cur.setHoldState(false);
          	}
		}
		else if(myApp.isWaitLine(tempSession))
		{
        	myApp.changeWaitLine();

		}
      	updateVideo1();

      	tempSession.Reset();
		return;
	}
	if(myApp.is2Line() != false)
	{
		if(myApp.isCurrentLine(tempSession))
		{
			Session waitLine = myApp.getWaitSession();

			//myEngine.unHold(waitLine.getSessionId());
			myApp.setCurrentLine(waitLine);
        	if(waitLine.getHoldState())
        	{
            	myEngine.unHold(waitLine.getSessionId());
            	waitLine.setHoldState(false);
        	}

		}

      	tempSession.Reset();

		myApp.set2Line(false);
      	updateVideo1();
		return;

	}

	myEngine.hangUp(tempSession.getSessionId());
	tempSession.Reset();
	Ring.getInstance(activity).stopRingBackTone();
		
	postMessage(LOADCONSOLEACTIVITY);
    //((MainActivity)getActivity()).loadConsoleActivity();
}
  private void HandleotherCallEvent(Session session)
  {
      if(session.IsIdle())
      {
          if(myApp.isMyConference())
          {
             PortSipSdk myEngine = myApp.getVoipLine(session.voip_line);

              myEngine.removeFromConference(session.getSessionId());

          }

          if(myApp.isMoreLine())
          {
            if(myApp.isWaitLine(session))
            {
                myApp.changeWaitLine();

            }
          }
          else if(myApp.is2Line() != false)
      		{

              myApp.set2Line(false);

      		}

          session.Reset();

      }

  }
  public void timeout()
  {
	if(m_rec_timer > 0)
	{
		m_rec_timer--;
		if(m_rec_timer == 0 && myApp.m_video_record_start)
		{
			Session currentLine = CallManager.Instance().getCurrentSession();
  		  	if(currentLine != null)
  				myApp.stopRecord(currentLine);
		}
	}

	if(!m_is_active)    return;

	if(m_sos_onhook_timer > 0)
	{
  		m_sos_onhook_timer--;
  		if(m_sos_onhook_timer == 0)
  		{
			postMessage(VideoFragment.ONHOOK_CALL);
		

  		}
	}
	else
	{
  		m_sos_onhook_timer = 0;
	}

	if(m_start_fwd > 0)
	{
		m_start_fwd--;

		if(m_start_fwd == 0){
			postMessage(VideoFragment.FORWARD_CALL);
		
		}

	}
	else
	{
 		//m_start_fwd = 0;
		if(m_onhook_timer > 0)
		{
			m_onhook_timer--;
			if(m_onhook_timer == 0)
			{
				postMessage(VideoFragment.ONHOOK_CALL);
				
			}
		}
		else
		{
			m_onhook_timer = 0;
		}
	}

	if(myApp.isRefer)
	{
		postMessage(VideoFragment.REFER_CALL);
	

	}
	 
  }
  private void postMessage(int id,int key)
  {
	Message message=mHandler.obtainMessage();
	
	Bundle data = new Bundle();
	
	data.putInt("key", key);
	message.setData(data);
  
	message.what = id;
	mHandler.sendMessage(message);
  }  
  	private void postMessage(int id)
	{
  		Message message=new Message();
  		message.what = id;
  		mHandler.sendMessage(message);
	}	 
    public int OnBroadcastReceiver(Intent intent)
	{
		String action = intent == null ? "" : intent.getAction();

		int ret;

		ret = -1;
		if (MyApplication.DOOR_ACTION_CALL.equals(action)) 
		{
		  int key = intent.getIntExtra(MyApplication.EXTRA_KEY,0);
		  
		  postMessage(DOOR_ACTION_CALL,key);
		  ret = 0;
		}     

		else if (MyApplication.PRESSKEY_ACTION.equals(action)) 
		{
		  int key = intent.getIntExtra(MyApplication.EXTRA_KEY,-1);
		  if(key >= 0)
		  	postMessage(PRESSKEY_ACTION,key);
		  ret = 0;
		}     
		else if (PortSipService.CALL_CHANGE_ACTION.equals(action))
		{
			long sessionId = intent.getLongExtra(PortSipService.EXTRA_CALL_SEESIONID, Session.INVALID_SESSION_ID);
			String status = intent.getStringExtra(PortSipService.EXTRA_CALL_DESCRIPTION);
			Session session = CallManager.Instance().findSessionBySessionID(sessionId);

      //showTips(status);

			if (session != null)
			{
				if(CallManager.Instance().isCurrentLine(session) == false)
				{
			  		HandleotherCallEvent(session);
			  		return 0;
				}
	
				ret = 0;
				
				switch (session.state)
				{
					case INCOMING:
						postMessage(CALL_CHANGE_ACTION_INCOMING);
						break;
					case TRYING:
						break;
					case CONNECTED:
					myApp.previous_call_status = 1;
					m_onhook_timer = 0;
					myApp.adjust_mic_vol(session);
					// myApp.adjust_spk(session);
          			m_sos_onhook_timer = 0;
          			((MainActivity)getActivity()).playSOSAlarmWav(session.voip_line,session.getSessionId());
					  postMessage(CALL_CHANGE_ACTION_CONNECTED);
					break;
					case CLOSED:
						//SessionClose(session);
						m_onhook_timer = 0;
						postMessage(CALL_CHANGE_ACTION_CLOSED);
						break;
          			case FAILED:
					  //SessionFailed(session);
					  m_onhook_timer = 0;
					  postMessage(CALL_CHANGE_ACTION_FAILED);

            		break;
				}
			}
		}
		else if (PortSipService.ONHOOK_ACTION.equals(action))
		{
      m_onhook_timer = 0;
			ret = intent.getIntExtra(PortSipService.EXTRA_ONHOOK, -1);
			if(ret == 0)
			{
				postMessage(LOADCONSOLEACTIVITY);
				//((MainActivity)getActivity()).loadConsoleActivity();

			}
			ret = 0;
		}
    else if (PortSipService.ANSWER_ACTION.equals(action))
    {
        //showTips("ANSWER_ACTION");
        //if(true) return 0;
        Session currentLine = CallManager.Instance().getCurrentSession();
  		  if(currentLine == null)
  				   return -1;

        ret = 0;
        switch(currentLine.state)
        {
          case INCOMING:
		  		postMessage(ANSWER_ACTION);
              //offhook(currentLine.HasVideo);
              break;
        }

    }
    else if (PortSipService.AUTO_ONHOOK_ACTION.equals(action))
    {
        //showTips("ANSWER_ACTION");
        //if(true) return 0;
        Session currentLine = CallManager.Instance().getCurrentSession();
  		  if(currentLine == null)
  				   return -1;

        ret = 0;
        switch(currentLine.state)
        {
          case TRYING:
          case CONNECTED:
		  postMessage(AUTO_ONHOOK_ACTION);
              //onHook(currentLine);
              break;
        }

    }
		return ret;
	}
}
