package com.weema.sipsample.ui;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.view.View;


import android.widget.ImageButton;
import android.widget.Button;

import android.widget.SeekBar;
import android.widget.SeekBar.OnSeekBarChangeListener;

import android.widget.Toast;
import android.view.LayoutInflater;

import android.view.View;
import android.view.View.OnLongClickListener;
import android.view.ViewGroup;


import android.app.Fragment;

import com.weema.R;

import com.weema.sipsample.util.Ring;

public class VolumeActivity extends Fragment {
	private MyApplication myApp;
	private SeekBar volumeControl = null;
	private SeekBar volumeControl_hw  = null;
	private SeekBar volumeControl_ring  = null;
	private SeekBar volumeControl_mic  = null;
	private ImageButton m_buttonPrev;
	private ImageButton m_buttonOk;

	MainActivity context;
	// 喇叭音量
	int m_progressChanged = 0;
	// 喇叭音量(硬體)
	int m_progressChanged_hw = 0;
	// 鈴聲音量
	int m_progressChanged_ring = 0;
	// 麥克風音量
	int m_progressChanged_mic = 0;
@Override
public View onCreateView(LayoutInflater inflater, ViewGroup container,
		  			Bundle savedInstanceState) {
		  		// TODO Auto-generated method stub

		  			super.onCreateView(inflater, container, savedInstanceState);
		  		  context = (MainActivity) getActivity();
				  myApp = (MyApplication) context.getApplication();

		  		  View rootView = inflater.inflate(R.layout.volume, null);
		  		  initView(rootView);

		  		  return rootView;
 }

 private void initView(View view) {

		 //m_is_first = true;

		m_buttonPrev = (ImageButton) view.findViewById(R.id.volume_button_prev);
		m_buttonPrev.setOnClickListener(btnDoListener);

		m_buttonOk = (ImageButton) view.findViewById(R.id.volume_button_ok);
		m_buttonOk.setOnClickListener(btnDoListener);

		volumeControl = (SeekBar) view.findViewById(R.id.volume_bar);

		// 喇叭音量
    	m_progressChanged = myApp.m_volume;

		volumeControl.setProgress(m_progressChanged);

		volumeControl.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {


			public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser){
				m_progressChanged = progress;
			}

			public void onStartTrackingTouch(SeekBar seekBar) {
				// TODO Auto-generated method stub
			}

			public void onStopTrackingTouch(SeekBar seekBar) {
				//Toast.makeText(VolumeActivity.this,"seek bar progress:"+m_progressChanged,
				//		Toast.LENGTH_SHORT).show();
			}
		});

		// 喇叭音量(硬體)
		volumeControl_hw = (SeekBar) view.findViewById(R.id.volume_hw_bar);

    	m_progressChanged_hw = Ring.getInstance(context).getVolume();
		volumeControl_hw.setProgress(m_progressChanged_hw);

		volumeControl_hw.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {


			public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser){
				m_progressChanged_hw = progress;
			}

			public void onStartTrackingTouch(SeekBar seekBar) {
				// TODO Auto-generated method stub
			}

			public void onStopTrackingTouch(SeekBar seekBar) {
				//Toast.makeText(VolumeActivity.this,"seek bar ring progress:"+m_progressChanged_ring,
				//		Toast.LENGTH_SHORT).show();
			}
		});

		// 麥克風音量
		volumeControl_mic = (SeekBar) view.findViewById(R.id.volume_mic_bar);

    	m_progressChanged_mic = myApp.mCurMicVol;
		volumeControl_mic.setProgress(m_progressChanged_mic);

		volumeControl_mic.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {

			public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser){
				m_progressChanged_mic = progress;
			}

			public void onStartTrackingTouch(SeekBar seekBar) {
				// TODO Auto-generated method stub
			}

			public void onStopTrackingTouch(SeekBar seekBar) {
				//Toast.makeText(VolumeActivity.this,"seek bar ring progress:"+m_progressChanged_ring,
				//		Toast.LENGTH_SHORT).show();
			}
		});

		// 鈴聲音量
		volumeControl_ring = (SeekBar) view.findViewById(R.id.volume_ring_bar);

    	m_progressChanged_ring = Ring.getInstance(context).getRingVolume();
		volumeControl_ring.setProgress(m_progressChanged_ring);

		volumeControl_ring.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {

			public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser){
				m_progressChanged_ring = progress;
			}

			public void onStartTrackingTouch(SeekBar seekBar) {
				// TODO Auto-generated method stub
			}

			public void onStopTrackingTouch(SeekBar seekBar) {
				//Toast.makeText(VolumeActivity.this,"seek bar ring progress:"+m_progressChanged_ring,
				//		Toast.LENGTH_SHORT).show();
			}
		});

		//volumeControl_mic = (SeekBar) view.findViewById(R.id.volume_bar2);

	}
	
    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
      
        if (!hidden){
			// 喇叭音量
			m_progressChanged = myApp.m_volume;
			volumeControl.setProgress(m_progressChanged);   
			
			// 喇叭音量(硬體)
			m_progressChanged_hw = Ring.getInstance(context).getVolume();
			volumeControl_hw.setProgress(m_progressChanged_hw);
			// 麥克風音量
    		m_progressChanged_mic = myApp.mCurMicVol;
			volumeControl_mic.setProgress(m_progressChanged_mic);
			// 鈴聲音量
			m_progressChanged_ring = Ring.getInstance(context).getRingVolume();
			volumeControl_ring.setProgress(m_progressChanged_ring);
				
        }
        else {
          
         
        }
    }
    private Button.OnClickListener btnDoListener=new Button.OnClickListener(){
        @Override
	public void onClick(View v)
        {
	    switch(v.getId())
	    {
	    case R.id.volume_button_prev:
			context.loadMySettingFragment();

		break;
	    case R.id.volume_button_ok:
			// 鈴聲音量
			Ring.getInstance(context).setRingVolume(m_progressChanged_ring);
			// 喇叭音量(硬體)
			Ring.getInstance(context).setVolume(m_progressChanged_hw);
			// 喇叭音量
      		myApp.setVolume(m_progressChanged);
			// 麥克風音量
			myApp.set_mic(m_progressChanged_mic);
			context.showTips_ex(
				"sp:" + Integer.toString(m_progressChanged) + ", " +
				"sp(hw):" + Integer.toString(m_progressChanged_hw) + ", " +
				"mic:" + Integer.toString(m_progressChanged_mic) + ", " +
				"ring:" + Integer.toString(m_progressChanged_ring)
			);
    		context.loadMySettingFragment();

		break;
	    }
        }
    };

}
