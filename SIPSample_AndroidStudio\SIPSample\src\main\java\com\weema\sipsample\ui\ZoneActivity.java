package com.weema.sipsample.ui;

import android.os.Handler;
import android.os.Message;
import java.lang.ref.WeakReference;

import android.os.Bundle;
import android.view.View;

import android.widget.CheckBox;

import android.widget.Button;

import com.weema.R;
import android.app.Fragment;
import android.view.LayoutInflater;
import android.view.ViewGroup;
//import android.support.annotation.Nullable;
import androidx.annotation.Nullable;
import android.util.Log;

public class ZoneActivity extends Fragment {

    private static final String TAG="ZoneActivity";
    MainActivity mainActivity;
    
    private int m_back_timer = 0;

    private MyApplication myApp;

    private static final int MAX_BACK_TIME = 20;
    private static final int MAX_FINISH_TIME = 1;

    private CheckBox[] m_checkB=new CheckBox[5];
    private Button btn_ok;
    private Button btn_esc;

    private boolean m_is_active;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
  			Bundle savedInstanceState) {
          // TODO Auto-generated method stub

            super.onCreateView(inflater, container, savedInstanceState);

            View rootView = inflater.inflate(R.layout.zone, null);
            //initView(rootView);
            mainActivity = (MainActivity) getActivity();
            myApp = (MyApplication) mainActivity.getApplicationContext();
            return rootView;
   }

   @Override
   public void onViewCreated(View view, Bundle savedInstanceState) {
       super.onViewCreated(view, savedInstanceState);

       initView(view);
       onHiddenChanged(false);
   }
   @Override
   public void onHiddenChanged(boolean hidden) {
       super.onHiddenChanged(hidden);

       m_is_active = !hidden;
       if (!hidden){
 
           mainActivity.receiver.broadcastReceiver = null;

           boolean[] zone = myApp.m_zone;

           if(zone.length >= 5)
           {
               for(int i=0;i<5;i++)
               {
                   m_checkB[i].setChecked(zone[i]);

               }
           }

           m_back_timer = MAX_BACK_TIME;
           //setOnlineStatus();
           Log.i(TAG,"onHiddenChanged");
          
       }
       else {
         
         m_back_timer = 0;

       }
   }


   private void initView(View view) {

        m_checkB[0] = (CheckBox)view.findViewById(R.id.zone_checkB_1);
        m_checkB[1] = (CheckBox)view.findViewById(R.id.zone_checkB_2);
        m_checkB[2] = (CheckBox)view.findViewById(R.id.zone_checkB_3);
        m_checkB[3] = (CheckBox)view.findViewById(R.id.zone_checkB_4);
        m_checkB[4] = (CheckBox)view.findViewById(R.id.zone_checkB_5);

        btn_ok= (Button)view.findViewById(R.id.zone_button_ok);
        btn_ok.setOnClickListener(btnDoListerner);

        btn_esc= (Button)view.findViewById(R.id.zone_button_esc);
        btn_esc.setOnClickListener(btnDoListerner);

   }



    private Button.OnClickListener btnDoListerner=new Button.OnClickListener(){
        @Override
	      public void onClick(View v)
        {
	          switch(v.getId())
	          {
	          case R.id.zone_button_ok:

            boolean[] zone = new boolean[5];
            for(int i=0;i<5;i++)
		        {
		            zone[i] = m_checkB[i].isChecked();
            }

           myApp.setZone(mainActivity,zone);
           mainActivity.showTips(R.string.str_setting_ok);

           m_back_timer = MAX_FINISH_TIME;
		
		      break;
	        case R.id.zone_button_esc:
		      m_back_timer = 1;
		      break;

	        }
        }
    };

    private void postMessage(int id)
    {
        Message message=new Message();
        message.what = id;
        mHandler.sendMessage(message);
    }
    public void timeout() {
        // TODO Auto-generated method stub
        if(m_is_active == false)    return;
        Log.i(TAG,"timertask");

         if(m_back_timer > 0)
         {
           m_back_timer--;
           if(m_back_timer == 0)
           {
            postMessage(MyApplication.GOTO_BACK);
           }
         }
    }

   public final StaticHandler mHandler = new StaticHandler(this);
  private  static class StaticHandler extends Handler{
    private final WeakReference<ZoneActivity> mActivity;
    public StaticHandler(ZoneActivity activity)
    {
      mActivity = new WeakReference<ZoneActivity>(activity);
    }
    @Override
    public void handleMessage(Message msg)
    {
      	ZoneActivity activity = mActivity.get();
      	if(activity == null) return;

        switch(msg.what)
        {
            case MyApplication.GOTO_BACK:
            activity.mainActivity.loadMySettingFragment();

             break;

        }
    }
  }
   
}
