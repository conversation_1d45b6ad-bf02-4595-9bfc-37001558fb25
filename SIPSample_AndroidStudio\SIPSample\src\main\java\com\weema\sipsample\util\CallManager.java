package com.weema.sipsample.util;

import com.portsip.PortSIPVideoRenderer;
import com.portsip.PortSipEnumDefine;
import com.portsip.PortSipSdk;

import android.util.Log;
public class CallManager
{
	public static final int MAX_LINES = 10;
	private static CallManager mInstance;
	private static Object locker = new Object();
	public Session[] sessions;
	public int CurrentLine;
	private boolean regist;
	private boolean regist2;
	private boolean regist3;
	private boolean regist4;
	public boolean speakerOn =false;

  private Session doSession;

  private int _waitLine;
	private boolean mis2Line;
	private boolean isMyConference;

	final private int MAX_CONF_LINES=4;
	final private int MIN_MORE_LINES=3;

	public boolean setSpeakerOn(PortSipSdk portSipSdk ,boolean speakerOn) {
		this.speakerOn = speakerOn;
		if(speakerOn) {
			portSipSdk.setAudioDevice(PortSipEnumDefine.AudioDevice.SPEAKER_PHONE);
		}else{
			portSipSdk.setAudioDevice(PortSipEnumDefine.AudioDevice.EARPIECE);
		}
		return speakerOn;
	}

	public boolean isSpeakerOn() {
		return speakerOn;
	}

	public static CallManager Instance()
	{
			if (mInstance == null)
			{
				synchronized (locker)
				{
					if (mInstance == null)
					{
						mInstance = new CallManager();
					}
				}
			}

			return mInstance;
	}
	private CallManager()
	{
		CurrentLine = 0;
		sessions = new Session[MAX_LINES];
		for (int i = 0; i < sessions.length; i++)
		{
			sessions[i] = new Session();
			sessions[i].myid = i;
			sessions[i].LineName = "line - " + i;

		}
	}

	public void myregist(int lineid,Boolean status)
	{
      switch(lineid)
			{
				case 1:
				regist = status;
				break;
				case 2:
				regist2 = status;
				break;
				case 3:
				regist3 = status;
				break;
				default:
				regist = status;
				break;

			}
	}

	public Boolean getregist(int lineid)
	{
		  Boolean myregister;

      switch(lineid)
			{
				case 1:
				myregister = regist;
				break;
				case 2:
				myregister = regist2;
				break;
				case 3:
				myregister = regist3;
				break;
				default:
				myregister = regist;
				break;

			}

			return myregister;
	}

	public boolean HasActiveSession()
	{

		for(Session session: sessions)
		{
			if (session.SessionID > Session.INVALID_SESSION_ID)
			{
				return true;
			}
		}

		return false;
	}

	public Session findSessionBySessionID(long SessionID)
	{
		for(Session session :sessions)
		{
			if (session.SessionID == SessionID)
			{
				return session;
			}
		}
		return null;
	}

	public Session findIdleSession()
	{
		for(Session session :sessions)
		{
			if (session.IsIdle())
			{
				return session;
			}
		}
		return null;
	}

	public Session getCurrentSession()
	{
		if (CurrentLine >= 0 && CurrentLine <= sessions.length)
		{

			return sessions[CurrentLine];

		}
		return null;
	}

	public Session findSessionByIndex(int index)
	{
		if (index >= 0 && index <= sessions.length)
		{

			return sessions[index];

		}
		return null;
	}
    public void addActiveSessionToConfrence(int lineid,PortSipSdk sdk)
    {
        for (Session session : sessions)
        {
            if(session.voip_line == lineid && session.state == Session.CALL_STATE_FLAG.CONNECTED)
            {
							  if (session.getHoldState())
							  {
								    sdk.unHold(session.getSessionId());
									  session.setHoldState(false);
							  }

                sdk.joinToConference(session.SessionID);
                sdk.sendVideo(session.SessionID, true);
								sdk.setRemoteVideoWindow(session.SessionID, null);
            }
        }
    }

	public void setRemoteVideoWindow(PortSipSdk sdk,long sessionid,PortSIPVideoRenderer renderer){
		sdk.setConferenceVideoWindow(null);
		for (Session session : sessions)
		{
			if(session.state == Session.CALL_STATE_FLAG.CONNECTED&&sessionid!=session.SessionID)
			{
				sdk.setRemoteVideoWindow(session.SessionID,null);
			}
		}
		sdk.setRemoteVideoWindow(sessionid,renderer);
	}

	public void setConferenceVideoWindow(PortSipSdk sdk,PortSIPVideoRenderer renderer){
		for (Session session : sessions)
		{
			if(session.state == Session.CALL_STATE_FLAG.CONNECTED)
			{
				sdk.setRemoteVideoWindow(session.SessionID,null);
			}
		}
		sdk.setConferenceVideoWindow(renderer);
	}
	public void myResetAll(int lineid)
	{
		for(Session session :sessions)
		{
			if(session.voip_line == lineid)
			    session.Reset();
		}
	}

	public void resetAll()
	{
		for(Session session :sessions)
		{
			session.Reset();
		}
	}

	public Session findIncomingCall()
	{
		for(Session session :sessions)
		{
			if (session.SessionID != Session.INVALID_SESSION_ID&&session.state== Session.CALL_STATE_FLAG.INCOMING)
			{
				return session;
			}
		}

		return null;
	}
	public boolean isMyConference() {
		return isMyConference;
	}
	public boolean isMoreLine()
	{
		int index;

		index = useLineCount();

		if(index >= MIN_MORE_LINES)
		    return true;

		return false;
  }
	public boolean is2Line()
	{
		Log.i("call","is2Line "+mis2Line);
		return mis2Line;
	}

	public boolean changeWaitLine()
	{
		Boolean ret;

		ret = false;

		for (int i = 0; i < sessions.length; i++) {
			if(i != CurrentLine && i != _waitLine && sessions[i].getSessionConnected())
			{
				_waitLine = i;
				ret = true;
				break;
			}
	  }

	  return ret;
	}

	public boolean changeCurrentLine()
	{
		boolean ret;

		ret = false;

		for (int i = 0; i < sessions.length; i++) {
			if(i != CurrentLine && i != _waitLine && sessions[i].getSessionConnected())
			{
				CurrentLine = i;
				ret = true;
				break;
			}
	  }

	  return ret;

	}

	public Session getWaitSession()
  {
	    return sessions[_waitLine];
  }

	public boolean isCurrentLine(Session line)
	{
		return line == sessions[CurrentLine];
	}
	public void setCurrentLine(Session session)
	{
      CurrentLine = session.myid;
	}
	public void set2Line(boolean is2Line)
	{
		Log.i("call","set2Line "+is2Line);
		mis2Line = is2Line;
	}

	public void removeConf(PortSipSdk mysdk)
	{
		for (int i = 0; i < sessions.length; i++) {
			if (sessions[i].getSessionConnected()) {
				mysdk.removeFromConference(sessions[i].getSessionId());

			}
	   }

		mysdk.destroyConference ();

		setMyConferenceMode(false);

	}
	public boolean upperLineCount() {
	  int index;

		index = useLineCount();

		if(index >= MAX_CONF_LINES)
		    return true;

		return false;
	}

	public void swapLine()
	{
		int tmp;
		tmp = _waitLine;
		_waitLine = CurrentLine;
		CurrentLine = tmp;
	}
	public void setMyConferenceMode(boolean state) {
		isMyConference = state;
	}

	public void addCurrentLine(Session line)
	{
		if (line == null) {

		} else {
			_waitLine = CurrentLine;
			CurrentLine = line.myid;
			set2Line(true);
		}
	}

	public int useLineCount()
	{
		int index;

		index = 0;
		for (int i = 0; i < sessions.length; i++) {
			if(sessions[i].getRecvCallState() || sessions[i].getSessionConnected())
			{
				index++;
			}
	  }

	  return index;
	}

	public void setDoSession(Session session)
	{
		doSession = session;
	}

	public Session getDoSession()
	{
		return doSession;
	}

}
