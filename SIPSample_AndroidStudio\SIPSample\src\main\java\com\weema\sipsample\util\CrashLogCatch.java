package com.weema.sipsample.util;
import android.app.Activity;
import android.app.Service;
import android.content.Context;
import 	java.io.IOException;
import java.io.FileOutputStream;
import android.util.Log;
import 	android.os.Environment;
import java.io.File;
import 	java.text.SimpleDateFormat;
import 	java.util.Calendar;
import java.io.DataOutputStream;
import java.io.InputStream;
import com.weema.sipsample.ui.MyApplication;
public class CrashLogCatch {
	private static final String TAG="CrashLogCatch";
    public static final long MAX_START_TIME = 2000;   //主线程名称
	
    private static long starttime ;
    private static String m_log_name = "log-";
	public static void initCrashLog(final Context context) {

		final Thread.UncaughtExceptionHandler defaultUEH = Thread.getDefaultUncaughtExceptionHandler();
        starttime = System.currentTimeMillis() ;
		Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
			public void uncaughtException(Thread thread, Throwable e) {

                m_log_name = "strace-";
                logit(e);
        
                android.os.Process.killProcess(android.os.Process.myPid());  //如果是service直接kill掉
                //defaultUEH.uncaughtException(thread, e);
                if(true)    return;

                long curtime = System.currentTimeMillis();

                if(curtime < (starttime+MAX_START_TIME))
                    defaultUEH.uncaughtException(thread, e);
                else
                    android.os.Process.killProcess(android.os.Process.myPid());  //如果是service直接kill掉

				
			}
		});
	}

    public static void logit(Throwable e) {
        StackTraceElement[] arr = e.getStackTrace();
        String report = e.toString() + "\n\n";
        report += "version " + MyApplication.VERSION + "\n";

        // 加上時間戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStr = sdf.format(new java.util.Date());
        report += "time " + timeStr + "\n";

        report += "--------- Stack trace ---------\n\n";
        for (StackTraceElement element : arr) {
            report += "    " + element.toString() + "\n";
        }
        report += "-------------------------------\n\n";

        // If the exception was thrown in a background thread inside
        // AsyncTask, then the actual exception can be found with getCause

        report += "--------- Cause ---------\n\n";
        Throwable cause = e.getCause();
        if(cause != null) {
            report += cause.toString() + "\n\n";
            arr = cause.getStackTrace();
            for (int i=0; i<arr.length; i++) {
                report += "    "+arr[i].toString()+"\n";
            }
        }
        report += "-------------------------------\n\n";

        try {
            String path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath();
        
            //File file = new File(path, "stack.txt");
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
 
            Calendar c = Calendar.getInstance();
            String str = m_log_name+df.format(c.getTime())+".txt";
            File file = new File(path, str);
            FileOutputStream trace = new FileOutputStream(file);

            trace.write(report.getBytes());
            trace.close();
            Log.i(TAG,report);
        } catch(IOException ioe) {
        // ...
        }
        catch(Throwable e1) {
            // ...
        }

        finally {
            
        }
        //android.os.Process.killProcess(android.os.Process.myPid());  //如果是service直接kill掉
   
    }
}