package com.weema.sipsample.util;

import org.json.JSONObject;
import org.json.JSONException;

public class JsonUtil {


public static String toJSon(TongXunLu person) {
      try {
        // Here we convert Java Object to JSON
        JSONObject jsonObj = new JSONObject();

        jsonObj.put("xingming", person.getXingming()); // Set the first name/pair
        jsonObj.put("haoma", person.getHaoma()); // Set the first name/pair
        jsonObj.put("beizhu", person.getBeizhu());
        jsonObj.put("touxiangPath", person.getTouxiangPath());

        return jsonObj.toString();

    }
    catch(JSONException ex) {
        ex.printStackTrace();
    }

    return null;

   }


   public static  Object fromJSon(String data,String action) {

     if(action.equals("TongXunJiLu"))
         return (Object)fromJSon1(data);

     if(action.equals("TongXunLu"))
           return (Object)fromJSon2(data);

     return null;
   }

   public static  TongXunLu fromJSon2(String data) {
         try {
           // Here we convert Java Object to JSON
           JSONObject jObj = new JSONObject(data);

           TongXunLu person = new TongXunLu();

           String xingming = jObj.getString("xingming");
           person.setXingming(xingming);

           String haoma = jObj.getString("haoma");
           person.setHaoma(haoma);

           String beizhu = jObj.getString("beizhu");
           person.setBeizhu(beizhu);

           String touxiangPath = jObj.getString("touxiangPath");
           person.setTouxiangPath(touxiangPath);

           return person;

       }
       catch(JSONException ex) {
           ex.printStackTrace();
       }

       return null;

      }

      public static String toJSon(TongXunJiLu person) {
            try {
              // Here we convert Java Object to JSON
              JSONObject jsonObj = new JSONObject();

              jsonObj.put("isJieTong", person.isJieTong()); // Set the first name/pair
              jsonObj.put("phone", person.getPhonenum()); // Set the first name/pair
              jsonObj.put("name", person.getName());
              jsonObj.put("riqi", person.getRiqi());
              jsonObj.put("shijian", person.getShijian());

              jsonObj.put("isVideo", person.getVideo());
              jsonObj.put("isOutgoing", person.getOutgoing());

              return jsonObj.toString();

          }
          catch(JSONException ex) {
              ex.printStackTrace();
          }

          return null;

         }

         public static  TongXunJiLu fromJSon1(String data) {
               try {
                 // Here we convert Java Object to JSON
                 JSONObject jObj = new JSONObject(data);

                 TongXunJiLu person = new TongXunJiLu();

                 Boolean isJieTong = jObj.getBoolean("isJieTong");
                 person.setJieTong(isJieTong);

                 String phone = jObj.getString("phone");
                 person.setPhonenum(phone);

                 String name = jObj.getString("name");
                 person.setName(name);

                 String riqi = jObj.getString("riqi");
                 person.setRiqi(riqi);

                 String shijian = jObj.getString("shijian");
                 person.setShijian(shijian);

                 Boolean isVideo = jObj.getBoolean("isVideo");
                 person.setVideo(isVideo);

                 Boolean isOutgoing = jObj.getBoolean("isOutgoing");
                 person.setOutgoing(isOutgoing);

                 return person;

             }
             catch(JSONException ex) {
                 ex.printStackTrace();
             }

             return null;

            }

}
