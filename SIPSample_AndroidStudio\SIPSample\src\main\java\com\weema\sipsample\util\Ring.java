package com.weema.sipsample.util;

import android.media.AudioManager;
import android.media.Ringtone;
import android.media.RingtoneManager;
import android.media.ToneGenerator;
import android.media.MediaPlayer;
import android.content.Context;

import android.widget.Toast;
import com.weema.R;
import android.util.Log;
import android.text.TextUtils;
import com.weema.sipsample.ui.MyApplication;
public class Ring {
    private static final String TAG="Ring";
		private static final int TONE_RELATIVE_VOLUME = 70;
		private ToneGenerator mRingbackPlayer=null;
		private ToneGenerator mDTMFPlayer;
		protected MediaPlayer mRingtonePlayer=null;
		int ringRef = 0;
		private static Context mContext;

		private static Ring single=null;

		private static MyApplication myApp;
    private static AudioManager audioManager=null;
		public static Ring getInstance(Context context) {
		     if (single == null) {
		        single = new Ring(context);
						mContext = context;
						myApp = (MyApplication) mContext.getApplicationContext();						
		      }

				  if(audioManager == null){
						audioManager = (AudioManager)context.getSystemService(Context.AUDIO_SERVICE);

				  }

		        return single;
		    }
		private Ring(Context context){
		//	mContext = context;
		}
		public boolean stop() {
			stopRingBackTone();
			stopRingTone();
			stopDTMF();

			return true;
		}

		public void startDTMF(int number) {
			if (mDTMFPlayer == null) {
				try {
					mDTMFPlayer = new ToneGenerator(AudioManager.STREAM_VOICE_CALL, TONE_RELATIVE_VOLUME);
				} catch (RuntimeException e) {
					mDTMFPlayer = null;
				}
			}

			if(mDTMFPlayer != null){
				synchronized(mDTMFPlayer){
					switch(number){
						case 0: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_0); break;
						case 1: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_1); break;
						case 2: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_2); break;
						case 3: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_3); break;
						case 4: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_4); break;
						case 5: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_5); break;
						case 6: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_6); break;
						case 7: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_7); break;
						case 8: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_8); break;
						case 9: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_9); break;
						case 10: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_S); break;
						case 11: mDTMFPlayer.startTone(ToneGenerator.TONE_DTMF_P); break;
					}
				}
			}
		}

		public void stopDTMF() {
			if(mDTMFPlayer != null){
				synchronized(mDTMFPlayer){
					mDTMFPlayer.stopTone();
					mDTMFPlayer = null;
				}
			}
		}
		public void startDoorBellTone() {
			startRingTone(R.raw.doorbell,false);
			
		}
	
		public void startRingTone() {
			startRingTone(R.raw.old_ring,true);
		}
		public void startRingTone(int id,boolean repeater) {
			if(mRingtonePlayer != null){//}&&mRingtonePlayer.isPlaying()){
				ringRef++;
				mRingtonePlayer.reset();
				try{

					mRingtonePlayer = MediaPlayer.create(mContext,id);
          //mRingtonePlayer.prepare();
				}catch(IllegalStateException e)
				{
					e.printStackTrace();
				}

				catch(Exception e){
					e.printStackTrace();
					//return;
				}

			}

			if(mRingtonePlayer == null&&mContext!=null){
				try{

					mRingtonePlayer = MediaPlayer.create(mContext,id);
          //mRingtonePlayer.prepare();
				}catch(IllegalStateException e)
				{
					e.printStackTrace();
				}

				catch(Exception e){
					e.printStackTrace();
					//return;
				}
			}

			if(mRingtonePlayer != null){
				synchronized(mRingtonePlayer){
					ringRef++;

						mRingtonePlayer.start();
						mRingtonePlayer.setLooping(repeater);

				}
			}
		}		

		public void stopRingTone() {
			if(mRingtonePlayer != null){
				synchronized(mRingtonePlayer){

					  ringRef = 0;
						mRingtonePlayer.stop();
						mRingtonePlayer.release();
						mRingtonePlayer = null;

				}
			}
		}

		public void startHoldTone() {
			startRingTone(R.raw.hold,true);
		
		}

		public void stopHoldTone() {
			stopRingTone();
			
		}


public void startCallWaitingTone() {
	Log.i(TAG,"startCallWaitingTone");
	startRingTone(R.raw.callwaiting,false);
	
}

public void stopCallWaitingTone() {
	stopRingTone();

}

		public void startRingBackTone() {
			startRingTone(R.raw.ringbacktone,true);
		
		}

		public void stopRingBackTone() {
			stopRingTone();
		
		}

   public void adjust_call_volume(int direction)
	 {

		 audioManager.adjustStreamVolume(AudioManager.STREAM_VOICE_CALL,direction,AudioManager.FLAG_SHOW_UI);

	 }


	 public int getVolume()
	 {
		int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL);
		int volume = audioManager.getStreamVolume(AudioManager.STREAM_VOICE_CALL);

		double val = volume*5;
		val = val/maxVolume;
		double val1 = Math.ceil(val);

		return (int)val1;
	 }

	 public void setVolume(int val)
	 {
		int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL);
		val = (maxVolume*(val))/5;

		audioManager.setStreamVolume(AudioManager.STREAM_VOICE_CALL,val,0);
	 }

	 public int getRingVolume()
	 {
		int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
		int volume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);

       	double val = volume*5;
		val = val/maxVolume;
       	double val1 = Math.ceil(val);

		return (int)val1;
	 }

	public void setRingVolume(int val)
	{
		int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
		val = (maxVolume*(val))/5;

		Log.i("ring",Integer.toString(maxVolume));

		Log.i("ring",Integer.toString(val));
		audioManager.setStreamVolume(AudioManager.STREAM_MUSIC,val,0);
	}

	 public void startBCTone()
	 {
		startRingTone(R.raw.bc_ring,false);
	 }

	 public void startRingTone(Session session)
	 {
		//startRingTone();
		startRingTone(getRingSource(session),true);
	 }

	 private String getRemoteCaller(String caller)
	 {
		 int index = caller.indexOf("@");
	 
		 if(index <= 0)    return caller;
		 String msg = caller.substring(0,index);
		 msg = msg.replace("sip:","");
	 
		 return msg;
			   
	 }		
	 private int getNumber(String str)
	 {
		 int number = 0;
 
		 try{
			 number = Integer.parseInt(str);
		 }catch(NumberFormatException ex)
		 {
 
		 }
		 return number;
	 }	 
	 private int getRingSource(Session session)
	 {
		 String caller = getRemoteCaller(session.Remote);
 
		 int number = 0;
		 if (TextUtils.isDigitsOnly(caller)) {
			 number = getNumber(caller);
		 }
			 
		 int source_id;
		 if(myApp.m_monitor_enable && myApp.m_monitor_number.equals(caller))
		 {
			 source_id = R.raw.doorbell;
		 }
		 else if(number <= 200)
		 {
			 source_id = R.raw.crazy_latin;
				 
		 }
		 else if(number < 999)
		 {
			 source_id = R.raw.ringc01;
		 }
		 else if(number < 5000)
		 {
			 source_id = R.raw.funk;
		 }
		 else 
		 {
			 source_id = R.raw.echo_beach;
		 }
		 
		 return source_id;
 
	 }	 
	}
