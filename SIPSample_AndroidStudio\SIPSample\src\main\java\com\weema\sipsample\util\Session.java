package com.weema.sipsample.util;


public class Session
{
	public static int INVALID_SESSION_ID = -1;
  public String showRemote;
	public String Remote;
	public int intRemote;

  public int voip_line;
	public String DisplayName;
	public boolean HasVideo;
	public long SessionID;
	public boolean Hold;

	public boolean Mute;
	public String LineName;
	public CALL_STATE_FLAG state;

  public boolean isSendVideo;

  public String callNumber;
  public boolean mVideoState;

  public int myid;
  public int mic_value;
	public boolean IsIdle()
	{

		if(state == CALL_STATE_FLAG.FAILED || state == CALL_STATE_FLAG.CLOSED)
		    return true;
		return false;
	}
	public Session()
	{
      Reset();
	}

	public void Reset()
	{
		Hold = false;
		Remote = null;
		DisplayName = null;
		HasVideo = false;
		SessionID = INVALID_SESSION_ID;
		state = CALL_STATE_FLAG.CLOSED;
		isSendVideo = false;
		mic_value = 3;
	}

	public enum CALL_STATE_FLAG
	{
		INCOMING,
		TRYING ,
		CONNECTED,
		FAILED,
		CLOSED,
	}

	public long getSessionId() {
		return SessionID;
	}



	public boolean getRecvCallState() {
		if(state == CALL_STATE_FLAG.INCOMING)
		    return true;
		else
		    return false;
	}
	public void setIsSendVideo(boolean isVideo) {
		isSendVideo = isVideo;;
	}
	public boolean getSessionConnected()
	{
		if(state == CALL_STATE_FLAG.CONNECTED)
		    return true;
		else
		    return false;
	}

	public boolean getIsSendVideo() {
		return isSendVideo;
	}

	public boolean getHoldState()
	{
		return Hold;
	}

	public void setHoldState(boolean state)
	{
      Hold = state;
	}

	public String getLineName()
	{
		return LineName;
	}

	public void setVideoState(boolean state) {
			mVideoState = state;
	}

	public boolean getVideoState() {
			return mVideoState;
	}

	public boolean getTryingState()
	{
		return (state == CALL_STATE_FLAG.TRYING);
	}

	public void setTryingState()
	{
		state = CALL_STATE_FLAG.TRYING;
	}

	public void setSessionId(long sessionId) {
		SessionID = sessionId;
	}


  public void setDescriptionString(String str)
  {

  }
}
