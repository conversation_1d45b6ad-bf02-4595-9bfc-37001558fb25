package com.weema.sipsample.util;

import java.util.HashMap;
import java.util.UUID;
import android.util.Log;

import com.portsip.PortSipEnumDefine;
import com.portsip.PortSipSdk;
import com.weema.R;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.content.res.Resources;

import android.preference.PreferenceManager;

import android.util.Log;

public class SettingConfig {
	 private static final String TAG="SettingConfig";
	public static final String PRE_NAME = "com.weema_preferences";
	static final String PORTSIP_USERNAME = "PORTSIP_USERNAME";
	static final String PORTSIP_USERPASSWORD = "PORTSIP_USEPWD";
	static final String PORTSIP_SIPSERVER = "PORTSIP_SIPSERVER";
	static final String PORTSIP_SIPSERVERPORT = "PORTSIP_SIPPORT";
	static final String PORTSIP_USERDOMAIN = "PORTSIP_USERDOMAIN";
	static final String PORTSIP_AUTHNAME = "PORTSIP_AUTHNAME";
	static final String PORTSIP_USEDISPALYNAME= "PORTSIP_DISNAME";

	static final String PORTSIP_STUNSVR= "PORTSIP_STUNSVR";
	static final String PORTSIP_STUNPORT= "PORTSIP_STUNPORT";
	static final String PORTSIP_TRANSTYPE= "PORTSIP_TRANSTYPE";
	static final String PORTSIP_SRTPTYPE= "PORTSIP_SRTPTYPE";
	static final String PORTSIP_U= "PORTSIP_SRTPTYPE";
	static final String PORTSIP_TIME= "PORTSIP_TIME";
	static final String PORTSIP_AUDIO_DEVICE= "PORTSIP_AUDIO_DEVICE";
  static final String PORTSIP_VIDEO_DEVICE= "PORTSIP_VIDEO_DEVICE";
	static final String PORTSIP_FWD= "PORTSIP_FWD";
  static final String PORTSIP_ENABLE_FWD= "PORTSIP_ENABLE_FWD";
	static final String PORTSIP_DND= "PORTSIP_DND";
	static final String PORTSIP_LOCK= "PORTSIP_LOCK";
	static final String PORTSIP_LOCKPASSWD= "PORTSIP_LOCKPASSWD";
	static final String PORTSIP_MIC= "PORTSIP_MIC";
	static final String PORTSIP_VOLUME= "PORTSIP_VOLUME";
	static final String PORTSIP_ZONE= "PORTSIP_ZONE";
	static final String PORTSIP_MDOOR= "PORTSIP_MDOOR2";
	static final String PORTSIP_MDOOR_ENABLE= "PORTSIP_MDOOR_ENABLE";
	static final String PORTSIP_IS_MDOOR= "PORTSIP_IS_MDOOR";
	static final String PORTSIP_SD= "PORTSIP_SD";
  static final String PORTSIP_MS= "PORTSIP_MS";
  	static final String PORTSIP_CENTER_IP= "PORTSIP_CENTER_IP";
	static final String PORTSIP_ALARM_MSG= "PORTSIP_ALARM_MSG";
	static final String PORTSIP_ALARM_MSG_ENABLE= "PORTSIP_ALARM_MSG_ENABLE";

	static final String PORTSIP_RING_NUMBER = "PORTSIP_RING_NUMBER";
	static final String PORTSIP_RING_ENABLE = "PORTSIP_RING_ENABLE";
	static final String PORTSIP_RING_PHONE_IP= "PORTSIP_RING_PHONE_IP";

	static final String PORTSIP_MONITOR_NUMBER = "PORTSIP_MONITOR_NUMBER";
	static final String PORTSIP_MONITOR_ENABLE = "PORTSIP_MONITOR_ENABLE";

  static final String PORTSIP_ENABLESOS1= "PORTSIP_ENABLESOS1";
	static final String PORTSIP_ENABLESOS2= "PORTSIP_ENABLESOS2";
	static final String PORTSIP_ENABLESOS3= "PORTSIP_ENABLESOS3";
	static final String PORTSIP_ENABLESOS4= "PORTSIP_ENABLESOS4";
	static final String PORTSIP_ENABLESOS5= "PORTSIP_ENABLESOS5";

	static final String PORTSIP_SELECTLINE1= "PORTSIP_SELECTLINE1";
	static final String PORTSIP_SELECTLINE2= "PORTSIP_SELECTLINE2";
	static final String PORTSIP_SELECTLINE3= "PORTSIP_SELECTLINE3";
	static final String PORTSIP_SELECTLINE4= "PORTSIP_SELECTLINE4";
	static final String PORTSIP_SELECTLINE5= "PORTSIP_SELECTLINE5";

	static final String PORTSIP_SOSNUMBER1= "PORTSIP_SOSNUMBER1";
	static final String PORTSIP_SOSNUMBER2= "PORTSIP_SOSNUMBER2";
	static final String PORTSIP_SOSNUMBER3= "PORTSIP_SOSNUMBER3";
	static final String PORTSIP_SOSNUMBER4= "PORTSIP_SOSNUMBER4";
	static final String PORTSIP_SOSNUMBER5= "PORTSIP_SOSNUMBER5";

	static final String PORTSIP_SOSTALK= "PORTSIP_SOSTALK";

	static final String PORTSIP_ALLOW= "PORTSIP_ALLOW";
	static final String PORTSIP_NIGHT_ENABLE= "PORTSIP_NIGHT_ENABLE";	
	static final String PORTSIP_NIGHT_START= "PORTSIP_NIGHT_START";
	static final String PORTSIP_NIGHT_END= "PORTSIP_NIGHT_END";
	static final String PORTSIP_NIGHT_CALL= "PORTSIP_NIGHT_CALL";	

	static final String PORTSIP_RECORD_ENABLE= "PORTSIP_RECORD_ENABLE";	

	static final String PORTSIP_VIDEO_IP= "PORTSIP_VIDEO_IP";	

	static final String PORTSIP_IPCAM = "PORTSIP_IPCAM";	

	static final String PORTSIP_JILU= "PORTSIP_JILU";
	static final String PORTSIP_CONTACTS= "PORTSIP_CONTACTS";
	static final String DOOR_LIST= "DOOR_LIST";
	static final String IS_VOICE= "IS_VOICE";

	static final String PORTSIP_DOOR_IP="PORTSIP_DOOR_IP";
	static final String PORTSIP_DOOR_NOTIFY="PORTSIP_DOOR_NOTIFY";
	static final String PORTSIP_DOOR_RECORD="PORTSIP_DOOR_RECORD";

	static final String PORTSIP_RING_CONFIG="PORTSIP_RING_CONFIG";
	static final String PORTSIP_CTRL_GAS="PORTSIP_CTRL_GAS";
	static final String PORTSIP_CTRL_BZ="PORTSIP_CTRL_BZ";
	static final String PORTSIP_SOS_PLAY="PORTSIP_SOS_PLAY";
	static final String PORTSIP_SOS_MODE="PORTSIP_SOS_MODE";
	static final String PORTSIP_MAIL_CONTENT="PORTSIP_MAIL_CONTENT";
	static final String PORTSIP_CAN_FORCE_MGR="PORTSIP_CAN_FORCE_MGR";

    static final String PORTSIP_APP_NUMBER= "PORTSIP_APP_NUMBER";
	static public  boolean saveSettingConfig(Context context, PortSipSdk sdk) {
		SharedPreferences mPrefrence = context
				.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		Editor mPrefrenceEdit = mPrefrence.edit();

		if (mPrefrenceEdit == null) {
			return false;
		}

//		mPrefrenceEdit.putBoolean(PORTSIP_FEATURE_VAD,mVAD);
//		sdk.enableVAD(mVAD);
//		mPrefrenceEdit.putBoolean(PORTSIP_FEATURE_AEC,mAEC);
//		sdk.enableAEC(mAEC);
//		mPrefrenceEdit.putBoolean(PORTSIP_FEATURE_ANS,mANS);
//		sdk.enableANS(mANS);
//		mPrefrenceEdit.putBoolean(PORTSIP_FEATURE_AGC,mAGC);
//		sdk.enableAGC(mAGC);
//		mPrefrenceEdit.putBoolean(PORTSIP_FEATURE_DTMFINFO,mDTMFINFO);

		return mPrefrenceEdit.commit();
	}

//	static public  void setCodecStatusById(Context context,HashMap<Integer,Boolean> codecs) {
//		if(codecs==null)
//			return;
//		boolean value;
//		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVAT);
//		Editor editor = mPrefrence.edit();
//
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_G729);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_G729, value);
//		//pcma
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_PCMA);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_PCMA, value);
//		//pcmu
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_PCMU);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_PCMU, value);
//		//gsm
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_GSM);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_GSM, value);
//		//g722
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_G722);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_G722, value);
//		//ilbc
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_ILBC);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_ILBC, value);
//		//amr
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_AMR);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_AMR, value);
//		//amrwb
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_AMR);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_AMR, value);
//		//speex
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_SPEEX);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_SPEEX, value);
//		//speexwb
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_SPEEXWB);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_SPEEXWB, value);
//		//Opus
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_OPUS);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_OPUS, value);
//		//dtmf
//		value = codecs.get(PortSipEnumDefine.ENUM_AUDIOCODEC_DTMF);
//		editor.putBoolean(PORTSIP_AUDIOCODEC_DTMF, value);
//		//h263
//		value = codecs.get(PortSipEnumDefine.ENUM_VIDEOCODEC_H263);
//		editor.putBoolean(PORTSIP_VIDEOCODEC_H263, value);
//		//h263.98
//		value = codecs.get(PortSipEnumDefine.ENUM_VIDEOCODEC_H263_1998);
//		editor.putBoolean(PORTSIP_VIDEOCODEC_H263_1998, value);
//		//h264
//		value = codecs.get(PortSipEnumDefine.ENUM_VIDEOCODEC_H264);
//		editor.putBoolean(PORTSIP_VIDEOCODEC_H264, value);
//
//		editor.commit();
//	}

	public static HashMap<Integer,Boolean> getCodecs(Context context){
		HashMap<Integer,Boolean> codecs= new HashMap<Integer,Boolean>();
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
//		Editor editor = mPrefrence.edit();
		String key;
		boolean value;
		Resources resources= context.getResources();
		//g729
		key = resources.getString(R.string.MEDIA_G729);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_G729, value);
		//pcma
		key = resources.getString(R.string.MEDIA_PCMA);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_PCMA, value);
		//pcmu
		key = resources.getString(R.string.MEDIA_PCMU);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_PCMU, value);
		//gsm
		key = resources.getString(R.string.MEDIA_GSM);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_GSM, value);
		//g722
		key = resources.getString(R.string.MEDIA_G722);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_G722, value);
		//ilbc
		key = resources.getString(R.string.MEDIA_ILBC);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_ILBC, value);
		//amr
		key = resources.getString(R.string.MEDIA_AMR);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_AMR, value);
		//amrwb
		key = resources.getString(R.string.MEDIA_AMRWB);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_AMRWB, value);
		//speex
		key = resources.getString(R.string.MEDIA_SPEEX);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_SPEEX, value);
		//speexwb
		key = resources.getString(R.string.MEDIA_SPEEXWB);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_SPEEXWB, value);
		//Opus
		key = resources.getString(R.string.MEDIA_OPUS);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_OPUS, value);

		//dtmf
		key = resources.getString(R.string.MEDIA_DTMF);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_AUDIOCODEC_DTMF, value);
		//h264
		key = resources.getString(R.string.MEDIA_H264);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_VIDEOCODEC_H264, value);

		//vp8
		key = resources.getString(R.string.MEDIA_VP8);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_VIDEOCODEC_VP8, value);

		//vp9
		key = resources.getString(R.string.MEDIA_VP9);
		value = mPrefrence.getBoolean(key, false);
		codecs.put(PortSipEnumDefine.ENUM_VIDEOCODEC_VP9, value);
		return codecs;
	}
		public static boolean setAppNumber(Context context,String value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_APP_NUMBER, value);

    editor.commit();

		return true;
	}

	public static String getAppNumber(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String value = mPrefrence.getString(PORTSIP_APP_NUMBER, "");

		return value;
	}

	public static UserInfo getUserInfo(Context context){
		UserInfo infor = new UserInfo();
		SharedPreferences mPrefrence = context
				.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		String stringitem = mPrefrence.getString(PORTSIP_USERNAME, "");
		infor.setUserName(stringitem);
		stringitem = mPrefrence.getString(PORTSIP_USERPASSWORD, "");
		infor.setUserPassword(stringitem);
		stringitem = mPrefrence.getString(PORTSIP_USEDISPALYNAME, "");
		infor.setUserDisplayName(stringitem);
		stringitem = mPrefrence.getString(PORTSIP_USERDOMAIN, "");
		infor.setUserDomain(stringitem);
		stringitem = mPrefrence.getString(PORTSIP_SIPSERVER, "");
		infor.setSipServer(stringitem);
		int port = mPrefrence.getInt(PORTSIP_SIPSERVERPORT, 5060);
		infor.setSipPort(port);

		stringitem = mPrefrence.getString(PORTSIP_STUNSVR, "");
		infor.setStunServer(stringitem);

		port = mPrefrence.getInt(PORTSIP_STUNPORT, 5060);
		infor.setStunPort(port);

		int tansType = mPrefrence.getInt(PORTSIP_TRANSTYPE, PortSipEnumDefine.ENUM_TRANSPORT_UDP);
		infor.setTranType(tansType);

		int srtpType = mPrefrence.getInt(PORTSIP_SRTPTYPE, PortSipEnumDefine.ENUM_SRTPPOLICY_NONE);
		infor.setSrtpType(srtpType);
		return infor;
	}


	public  static void setUserInfo(Context context,UserInfo infor){
		if(infor!=null){
			SharedPreferences mPrefrence = context
					.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
			Editor editor = mPrefrence.edit();
			String stringitem = infor.getUserName();
			editor.putString(PORTSIP_USERNAME, stringitem);

			stringitem = infor.getUserPassword();
			editor.putString(PORTSIP_USERPASSWORD, stringitem);

			stringitem = infor.getUserDisplayName();
			editor.putString(PORTSIP_USEDISPALYNAME, stringitem);

			stringitem = infor.getUserDomain();
			editor.putString(PORTSIP_USERDOMAIN, stringitem);

			stringitem = infor.getSipServer();
			editor.putString(PORTSIP_SIPSERVER, stringitem);

			int port = infor.getSipPort();
			editor.putInt(PORTSIP_SIPSERVERPORT, port);


			stringitem = infor.getStunServer();
			editor.putString(PORTSIP_STUNSVR, stringitem);


			port = infor.getStunPort();
			editor.putInt(PORTSIP_STUNPORT, port );


			int tansType = infor.getTransType();
			editor.putInt(PORTSIP_TRANSTYPE, tansType );

			int srtpType = infor.getSrtpType();
			editor.putInt(PORTSIP_SRTPTYPE, srtpType );

			editor.commit();
		}

	}


	public static boolean getCanForceMgr(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		return  mPrefrence.getBoolean(PORTSIP_CAN_FORCE_MGR, false);

	}
	
	public static void setCanForceMgr(Context context,boolean val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		Editor editor = mPrefrence.edit();
		editor.putBoolean(PORTSIP_CAN_FORCE_MGR, val);
		
		editor.commit();

	}

	public static boolean getVoice(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		return  mPrefrence.getBoolean("IS_VOICE", false);

	}
	public static void setVoice(Context context,boolean val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		Editor editor = mPrefrence.edit();
		editor.putBoolean(IS_VOICE, val);
		
		editor.commit();

	}

	public static void setSrtpType(Context context,int enum_srtpType,PortSipSdk sdk){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		Editor editor = mPrefrence.edit();
		editor.putInt(PORTSIP_SRTPTYPE, enum_srtpType);
		sdk.setSrtpPolicy(enum_srtpType);
		editor.commit();

	}

	public static void setTransType(Context context,int enum_transType){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		Editor editor = mPrefrence.edit();
		editor.putInt(PORTSIP_TRANSTYPE, enum_transType);
		editor.commit();

	}

	public static void setVideoIP(Context context,String ip){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		Editor editor = mPrefrence.edit();
		editor.putString(PORTSIP_VIDEO_IP, ip);
		editor.commit();

	}
	
	public static void setIPCam(Context context,String ipcam){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		Editor editor = mPrefrence.edit();
		editor.putString(PORTSIP_IPCAM, ipcam);
		editor.commit();

	}		
	public static String getIPCam(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		String ipcam = mPrefrence.getString(PORTSIP_IPCAM, "");
		
		return ipcam;
	}	
	public static String getUniqueID(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		String uniqueID = mPrefrence.getString("APP_SESSION_ID", "");
		if(uniqueID.equals(""))
		{
			Editor editor = mPrefrence.edit();
			uniqueID = UUID.randomUUID().toString();

			editor.putString("APP_SESSION_ID", uniqueID);
		}
		return uniqueID;
	}

	public static String getVideoIP(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
    
		return mPrefrence.getString(PORTSIP_VIDEO_IP, "");
	}
	public static int getRegisterTime(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
    int time;

		time = mPrefrence.getInt(PORTSIP_TIME, 60);

    if(time < 60)
		    time = 60;

		return time;
	}

	public static int getSrtpType(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		return mPrefrence.getInt(PORTSIP_SRTPTYPE, PortSipEnumDefine.ENUM_SRTPPOLICY_NONE);
	}

	public static void setAVArguments(Context context,PortSipSdk sdk) {

		// audio codecs
		//SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
    SharedPreferences mPrefrence = PreferenceManager.getDefaultSharedPreferences(context) ;

		sdk.clearAudioCodec();
		Resources resources = context.getResources();

		String key = resources.getString(R.string.MEDIA_G722);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_G722);
		}

		key = resources.getString(R.string.MEDIA_G729);
		if (mPrefrence.getBoolean(key, true)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_G729);
		}

		key = resources.getString(R.string.MEDIA_AMR);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_AMR);
		}

		key = resources.getString(R.string.MEDIA_AMRWB);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_AMRWB);
		}

		key = resources.getString(R.string.MEDIA_GSM);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_GSM);
		}

		key = resources.getString(R.string.MEDIA_PCMA);
		if (mPrefrence.getBoolean(key, true)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_PCMA);
		}

		key = resources.getString(R.string.MEDIA_PCMU);
		if (mPrefrence.getBoolean(key, true)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_PCMU);
		}

		key = resources.getString(R.string.MEDIA_SPEEX);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_SPEEX);
		}

		key = resources.getString(R.string.MEDIA_SPEEXWB);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_SPEEXWB);
		}

		key = resources.getString(R.string.MEDIA_ILBC);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_ILBC);
		}

		key = resources.getString(R.string.MEDIA_ISACWB);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_ISACWB);
		}

		key = resources.getString(R.string.MEDIA_ISACSWB);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_ISACSWB);
		}

		key = resources.getString(R.string.MEDIA_OPUS);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_OPUS);
		}

		key = resources.getString(R.string.MEDIA_DTMF);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addAudioCodec(PortSipEnumDefine.ENUM_AUDIOCODEC_DTMF);
		}



		// Video codecs
		sdk.clearVideoCodec();
		key = resources.getString(R.string.MEDIA_H264);
		if (mPrefrence.getBoolean(key, true)) {
			sdk.addVideoCodec(PortSipEnumDefine.ENUM_VIDEOCODEC_H264);
		}
		key = resources.getString(R.string.MEDIA_VP8);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addVideoCodec(PortSipEnumDefine.ENUM_VIDEOCODEC_VP8);
		}

		key = resources.getString(R.string.MEDIA_VP9);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.addVideoCodec(PortSipEnumDefine.ENUM_VIDEOCODEC_VP9);
		}
		sdk.setVideoNackStatus(true);
		key = resources.getString(R.string.str_resolution);

		String resolution = mPrefrence.getString(key, "CIF");
		int width = 352;
		int height = 288;
		if(resolution.equals("QCIF"))
		{
			width = 176;
			height = 144;
		}
		else if(resolution.equals("CIF"))
		{
			width = 352;
			height = 288;
		}
		else if(resolution.equals("VGA"))
		{
			width = 640;
			height = 480;
		}
		else if(resolution.equals("720P"))
		{
			width = 1280;
			height = 720;
		}
		else if(resolution.equals("1080P"))
		{
			width = 1920;
			height = 1080;
		}

		sdk.setVideoResolution(width, height);

//		setForward(mpreferences);
		key = resources.getString(R.string.MEDIA_VAD);
		sdk.enableVAD(mPrefrence.getBoolean(key, true));

		key = resources.getString(R.string.MEDIA_AEC);
		sdk.enableAEC(mPrefrence.getBoolean(key, true));

		key = resources.getString(R.string.MEDIA_ANS);
		sdk.enableANS(mPrefrence.getBoolean(key, false));

		key = resources.getString(R.string.MEDIA_AGC);
		sdk.enableAGC(mPrefrence.getBoolean(key, false));

		key = resources.getString(R.string.MEDIA_CNG);
		sdk.enableCNG(mPrefrence.getBoolean(key, true));

		key = resources.getString(R.string.str_pracktitle);
		if (mPrefrence.getBoolean(key, false)) {
			sdk.enableReliableProvisional(true);
		}

		// Use earphone
		sdk.setAudioDevice(PortSipEnumDefine.AudioDevice.EARPIECE);

		// Use Front Camera
		sdk.setVideoDeviceId(0);
	}

	public static boolean setCtrlGas(Context context,boolean val)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_CTRL_GAS, val);

    	editor.commit();

		return true;		
	}
	public static boolean setSOSMode(Context context,boolean val)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_SOS_MODE, val);

    	editor.commit();

		return true;		
	}	
	public static boolean setSOSPlay(Context context,boolean val)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_SOS_PLAY, val);

    	editor.commit();

		return true;		
	}

	public static boolean setRingConfig(Context context,String str)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_RING_CONFIG, str);

    	editor.commit();

		return true;		
	}
	public static String getRingConfig(Context context)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String str = mPrefrence.getString(PORTSIP_RING_CONFIG, "");

		return str;
	}
	public static boolean getCtrlGas(Context context)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean str = mPrefrence.getBoolean(PORTSIP_CTRL_GAS, false);

		return str;
	}
	public static boolean setAGC(Context context,boolean str)
	{
		SharedPreferences mPrefrence = PreferenceManager.getDefaultSharedPreferences(context);

		//SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(context.getString(R.string.MEDIA_AGC), str);

    	editor.commit();

		return true;		
	}
	public static boolean setCtrlBZ(Context context,int val)
	{
		SharedPreferences mPrefrence = PreferenceManager.getDefaultSharedPreferences(context);

		//SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putInt(PORTSIP_CTRL_BZ, val);

    	editor.commit();

		return true;		
	}	
	public static String getMailContent(Context context)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		return  mPrefrence.getString(PORTSIP_MAIL_CONTENT, "");

	}	
	public static boolean getSOSMode(Context context)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean str = mPrefrence.getBoolean(PORTSIP_SOS_MODE, true);

		return str;
	}
	public static int getCtrlBZ(Context context)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		return mPrefrence.getInt(PORTSIP_CTRL_BZ, 1);

	}	
	public static boolean getAGC(Context context)
	{
		SharedPreferences mPrefrence = PreferenceManager.getDefaultSharedPreferences(context);
		//SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean ret = mPrefrence.getBoolean(context.getString(R.string.MEDIA_AGC), false);

		return ret;
	}	
	public static boolean getSOSPlay(Context context)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean str = mPrefrence.getBoolean(PORTSIP_SOS_PLAY, false);

		return str;
	}

	public static String getDoorList(Context context)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String str = mPrefrence.getString(DOOR_LIST, "");

		return str;
	}
  public static boolean setDoorList(Context context,String str)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(DOOR_LIST, str);

    editor.commit();

		return true;
	}
	public static int setFWD(Context context,String str){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_FWD, str);

    editor.commit();

		return 0;
	}
	public static boolean setMailContent(Context context,String val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();	

		editor.putString(PORTSIP_MAIL_CONTENT, val);

		editor.commit();

		return true;
  }	
	public static boolean setMonitor(Context context,boolean is_enable,String val){
		  SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		  Editor editor = mPrefrence.edit();

			editor.putBoolean(PORTSIP_MONITOR_ENABLE, is_enable);

		  editor.putString(PORTSIP_MONITOR_NUMBER, val);

      editor.commit();

		  return true;
	}
	public static boolean setMDoor(Context context,boolean is_enable,String val,boolean is_mdoor){
		  SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		  Editor editor = mPrefrence.edit();

			editor.putBoolean(PORTSIP_MDOOR_ENABLE, is_enable);

		  editor.putString(PORTSIP_MDOOR, val);

			editor.putBoolean(PORTSIP_IS_MDOOR, is_mdoor);

      editor.commit();

		  return true;
	}
	public static boolean setRing(Context context,boolean is_enable,String val,String phone_ip){
		  SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		  Editor editor = mPrefrence.edit();

			editor.putBoolean(PORTSIP_RING_ENABLE, is_enable);

		  editor.putString(PORTSIP_RING_NUMBER, val);

		   editor.putString(PORTSIP_RING_PHONE_IP, phone_ip);

      editor.commit();

		  return true;
	}
	public static boolean setCenterIP(Context context,String val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_CENTER_IP, val);

        editor.commit();

		  return true;
	}	
	public static boolean setRecordEnable(Context context,boolean is_enable){
		  SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		  Editor editor = mPrefrence.edit();

			editor.putBoolean(PORTSIP_RECORD_ENABLE, is_enable);

      editor.commit();

		  return true;
	}

	public static boolean setAlarmMsg(Context context,boolean is_enable,String val){
		  SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		  Editor editor = mPrefrence.edit();

			editor.putBoolean(PORTSIP_ALARM_MSG_ENABLE, is_enable);

		  editor.putString(PORTSIP_ALARM_MSG, val);

      editor.commit();

		  return true;
	}

	public static int setDoorIP(Context context,String ip)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_DOOR_IP, ip);

    	editor.commit();

		return 0;		
	}
	public static int setDoorNotify(Context context,boolean val)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_DOOR_NOTIFY, val);

    	editor.commit();

		return 0;		
	}	
	public static int setSOSTalkEnable(Context context,boolean val)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_SOSTALK, val);

    	editor.commit();

		return 0;		
	}	
	public static int setDoorRecord(Context context,boolean val)
	{
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_DOOR_RECORD, val);

    	editor.commit();

		return 0;		
	}
	
	public static boolean getDoorNotify(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean ret = mPrefrence.getBoolean(PORTSIP_DOOR_NOTIFY, false);

		return ret;
	}	
	public static boolean getSOSTalkEnable(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean ret = mPrefrence.getBoolean(PORTSIP_SOSTALK, false);

		return ret;
	}				
	public static boolean getDoorRecord(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean ret = mPrefrence.getBoolean(PORTSIP_DOOR_RECORD, false);

		return ret;
	}			
	public static String getDoorIP(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String stringitem = mPrefrence.getString(PORTSIP_DOOR_IP, null);

		return stringitem;
	}	
	public static boolean getRingEnable(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean stringitem = mPrefrence.getBoolean(PORTSIP_RING_ENABLE, false);

		return stringitem;
	}
	public static String getRingPhoneIP(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String stringitem = mPrefrence.getString(PORTSIP_RING_PHONE_IP, "");

		return stringitem;
	}	
	public static String getRingNumber(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String stringitem = mPrefrence.getString(PORTSIP_RING_NUMBER, "1");

		return stringitem;
	}
	public static boolean getMonitorEnable(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean stringitem = mPrefrence.getBoolean(PORTSIP_MONITOR_ENABLE, false);

		return stringitem;
	}
	public static boolean getRecordEnable(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		return mPrefrence.getBoolean(PORTSIP_RECORD_ENABLE, false);

	
	}	
	public static String getMonitorNumber(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String stringitem = mPrefrence.getString(PORTSIP_MONITOR_NUMBER, "");

		return stringitem;
	}
	public static String getCenterIP(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String stringitem = mPrefrence.getString(PORTSIP_CENTER_IP, "***********");

		return stringitem;
	}	
	public static boolean getAlarmMsgEnable(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean stringitem = mPrefrence.getBoolean(PORTSIP_ALARM_MSG_ENABLE, true);

		return stringitem;
	}
	public static String getAlarmMsg(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String stringitem = mPrefrence.getString(PORTSIP_ALARM_MSG, "88888844");

		return stringitem;
	}
	public static boolean getIsMDoor(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean stringitem = mPrefrence.getBoolean(PORTSIP_IS_MDOOR, false);

		return stringitem;
	}
	public static boolean getMDoorEnable(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean stringitem = mPrefrence.getBoolean(PORTSIP_MDOOR_ENABLE, false);

		return stringitem;
	}
	public static String getMDoor(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String stringitem = mPrefrence.getString(PORTSIP_MDOOR, "");

		return stringitem;
	}

	public static String getFWD(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String stringitem = mPrefrence.getString(PORTSIP_FWD, "");

		return stringitem;
	}

	public static int setEnableFWD(Context context,boolean value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_ENABLE_FWD, value);

    editor.commit();

		return 0;
	}


	public static boolean getEnableFWD(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean value = mPrefrence.getBoolean(PORTSIP_ENABLE_FWD, false);

		return value;
	}

	public static int setLimitcall(Context context,String allow,boolean night_enable,int[] night_time,String night_call){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);
		Editor editor = mPrefrence.edit();

        editor.putString(PORTSIP_ALLOW, allow);

		editor.putBoolean(PORTSIP_NIGHT_ENABLE, night_enable);

		editor.putInt(PORTSIP_NIGHT_START, night_time[0]);

		editor.putInt(PORTSIP_NIGHT_END, night_time[1]);
		editor.putString(PORTSIP_NIGHT_CALL, night_call);		
        editor.commit();

		return 0;
	}
	public static String getNightCall(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

        String value;

		value = mPrefrence.getString(PORTSIP_NIGHT_CALL, "");
		
		return value;
	}
	public static int[] getNightTime(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

        int[] value = new int[2];

		value[0] = mPrefrence.getInt(PORTSIP_NIGHT_START, 0);
		value[1] = mPrefrence.getInt(PORTSIP_NIGHT_END, 0);

		return value;
	}

	public static String getAllow(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

        String value;

		value = mPrefrence.getString(PORTSIP_ALLOW, "");
		
		return value;
	}
	public static boolean getNightEnable(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

        boolean value;

		value = mPrefrence.getBoolean(PORTSIP_NIGHT_ENABLE, false);
		
		return value;
	}
	public static boolean[] getEnableSOS(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

    boolean[] value = new boolean[5];

		value[0] = mPrefrence.getBoolean(PORTSIP_ENABLESOS1, true);
		value[1] = mPrefrence.getBoolean(PORTSIP_ENABLESOS2, false);
		value[2] = mPrefrence.getBoolean(PORTSIP_ENABLESOS3, false);
		value[3] = mPrefrence.getBoolean(PORTSIP_ENABLESOS4, false);
		value[4] = mPrefrence.getBoolean(PORTSIP_ENABLESOS5, false);

		return value;
	}

	public static boolean setEnableSOS(Context context,boolean[] value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_ENABLESOS1, value[0]);
		editor.putBoolean(PORTSIP_ENABLESOS2, value[1]);
		editor.putBoolean(PORTSIP_ENABLESOS3, value[2]);
		editor.putBoolean(PORTSIP_ENABLESOS4, value[3]);
		editor.putBoolean(PORTSIP_ENABLESOS5, value[4]);

    editor.commit();

		return true;
	}
	public static int[] getSelectLine(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

    int[] value = new int[5];

		value[0] = mPrefrence.getInt(PORTSIP_SELECTLINE1, 0);
		value[1] = mPrefrence.getInt(PORTSIP_SELECTLINE2, 0);
		value[2] = mPrefrence.getInt(PORTSIP_SELECTLINE3, 0);
		value[3] = mPrefrence.getInt(PORTSIP_SELECTLINE4, 0);
		value[4] = mPrefrence.getInt(PORTSIP_SELECTLINE5, 0);

		return value;
	}

	public static boolean setSelectLine(Context context,int[] value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putInt(PORTSIP_SELECTLINE1, value[0]);
		editor.putInt(PORTSIP_SELECTLINE2, value[1]);
		editor.putInt(PORTSIP_SELECTLINE3, value[2]);
		editor.putInt(PORTSIP_SELECTLINE4, value[3]);
		editor.putInt(PORTSIP_SELECTLINE5, value[4]);

    editor.commit();

		return true;
	}
	public static String[] getSOSNumber(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

    String[] value = new String[5];

		value[0] = mPrefrence.getString(PORTSIP_SOSNUMBER1, "128");
		value[1] = mPrefrence.getString(PORTSIP_SOSNUMBER2, "");
		value[2] = mPrefrence.getString(PORTSIP_SOSNUMBER3, "");
		value[3] = mPrefrence.getString(PORTSIP_SOSNUMBER4, "");
		value[4] = mPrefrence.getString(PORTSIP_SOSNUMBER5, "");

		return value;
	}
	public static boolean setSOSNumber(Context context,String[] value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_SOSNUMBER1, value[0]);
		editor.putString(PORTSIP_SOSNUMBER2, value[1]);
		editor.putString(PORTSIP_SOSNUMBER3, value[2]);
		editor.putString(PORTSIP_SOSNUMBER4, value[3]);
		editor.putString(PORTSIP_SOSNUMBER5, value[4]);

    editor.commit();

		return true;
	}
	public static boolean getMS(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean value = mPrefrence.getBoolean(PORTSIP_MS, false);

		return value;
	}
	public static boolean setMS(Context context,boolean value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_MS, value);

    editor.commit();

		return true;
	}
	public static boolean getSD(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean value = mPrefrence.getBoolean(PORTSIP_SD, false);

		return value;
	}
	public static boolean setSD(Context context,boolean value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_SD, value);

    editor.commit();

		return true;
	}
	public static String getZone(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String value = mPrefrence.getString(PORTSIP_ZONE, "11111");

		return value;
	}
	public static boolean setZone(Context context,boolean[] value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();
    String str;
		str="";
		for(int i=0;i<5;i++)
		{
				if(value[i])
				{
						str += '1';
				}
				else
				{
						str += '0';
				}
	 }

		editor.putString(PORTSIP_ZONE, str);

    editor.commit();

		return true;
	}
	public static int setDND(Context context,boolean value){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_DND, value);

    editor.commit();

		return 0;
	}
	public static boolean setLockPasswd(Context context,String val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_LOCKPASSWD, val);

    editor.commit();

		return true;
	}
	public static String getLockPasswd(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String value = mPrefrence.getString(PORTSIP_LOCKPASSWD, "0001");

		return value;
	}
	public static boolean getDND(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean value = mPrefrence.getBoolean(PORTSIP_DND, false);

		return value;
	}
	public static boolean getLock(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		boolean value = mPrefrence.getBoolean(PORTSIP_LOCK, false);

		return value;
	}
	public static boolean setLock(Context context,boolean val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putBoolean(PORTSIP_LOCK, val);

    editor.commit();

		return true;

	}
	public static int setMIC(Context context,int val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putInt(PORTSIP_MIC, val);

    editor.commit();

		return 0;
	}
	public static int setVolume(Context context,int val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putInt(PORTSIP_VOLUME, val);

    editor.commit();

		return 0;
	}

	public static int getVolume(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		int val = mPrefrence.getInt(PORTSIP_VOLUME, 2);

		return val;
	}
	public static int getMIC(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		int val = mPrefrence.getInt(PORTSIP_MIC, 2);

		return val;
	}

	public static int setRecord(Context context,String val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_JILU, val);

    editor.commit();

		return 0;
	}
	public static String getRecord(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String val = mPrefrence.getString(PORTSIP_JILU,"");

		return val;
	}

	public static int setContacts(Context context,String val){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		Editor editor = mPrefrence.edit();

		editor.putString(PORTSIP_CONTACTS, val);

    editor.commit();

		return 0;
	}
	public static String getContacts(Context context){
		SharedPreferences mPrefrence = context.getSharedPreferences(PRE_NAME,Context.MODE_PRIVATE);

		String val = mPrefrence.getString(PORTSIP_CONTACTS,"");

		return val;
	}

}
