
package com.weema.sipsample.util;

public class TongXunJiLu {

    /** 作用:1已接 2:未接 */
    private boolean isJieTong;
    private String  phonenum;
    private String  name;
    private String  riqi;
    private String  shijian;

    private boolean  isVideo;
    private boolean  isOutgoing;

    public boolean getVideo() {
		return isVideo;
	}
  public TongXunJiLu()
  {
      isJieTong = false;
      phonenum = " ";
      name = " ";
      riqi = " ";
      shijian = " ";
      isVideo = false;
      isOutgoing = true;
  }
	public void setVideo(boolean isVideo) {
		this.isVideo = isVideo;
	}

  public boolean getOutgoing() {
  return isOutgoing;
}

public void setOutgoing(boolean isOutgoing) {
  this.isOutgoing = isOutgoing;
}

    public boolean isJieTong() {
		return isJieTong;
	}

	public void setJieTong(boolean isJieTong) {
		this.isJieTong = isJieTong;
	}

	public String getPhonenum() {
		return phonenum;
	}

	public void setPhonenum(String phonenum) {
		this.phonenum = phonenum;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRiqi() {
		return riqi;
	}

	public void setRiqi(String riqi) {
		this.riqi = riqi;
	}

	public String getShijian() {
		return shijian;
	}

	public void setShijian(String shijian) {
		this.shijian = shijian;
	}

//	public TongXunLu(String phonenum, String laiQu, String riqi, String shijian) {
//
//        this.phonenum = phonenum;
//        this.name = laiQu;
//        this.riqi = riqi;
//        this.shijian = shijian;
//        if(laiQu.equals("撥出")){
//        	isJieTong = true;
//        }
//
//    }

}
