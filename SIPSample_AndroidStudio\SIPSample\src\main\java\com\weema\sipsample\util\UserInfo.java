package com.weema.sipsample.util;

import com.portsip.PortSipEnumDefine;

public class UserInfo {
	private String mUsername = "";
	private String mUserPwd= "";
	private String mSipServer= "";
	private String mUserDomain= "";
	private String mAuthName= "";
	private String mUserDisName= "";
	private String mStunSvr= "";

	private int mStunPort= 5060;
	private int mSipServerPort= 5060;
	private int mTransType= PortSipEnumDefine.ENUM_TRANSPORT_UDP;
	private int mSrtpType= PortSipEnumDefine.ENUM_SRTPPOLICY_NONE;

	public void setUserName(String userName){
		mUsername =userName;
	}

	public String getUserName() {
		return mUsername;
	}

	public void setUserPassword(String password){
		mUserPwd =password;
	}

	public String getUserPassword() {
		return mUserPwd;
	}

	public void setSipServer(String server){
		mSipServer =server;
	}

	public String getSipServer() {
		return mSipServer;
	}

	public void setUserDisplayName(String dispalyName){
		mUserDisName =dispalyName;
	}

	public String getUserDisplayName() {
		return mUserDisName;
	}

	public void setUserDomain(String domain){
		mUserDomain =domain;
	}
	public String getUserDomain() {
		return mUserDomain;
	}

	public void setAuthName(String authName){
		mAuthName = authName;
	}
	public String getAuthName() {
		return mAuthName;
	}

	public void setSipPort(int port){
		mSipServerPort = port;
	}

	public int getSipPort(){
		return mSipServerPort;
	}

	public void setStunServer(String stunServer){
		mStunSvr = stunServer;
	}

	public String getStunServer(){
		return mStunSvr;
	}

	public void setStunPort(int port){
		mStunPort = port;
	}

	public int getStunPort(){
		return mStunPort;
	}

	public void setTranType(int enum_transType){
		mTransType = enum_transType;
	}

	public int getTransType(){
		return mTransType;
	}

	public void setSrtpType(int enum_srtpType){
		mSrtpType = enum_srtpType;
	}

	public int getSrtpType(){
		return mSrtpType;
	}


	public boolean isAvailable(){

		if (mUsername != null && mUsername.length() > 0 &&
				mUserPwd!= null	&& mUserPwd.length() > 0 &&
				mSipServerPort>0&&mSipServerPort<65535&&
				mSipServer!= null&&mSipServer.length() > 0)// these fields are required
		{
			return true;
		}
		return false;
	}

}
