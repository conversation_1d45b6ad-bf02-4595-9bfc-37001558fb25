package com.weema.sipsample.util;



public class Utility {

 	public static  String getRemoteCaller(String caller)
	{
		int index = caller.indexOf("@");
	
		if(index <= 0)    return caller; 
	
		String msg = caller.substring(0,index);
		msg = msg.replace("sip:","");
	
		return msg;
			  
	}			
    public static  int getPhoneNumber(String caller)
	{
        String str = getRemoteCaller(caller);
		int number = getNumber(str);

		return number;
			  
	}	

    public static int getNumber(String str)
	{
		int number = 0;

		try{
			number = Integer.parseInt(str);
		}catch(NumberFormatException ex)
		{

		}
		return number;
	}		
}