<?xml version="1.0" encoding="UTF-8"?>
<inset android:insetLeft="1.0px" android:insetRight="1.0px" android:insetTop="0.0px" android:insetBottom="1.0px"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <selector>
        <item android:state_pressed="true">
            <shape>
                <gradient android:startColor="@color/rounded_container_bg" android:endColor="@color/rounded_container_bg" android:angle="270.0" />
                <corners android:radius="11.0dip" />
            </shape>
        </item>
        <item>
            <shape>
            	<stroke android:width="1.0px" android:color="@color/rounded_container_border" />
                <gradient android:startColor="@color/rounded_container_bg" android:endColor="@color/rounded_container_bg" android:angle="270.0" />
                <corners android:radius="10.0dip" />
 
            </shape>
        </item>
    </selector>
</inset>