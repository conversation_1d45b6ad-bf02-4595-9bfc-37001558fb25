<?xml version="1.0" encoding="utf-8"?>
<rotate xmlns:android="http://schemas.android.com/apk/res/android"
    android:pivotX="50%" android:pivotY="50%" android:fromDegrees="15"
    android:toDegrees="360">

    <shape xmlns:android="http://schemas.android.com/apk/res/android"
        android:shape="oval"
        android:useLevel="true">
        <size
            android:width="20dp"
            android:height="20dp" />
        <stroke
            android:width="4dp"
            android:color="@android:color/white"
            android:dashGap="4dp"
            android:dashWidth="1dp"/>
    </shape>
</rotate>