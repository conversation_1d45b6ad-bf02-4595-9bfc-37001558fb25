<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:layout_gravity="center"
    android:padding="10dp" >

    <LinearLayout
        android:id="@+id/alarm_dialArea1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:weightSum="10"
         >

        <TextView
            android:id="@+id/bc_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop = "100dp"
            android:text="廣播訊息"
            android:textSize="30sp"
            android:layout_weight="1"
           />
        
        <TextView
            android:id="@+id/bc_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="9"
            android:text="環保鈴聲"
            android:textSize="20sp"/>
        
        <ImageButton
        android:id="@+id/bc_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        
        android:background="@drawable/number_ok" />
 
    </LinearLayout>

  
</RelativeLayout>