<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"

    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background"
    tools:context="com.weema.app.app6.LoginActivity">

    <RelativeLayout
        android:id="@+id/login_local_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="30dp"
        >

        <CheckBox
            android:id="@+id/login_local"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_marginLeft="10dp"
            android:layout_marginStart="10dp"
            android:text="@string/str_login_local"
             />

        <RelativeLayout

            android:layout_width="400dp"
            android:layout_height="wrap_content"

            android:layout_marginLeft="10dp"
            android:layout_marginTop="50dp"
            android:orientation="vertical">


            <TextView
                android:id="@+id/login_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/editText_login_passwd"
                android:layout_alignParentLeft="true"

                android:layout_marginLeft="10dp"

                android:text="@string/str_login_username"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <ImageButton
                android:id="@+id/login_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:layout_centerHorizontal="true"
                android:layout_below="@+id/outbound_port"
                android:background="@drawable/confirm_3" />


            <EditText
                android:id="@+id/editText_login_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_marginLeft="25dp"

                android:layout_toEndOf="@+id/outbound_port"
                android:layout_toRightOf="@+id/outbound_port"
                android:ems="10"
                android:inputType="textPersonName"
                android:text="123" />

            <EditText
                android:id="@+id/editText_login_passwd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/editText_login_name"
                android:layout_alignStart="@+id/editText_login_name"
                android:layout_below="@+id/editText_login_name"
                android:layout_marginTop="4dp"
                android:ems="10"
                android:inputType="textPersonName"
                android:text="123" />

            <EditText
                android:id="@+id/editText_login_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/editText_login_name"
                android:layout_alignStart="@+id/editText_login_name"
                android:layout_below="@+id/editText_login_passwd"
                android:layout_marginTop="4dp"
                android:ems="10"
                android:inputType="textPostalAddress"

            />

            <EditText
                android:id="@+id/editText_domain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/editText_login_name"
                android:layout_alignStart="@+id/editText_login_name"
                android:layout_below="@+id/editText_login_port"
                android:layout_marginTop="4dp"
                android:ems="10"
                android:inputType="textPostalAddress"

            />

            <TextView
                android:id="@+id/login_passwd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/editText_login_passwd"
                android:layout_alignLeft="@+id/login_name"
                android:layout_alignStart="@+id/login_name"
                android:text="@string/str_passwd"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/login_port"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/editText_login_port"
                android:layout_alignLeft="@+id/login_name"
                android:layout_alignStart="@+id/login_name"
                android:text="@string/str_port"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/outbound_port"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:layout_alignBottom="@+id/editText_outbound_port"
                android:layout_alignLeft="@+id/login_name"
                android:layout_alignStart="@+id/login_name"
                android:text="@string/str_port_outbound"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <EditText
                android:id="@+id/editText_login_port"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/editText_login_name"
                android:layout_alignStart="@+id/editText_login_name"
                android:layout_below="@+id/editText_login_ip"
                android:layout_marginTop="5dp"
                android:ems="10"
                android:inputType="numberDecimal"

            />

            <EditText
                android:id="@+id/editText_outbound_port"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:layout_alignLeft="@+id/editText_login_name"
                android:layout_alignStart="@+id/editText_login_name"
                android:layout_below="@+id/editText_domain"
                android:layout_marginTop="5dp"
                android:ems="10"
                android:inputType="numberDecimal"

            />

            <TextView
                android:id="@+id/login_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/editText_login_ip"
                android:layout_alignLeft="@+id/login_passwd"
                android:layout_alignStart="@+id/login_passwd"
                android:text="@string/str_serverip"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/domain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/editText_domain"
                android:layout_alignLeft="@+id/login_passwd"
                android:layout_alignStart="@+id/login_passwd"
                android:text="@string/str_domain"
                android:textAppearance="?android:attr/textAppearanceLarge" />

        </RelativeLayout>
    </RelativeLayout>

<RelativeLayout
  android:layout_width="wrap_content"
  android:layout_height="wrap_content"
  android:id="@+id/login_save_layout"
  android:layout_toRightOf="@+id/login_local_layout"
  android:layout_marginTop="30dp"
  >

  <CheckBox
      android:id="@+id/login_save2"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"

      android:layout_alignParentLeft="true"
      android:layout_alignParentStart="true"
      android:layout_marginLeft="10dp"
      android:layout_marginStart="10dp"
      android:text="@string/str_login_save"
      android:textAppearance="?android:attr/textAppearanceLarge" />

  <RelativeLayout

      android:layout_width="400dp"
      android:layout_height="wrap_content"

      android:layout_marginTop="50dp"
      android:orientation="vertical" >


  <TextView
      android:id="@+id/login_name2"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_above="@+id/editText_login_passwd2"
      android:layout_alignParentLeft="true"
      android:layout_alignParentStart="false"
      android:layout_marginLeft="10dp"

      android:text="@string/str_login_username"
      android:textAppearance="?android:attr/textAppearanceLarge" />

  <ImageButton
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:background="@drawable/confirm_3"
      android:id="@+id/login_btn2"
      android:layout_below="@+id/outbound_port2"
      android:layout_centerHorizontal="true" />


      <EditText
          android:id="@+id/editText_login_name2"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_alignParentTop="true"
          android:layout_marginLeft="25dp"

          android:layout_toEndOf="@+id/outbound_port2"
          android:layout_toRightOf="@+id/outbound_port2"
          android:ems="10"
          android:inputType="textPersonName"
          android:text="123" />

  <EditText
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:inputType="textPersonName"
      android:text="123"
      android:ems="10"
      android:id="@+id/editText_login_passwd2"
      android:layout_below="@+id/editText_login_name2"
      android:layout_marginTop="4dp"
      android:layout_alignLeft="@+id/editText_login_name2"
      android:layout_alignStart="@+id/editText_login_name2"/>

  <EditText
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:inputType="textPostalAddress"
      android:ems="10"
      android:id="@+id/editText_login_ip2"
      android:layout_below="@+id/editText_login_passwd2"
      android:layout_marginTop="4dp"
      android:layout_alignLeft="@+id/editText_login_name2"
      android:layout_alignStart="@+id/editText_login_name2"

       />

       <EditText
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:inputType="textPostalAddress"
           android:ems="10"
           android:id="@+id/editText_domain2"
           android:layout_below="@+id/editText_login_port2"
           android:layout_marginTop="4dp"
           android:layout_alignLeft="@+id/editText_login_name2"
           android:layout_alignStart="@+id/editText_login_name2"

            />

  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:textAppearance="?android:attr/textAppearanceLarge"
      android:text="@string/str_passwd"
      android:id="@+id/login_passwd2"
      android:layout_alignBottom="@+id/editText_login_passwd2"
      android:layout_alignLeft="@+id/login_name2"
      android:layout_alignStart="@+id/login_name2" />

  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:textAppearance="?android:attr/textAppearanceLarge"
      android:text="@string/str_port"
      android:id="@+id/login_port2"
      android:layout_alignBottom="@+id/editText_login_port2"
      android:layout_alignLeft="@+id/login_name2"
      android:layout_alignStart="@+id/login_name2" />

      <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textAppearance="?android:attr/textAppearanceLarge"
          android:text="@string/str_port_outbound"
          android:id="@+id/outbound_port2"

          android:layout_alignBottom="@+id/editText_outbound_port2"
          android:layout_alignLeft="@+id/login_name2"
          android:layout_alignStart="@+id/login_name2" />

  <EditText
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:inputType="numberDecimal"
      android:ems="10"
      android:id="@+id/editText_login_port2"
      android:layout_below="@+id/editText_login_ip2"
      android:layout_marginTop="5dp"
      android:layout_alignLeft="@+id/editText_login_name2"
      android:layout_alignStart="@+id/editText_login_name2"

       />

       <EditText
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:inputType="numberDecimal"
           android:ems="10"
           android:id="@+id/editText_outbound_port2"
           android:layout_below="@+id/editText_domain2"

           android:layout_marginTop="5dp"
           android:layout_alignLeft="@+id/editText_login_name2"
           android:layout_alignStart="@+id/editText_login_name2"

            />

  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:textAppearance="?android:attr/textAppearanceLarge"
      android:text="@string/str_serverip"
      android:id="@+id/login_ip2"
      android:layout_alignBottom="@+id/editText_login_ip2"
      android:layout_alignLeft="@+id/login_passwd2"
      android:layout_alignStart="@+id/login_passwd2" />

      <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textAppearance="?android:attr/textAppearanceLarge"
          android:text="@string/str_domain"
          android:id="@+id/doamin2"
          android:layout_alignBottom="@+id/editText_domain2"
          android:layout_alignLeft="@+id/login_passwd2"
          android:layout_alignStart="@+id/login_passwd2" />

</RelativeLayout>
</RelativeLayout>

<RelativeLayout
  android:layout_width="wrap_content"
  android:layout_height="wrap_content"
  android:id="@+id/login_save3_layout"
  android:layout_toRightOf="@+id/login_save_layout"
  android:layout_marginTop="30dp"
  >

  <CheckBox
      android:id="@+id/login_save3"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"

      android:layout_alignParentLeft="true"
      android:layout_alignParentStart="true"
      android:layout_marginLeft="10dp"
      android:layout_marginStart="10dp"
      android:text="@string/str_login_save2"
      android:textAppearance="?android:attr/textAppearanceLarge" />

  <RelativeLayout

      android:layout_width="400dp"
      android:layout_height="wrap_content"

      android:layout_marginTop="50dp"
      android:orientation="vertical" >


  <TextView
      android:id="@+id/login_name3"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_above="@+id/editText_login_passwd3"
      android:layout_alignParentLeft="true"
      android:layout_alignParentStart="false"
      android:layout_marginLeft="10dp"

      android:text="@string/str_login_username"
      android:textAppearance="?android:attr/textAppearanceLarge" />

  <ImageButton
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:background="@drawable/confirm_3"
      android:id="@+id/login_btn3"
      android:layout_below="@+id/outbound_port3"
      android:layout_centerHorizontal="true" />


      <EditText
          android:id="@+id/editText_login_name3"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_alignParentTop="true"
          android:layout_marginLeft="25dp"

          android:layout_toEndOf="@+id/outbound_port3"
          android:layout_toRightOf="@+id/outbound_port3"
          android:ems="10"
          android:inputType="textPersonName"
          android:text="123" />

  <EditText
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:inputType="textPersonName"
      android:text="123"
      android:ems="10"
      android:id="@+id/editText_login_passwd3"
      android:layout_below="@+id/editText_login_name3"
      android:layout_marginTop="4dp"
      android:layout_alignLeft="@+id/editText_login_name3"
      android:layout_alignStart="@+id/editText_login_name3"/>

  <EditText
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:inputType="textPostalAddress"
      android:ems="10"
      android:id="@+id/editText_login_ip3"
      android:layout_below="@+id/editText_login_passwd3"
      android:layout_marginTop="4dp"
      android:layout_alignLeft="@+id/editText_login_name3"
      android:layout_alignStart="@+id/editText_login_name3"

       />

       <EditText
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:inputType="textPostalAddress"
           android:ems="10"
           android:id="@+id/editText_domain3"
           android:layout_below="@+id/editText_login_port3"
           android:layout_marginTop="4dp"
           android:layout_alignLeft="@+id/editText_login_name3"
           android:layout_alignStart="@+id/editText_login_name3"

            />

  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:textAppearance="?android:attr/textAppearanceLarge"
      android:text="@string/str_passwd"
      android:id="@+id/login_passwd3"
      android:layout_alignBottom="@+id/editText_login_passwd3"
      android:layout_alignLeft="@+id/login_name3"
      android:layout_alignStart="@+id/login_name3" />

  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:textAppearance="?android:attr/textAppearanceLarge"
      android:text="@string/str_port"
      android:id="@+id/login_port3"
      android:layout_alignBottom="@+id/editText_login_port3"
      android:layout_alignLeft="@+id/login_name3"
      android:layout_alignStart="@+id/login_name3" />

      <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textAppearance="?android:attr/textAppearanceLarge"
          android:text="@string/str_port_outbound"

          android:id="@+id/outbound_port3"
          android:layout_alignBottom="@+id/editText_outbound_port3"
          android:layout_alignLeft="@+id/login_name3"
          android:layout_alignStart="@+id/login_name3" />

  <EditText
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:inputType="numberDecimal"
      android:ems="10"
      android:id="@+id/editText_login_port3"
      android:layout_below="@+id/editText_login_ip3"
      android:layout_marginTop="5dp"
      android:layout_alignLeft="@+id/editText_login_name3"
      android:layout_alignStart="@+id/editText_login_name3"

       />

       <EditText
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:inputType="numberDecimal"
           android:ems="10"
           android:id="@+id/editText_outbound_port3"
           android:layout_below="@+id/editText_domain3"

           android:layout_marginTop="5dp"
           android:layout_alignLeft="@+id/editText_login_name3"
           android:layout_alignStart="@+id/editText_login_name3"

            />

  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:textAppearance="?android:attr/textAppearanceLarge"
      android:text="@string/str_serverip"
      android:id="@+id/login_ip3"
      android:layout_alignBottom="@+id/editText_login_ip3"
      android:layout_alignLeft="@+id/login_passwd3"
      android:layout_alignStart="@+id/login_passwd3" />

      <TextView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textAppearance="?android:attr/textAppearanceLarge"
          android:text="@string/str_domain"
          android:id="@+id/domain3"
          android:layout_alignBottom="@+id/editText_domain3"
          android:layout_alignLeft="@+id/login_passwd3"
          android:layout_alignStart="@+id/login_passwd3" />

</RelativeLayout>
</RelativeLayout>

    <ImageButton
        android:id="@+id/back_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:layout_below="@+id/login_local_layout"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"

        android:layout_marginStart="374dp"
        android:layout_marginLeft="374dp"
        android:layout_marginTop="72dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/back_3" />

</RelativeLayout>
