<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:layout_gravity="center"
    android:padding="20dp" >

    <LinearLayout
        android:id="@+id/alarm_dialArea1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="60dp"
         >
        
        <TextView
            android:id="@+id/mail_text1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            
            android:text="1234"
            android:textSize="20sp"/>
        
        <TextView
            android:id="@+id/mail_text2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
        
            android:text="1234"
            android:textSize="20sp"/>

        <TextView
            android:id="@+id/mail_text3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
        
            android:text="1234"
            android:textSize="20sp"/>
        
        <TextView
            android:id="@+id/mail_text4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
        
            android:text="1234"
            android:textSize="20sp"/>

        <TextView
            android:id="@+id/mail_text5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            
            android:text="1234"
            android:textSize="20sp"/>
        
        <TextView
            android:id="@+id/mail_text6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
        
            android:text="1234"
            android:textSize="20sp"/>

        <TextView
            android:id="@+id/mail_text7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            
            android:text="1234"
            android:textSize="20sp"/>
        
        <TextView
            android:id="@+id/mail_text8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
        
            android:text="1234"
            android:textSize="20sp"/>

        <TextView
            android:id="@+id/mail_text9"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            
            android:text="1234"
            android:textSize="20sp"/>
        
        <TextView
            android:id="@+id/mail_text10"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
        
            android:text="1234"
            android:textSize="20sp"/>

        <TextView
            android:id="@+id/mail_text11"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            
            android:text="1234"
            android:textSize="20sp"/>
        
      
        <LinearLayout
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
        >

        <EditText
            android:id="@+id/EditTextReception"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
        
            android:background="@drawable/dialtextview1"
            android:enabled="false"
            android:gravity="center"
            android:textStyle="bold"
            android:textSize="80sp"
            android:textColor="#ffffffff"
            android:paddingLeft="10dp"
            android:paddingRight="10dp" />

        </LinearLayout>

      
       <LinearLayout
            android:gravity="right"
            
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="1dp"
        >
       <TextView
            android:id="@+id/mail_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
        
            android:text=""
            android:textSize="18sp"
            
           />

       <TextView
            android:id="@+id/mail_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            
            android:text=""
            android:textSize="18sp"
            
           />           
       <TextView
            android:id="@+id/mail_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
        
            android:text=""
            android:textSize="18sp"
            
           />


           </LinearLayout>        
    </LinearLayout>

  
</RelativeLayout>