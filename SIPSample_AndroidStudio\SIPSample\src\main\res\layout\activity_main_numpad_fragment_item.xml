<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
	android:layout_height="wrap_content"
	android:orientation="horizontal"
    android:gravity="center_vertical"
    android:minHeight="@dimen/contactphone_item_height"
	android:background="@android:color/white">
    <TextView
        android:id="@+id/activity_main_numpad_fragment_contact_name"
        android:lines="1"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="15dp"
        android:gravity="bottom"
        android:textSize="@dimen/activity_main_fragment_dial_textsize_contactname"
        android:text="dsaffffffff"
        android:textColor="@color/portgo_color_black"/>
    <RelativeLayout
        android:orientation="horizontal"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/activity_main_numpad_fragment_phone_number"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"

            android:gravity="right"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lines="1"
            android:text=""
            android:textColor="@color/portgo_color_light_black"
            android:textSize="@dimen/activity_main_fragment_dial_textsize_phonenumber"/>
        <TextView
            android:visibility="gone"
            android:lines="1"
            android:id="@+id/activity_main_numpad_fragment_phone_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="dsaffff"
            android:textColor="@color/portgo_color_light_black"
            android:textSize="@dimen/activity_main_fragment_dial_textsize_phonenumber"/>

    </RelativeLayout>
</LinearLayout>