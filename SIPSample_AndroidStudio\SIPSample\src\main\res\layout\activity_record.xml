<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextureView
        android:id="@+id/textureView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <Button
        android:id="@+id/btnTakePicture"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:text="播號"
        android:textSize="20sp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:layout_marginBottom="15dp"
        android:layout_marginLeft="20dp"/>

    <Button
        android:id="@+id/btnRecordingVideo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:text="錄影"
        android:textSize="20sp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:layout_marginBottom="15dp"
        android:layout_marginRight="20dp"/>

</RelativeLayout>
