<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/bg2"
    android:orientation="vertical"
    android:paddingBottom="30dp"
    android:paddingLeft="50dp"
    android:paddingRight="50dp"
    android:paddingTop="30dp" >

        <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

    <Button
        android:id="@+id/setting_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="5dp"
        android:textSize="18sp"
        android:text="@string/str_prev" />

     <TextView
        android:id="@+id/setting_text"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="VERSION 1.0"
        android:textColor="#ff000000"
        android:textSize="18sp"
        android:clickable="false"
        />

     </LinearLayout>

   

</LinearLayout>
