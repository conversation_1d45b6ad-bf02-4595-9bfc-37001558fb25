<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:id="@+id/myDial"
    android:padding="10dp" >

    <LinearLayout
        android:id="@+id/dialArea1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop = "10dp"
        android:layout_marginLeft = "30dp"
        android:orientation="horizontal" >

        <LinearLayout
            android:id="@+id/dialArea011"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30dp"
            android:orientation="vertical" >

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >

                <EditText
                    android:id="@+id/new_editText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginTop="30dp"
                    android:background="@drawable/dialtextview"
                    android:focusable="false"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:textSize="20sp"
                    android:text="@string/str_new_passwd"
                    android:textColor="#ffffffff"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/dialButton_audio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/audio_1" />

                <ImageButton
                    android:id="@+id/dialButton_video"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/video_1" />

                <ImageButton
                    android:id="@+id/dialButton_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/backs" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/dialButton_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/number_1" />

                <ImageButton
                    android:id="@+id/dialButton_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/number_2" />

                <ImageButton
                    android:id="@+id/dialButton_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/number_3" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/dialButton_4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/number_4" />

                <ImageButton
                    android:id="@+id/dialButton_5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/number_5" />

                <ImageButton
                    android:id="@+id/dialButton_6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/number_6" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/dialButton_7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/number_7" />

                <ImageButton
                    android:id="@+id/dialButton_8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/number_8" />

                <ImageButton
                    android:id="@+id/dialButton_9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/number_9" />
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/dialButton_xing"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/number_xing" />

                <ImageButton
                    android:id="@+id/dialButton_0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/number_0" />

                <ImageButton
                    android:id="@+id/dialButton_jing"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/number_jing" />
            </RelativeLayout>


        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
