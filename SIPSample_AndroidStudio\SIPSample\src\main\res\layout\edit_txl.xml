<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/bg2"
    android:padding="100dp" >

    <RelativeLayout
        android:id="@+id/temp_12"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" >

        <Button
            android:id="@+id/no"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:text="@string/str_cancel"
            android:textSize="34sp" />

        <Button
            android:id="@+id/yes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:layout_toLeftOf="@id/no"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:text="@string/str_ok"
            android:textSize="34sp" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_above="@id/temp_12"
        android:orientation="vertical" >

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:orientation="horizontal" >

            <TextView
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/str_name"
                android:textSize="30sp" />

            <EditText
                android:id="@+id/edit_1"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_weight="0.5"
                android:inputType="text"
                android:maxLength="8"
                android:paddingLeft="10dp"
                android:textSize="30sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:orientation="horizontal" >

            <TextView
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/str_phone_number"
                android:textSize="30sp" />

            <EditText
                android:id="@+id/edit_2"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_weight="0.5"
                android:digits="0123456789"
                android:inputType="phone"
                android:maxLength="12"
                android:paddingLeft="10dp"
                android:textSize="30sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:orientation="horizontal" >

            <TextView
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/str_note"
                android:textSize="30sp" />

            <EditText
                android:id="@+id/edit_3"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_weight="0.5"
                android:inputType="text"
                android:maxLength="20"
                android:paddingLeft="10dp"
                android:textSize="30sp" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>