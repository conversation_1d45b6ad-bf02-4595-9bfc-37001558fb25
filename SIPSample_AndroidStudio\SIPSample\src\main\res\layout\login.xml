<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:layout_gravity="center"
    android:background="@color/white"
    android:gravity="center_horizontal"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/headline"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_margin="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/textView4"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/sipsample"
            android:textColor="@color/black" />
        <TextView
            android:id="@+id/txtips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/unregister"
            android:textColor="@color/black" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/bottomline"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/activity_horizontal_margin">
            <Button
                android:id="@+id/btonline"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/online" />
            <Button
                android:id="@+id/btoffline"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/offline" />
        </LinearLayout>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/website"
            android:textColor="@color/black" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/email"
            android:textColor="@color/black" />
    </LinearLayout>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/bottomline"
        android:layout_below="@id/headline">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:text="@string/str_requried"
                android:textColor="@color/black"
                android:textSize="@dimen/labletxsize" />
            <TableLayout
                android:id="@+id/required"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TableRow
                    android:id="@+id/tableRow1"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_username"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etusername"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:singleLine="true"
                        android:text="" />
                </TableRow>
                <TableRow
                    android:id="@+id/tableRow2"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_password"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etpwd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:inputType="textPassword"
                        android:singleLine="true"
                        android:text="" />
                </TableRow>
                <TableRow
                    android:id="@+id/tableRow3"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_SIPServer"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etsipsrv"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:singleLine="true"
                        android:text="" />
                </TableRow>
                <TableRow
                    android:id="@+id/tableRow4"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_SIPServerPort"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etsipport"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:inputType="number"
                        android:singleLine="true"
                        android:text="5060" />
                </TableRow>
            </TableLayout>
            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/activity_horizontal_margin"
                android:orientation="horizontal"
                android:weightSum="4">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/str_transport"
                    android:textColor="@color/black" />
                <Spinner
                    android:id="@+id/spTransport"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:text="@string/str_transport" />
            </LinearLayout>
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:text="@string/str_optional"
                android:textColor="@color/black"
                android:textSize="@dimen/labletxsize" />
            <TableLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible">
                <TableRow
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_displayname"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etdisplayname"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:singleLine="true" />
                </TableRow>
                <TableRow
                    android:id="@+id/tableRow6"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_userDomain"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etuserdomain"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:singleLine="true" />
                </TableRow>
                <TableRow
                    android:id="@+id/TableRow7"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_authName"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etauthName"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:singleLine="true" />
                </TableRow>
                <TableRow
                    android:id="@+id/tableRow8"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/stunsrv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_StunServer"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etStunServer"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:singleLine="true" />
                </TableRow>
                <TableRow
                    android:id="@+id/TableRow9"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/StunServerPort"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_StunServerPort"
                        android:textColor="@color/black" />
                    <EditText
                        android:id="@+id/etStunPort"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:ems="10"
                        android:singleLine="true" />
                </TableRow>
            </TableLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/activity_horizontal_margin"
                android:orientation="horizontal"
                android:weightSum="4">
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/str_srtp"
                    android:textColor="@color/black" />
                <Spinner
                    android:id="@+id/spSRTP"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:text="@string/str_srtp" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</RelativeLayout>