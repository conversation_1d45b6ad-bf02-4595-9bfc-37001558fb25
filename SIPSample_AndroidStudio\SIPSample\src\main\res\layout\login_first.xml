<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <EditText
            android:id="@+id/editText_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="27dp"
            android:layout_marginStart="27dp"
            android:layout_marginTop="91dp"
            android:ems="10"
            android:hint="帳號"
            android:inputType="textMultiLine"
            android:text="" />

        <EditText
            android:id="@+id/editText_passwd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/editText_name"
            android:layout_alignStart="@+id/editText_name"
            android:layout_below="@+id/editText_name"
            android:ems="10"
            android:hint="密碼"
            android:inputType="textPersonName"
            android:text="" />

        <EditText
            android:id="@+id/editText_domain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/editText_passwd"
            android:layout_alignStart="@+id/editText_passwd"
            android:layout_below="@+id/editText_passwd"
            android:layout_marginTop="9dp"
            android:ems="10"
            android:hint="domain"
            android:inputType="textPersonName"
            android:text="" />

        <EditText
            android:id="@+id/editText_ip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@+id/editText_port"
            android:layout_alignLeft="@+id/editText_domain"
            android:layout_alignStart="@+id/editText_domain"
            android:layout_marginBottom="57dp"
            android:ems="10"
            android:hint="server ip"
            android:inputType="textPersonName"
            android:text="" />

        <EditText
            android:id="@+id/editText_port"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/editText_ip"
            android:layout_alignStart="@+id/editText_ip"
            android:layout_below="@+id/editText_domain"
            android:layout_marginTop="42dp"
            android:ems="10"
            android:hint="port"
            android:inputType="textPersonName"
            android:text="" />

        <Button
            android:id="@+id/btn_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/editText_port"
            android:layout_alignStart="@+id/editText_port"
            android:layout_below="@+id/editText_port"
            android:layout_marginTop="21dp"
            android:text="送出" />
    </RelativeLayout>
</LinearLayout>
