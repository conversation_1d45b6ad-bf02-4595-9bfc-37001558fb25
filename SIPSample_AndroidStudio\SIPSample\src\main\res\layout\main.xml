<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">
		<RadioGroup
       android:id="@+id/tab_menu"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="#ffffff"
        android:orientation="horizontal">        
			
			<RadioButton
				android:id="@+id/activity_main_tab_contacts"
				style="@style/activity_main_tab"
				android:drawableTop="@drawable/tab_contacts_selector"
				android:text="@string/portgo_title_contact"
				/>
			<RadioButton
				android:id="@+id/activity_main_tab_history"
				style="@style/activity_main_tab_message"
				android:drawableTop="@drawable/tab_history_selector"
				android:text="@string/portgo_title_history"
				 />
			<RadioButton
				android:id="@+id/activity_main_tab_numbers"
				style="@style/activity_main_tab"
				android:drawableTop="@drawable/tab_pad_selector"
				android:checked="true"
				android:text="@string/portgo_title_numberpad"/>
			<RadioButton
				android:id="@+id/activity_main_tab_message"
				style="@style/activity_main_tab_message"
				android:drawableTop="@drawable/tab_message_selector"
				android:text="@string/portgo_title_message"/>
			<RadioButton
				android:id="@+id/activity_main_tab_setting"
				style="@style/activity_main_tab"
				android:drawableTop="@drawable/tab_setting_selector"
				android:text="@string/portgo_title_setting"/>
		</RadioGroup>
        <!--
    <RadioGroup
        android:id="@+id/tab_menu"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="#000000"
        android:orientation="horizontal">
        <RadioButton
            android:id="@+id/tab_login"
            style="@style/radio_tab"
            android:text="@string/str_login" />
        <RadioButton
            android:id="@+id/tab_numpad"
            style="@style/radio_tab"
            android:text="@string/str_numpad" />
        <RadioButton
            android:id="@+id/tab_video"
            style="@style/radio_tab"
            android:text="@string/str_video" />
        <RadioButton
            android:id="@+id/tab_message"
            style="@style/radio_tab"
            android:text="@string/str_message" />
        <RadioButton
            android:id="@+id/tab_setting"
            style="@style/radio_tab"
            android:text="@string/str_setting" />
    </RadioGroup>
    -->
    <FrameLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/tab_menu" >


    </FrameLayout>
</RelativeLayout>
