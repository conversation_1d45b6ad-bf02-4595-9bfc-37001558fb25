<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/darkgray"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/messagelayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:layout_alignParentBottom="true"
        android:layout_margin="@dimen/activity_vertical_margin"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Messge"
            android:textColor="@color/black" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_sendto"
                android:textColor="@color/black" />
            <EditText
                android:id="@+id/etmsgdest"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:layout_weight="1"
                android:ems="10" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/message"
                android:textColor="@color/black" />
            <EditText
                android:id="@+id/etmessage"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:layout_weight="1"
                android:ems="10" />
            <Button
                android:id="@+id/btsendmsg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_sendmsg" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/presencelayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_above="@id/messagelayout"
        android:background="@color/white"
        android:layout_margin="@dimen/activity_vertical_margin"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Presence"
            android:textColor="@color/black" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/activity_vertical_margin"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/contact"
                    android:textColor="@color/black" />
                <EditText
                    android:id="@+id/etcontact"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="2dp"
                    android:layout_weight="2"
                    android:ems="10"
                    android:hint="sip:<EMAIL>:5060"
                    android:singleLine="true" />
                <ImageButton
                    android:id="@+id/btaddcontact"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@android:drawable/ic_input_add" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:orientation="horizontal">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_status"
                    android:textColor="@color/black" />
                <EditText
                    android:id="@+id/etstatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="2dp"
                    android:layout_weight="1"
                    android:ems="10" />
                <Button
                    android:id="@+id/btsendstatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_setstatus" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btsubscribe"
                    style="android:buttonBarStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/str_update" />
                <Button
                    android:id="@+id/btaccept"
                    style="android:buttonBarStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/str_accept" />
                <Button
                    android:id="@+id/btrefuse"
                    style="android:buttonBarStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/str_refuse" />
                <Button
                    android:id="@+id/btclear"
                    style="android:buttonBarStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/str_clear" />
            </LinearLayout>
        </LinearLayout>
        <ListView
            android:id="@+id/lvcontacs"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/activity_vertical_margin"
            android:choiceMode="singleChoice"
            android:cacheColorHint="@null" />
    </LinearLayout>
</RelativeLayout>