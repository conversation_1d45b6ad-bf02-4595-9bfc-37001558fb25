<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/bg2"
    android:orientation="vertical"
    android:paddingBottom="30dp"
    android:paddingLeft="50dp"
    android:paddingRight="50dp"
    android:paddingTop="30dp" >

 
    <Button
        android:id="@+id/opt_sos_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="100dp"
        android:textSize="32sp"
        android:text="@string/str_prev" />

    <ScrollView
        android:layout_width="fill_parent"
        android:layout_height="fill_parent" >

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:orientation="vertical"
            android:paddingLeft="30dp"
            android:paddingRight="30dp" >

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/opt_sos_start"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >

                    <CheckBox
                        android:id="@+id/opt_sos_checkB_state"
                        style="@style/vstyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:clickable = "false"
                        android:layout_marginRight="10dp" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_toLeftOf="@id/opt_sos_checkB_state"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_sos_start_title"
                            android:textColor="#ff000000"
                            android:textSize="28sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_sos_start_info"
                            android:textSize="24sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/opt_sos_set_unlock"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >
                    
 
                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_set_unlock_title"
                            android:textColor="#ff000000"
                            android:textSize="28sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_set_unlock_info"
                            android:textSize="24sp" />
                    </LinearLayout>
                    

        
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/opt_sos_unlock"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >
        
                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"         
                            android:text="@string/str_unlock_title"      
                            android:textColor="#ff000000"
                            android:textSize="28sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_unlock_info"
                            android:textSize="24sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/opt_sos_volume"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:visibility="invisible"
                    android:background="#00ffffff" >
                    
                    
                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_volume_title"
                            android:textColor="#ff000000"
                            android:textSize="28sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_volume_info"
                            android:textSize="24sp" />
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>


        </LinearLayout>
    </ScrollView>

</LinearLayout>