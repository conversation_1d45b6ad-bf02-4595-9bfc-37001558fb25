<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:orientation="vertical" >

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="150dp"
        android:layout_marginLeft="100dp"
        android:text="@string/str_setting_mdoor_title"
        android:textColor="#000000"
        android:textSize="36dp" />

    <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginLeft="100dp"
		android:layout_marginTop="20dp"
		>

      <CheckBox
            android:id="@+id/checkbox_is_mdoor"
            style="@style/mystyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_is_mdoor"
        android:textColor="#000000"
        android:textSize="36dp" />

    </LinearLayout>

    <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginLeft="100dp"
		android:layout_marginTop="20dp"
		>

            <CheckBox
                android:id="@+id/mdoor_checkB_1"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_phone_number"
        android:textColor="#000000"
        android:textSize="36dp" />

        <EditText android:layout_width="200dp"
        android:layout_height="50dp"
        android:id="@+id/mdoor_editText1"

        android:textSize="24sp"
       />


    </LinearLayout>

    <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginTop = "30dp"
		>

    <Button
        android:id="@+id/mdoor_button_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "100dp"
        android:textSize="30sp"
        android:text="@string/str_finish" />

    <Button
        android:id="@+id/mdoor_button_esc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "40dp"
        android:textSize="30sp"
        android:text="@string/str_prev" />

    </LinearLayout>


</LinearLayout>
