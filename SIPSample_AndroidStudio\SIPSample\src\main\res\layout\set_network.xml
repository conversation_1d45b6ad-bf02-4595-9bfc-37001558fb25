<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:orientation="vertical" >

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="50dp"
        android:layout_marginLeft="100dp"
        android:text="@string/str_network_title"
        android:textColor="#000000"
        android:textSize="36dp" />
        
        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="ip addr"
        android:textColor="#000000"
        android:textSize="36dp" />

        <EditText android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:id="@+id/ipaddr"
        android:inputType="number|numberDecimal"
        android:digits="0123456789."
        android:textSize="24sp"
       />

        </LinearLayout>     

        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="netmask"
        android:textColor="#000000"
        android:textSize="36dp" />

        <EditText android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:id="@+id/netmask"
        android:inputType="number|numberDecimal"
        android:digits="0123456789."
        android:textSize="24sp"
       />

        </LinearLayout>  
        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="gateway"
        android:textColor="#000000"
        android:textSize="36dp" />

        <EditText android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:id="@+id/gateway"
        android:inputType="number|numberDecimal"
        android:digits="0123456789."
        android:textSize="24sp"
       />

        </LinearLayout>        
        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="dns"
        android:textColor="#000000"
        android:textSize="36dp" />

        <EditText android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:id="@+id/dns"
        android:inputType="number|numberDecimal"
        android:digits="0123456789."
        android:textSize="24sp"
       />

        </LinearLayout>                                
                                  
    <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginTop = "30dp"
		>
    
    <Button
        android:id="@+id/ring_set_button_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "100dp"
        android:textSize="30sp"
        android:text="@string/str_finish" />

    <Button
        android:id="@+id/ring_set_button_esc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "40dp"
        android:textSize="30sp"
        android:text="@string/str_prev" />

    </LinearLayout>


</LinearLayout>
