<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:orientation="vertical" >

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="50dp"
        android:layout_marginLeft="100dp"
        android:text="@string/str_other_title"
        android:textColor="#000000"
        android:textSize="36dp" />

        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="錄影"
        android:textColor="#000000"
        android:textSize="36dp" />

        <CheckBox
            android:id="@+id/checkbox_is_enable"
            style="@style/mystyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />

        </LinearLayout>

        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="訪客通知"
        android:textColor="#000000"
        android:textSize="36dp" />

        <CheckBox
            android:id="@+id/checkbox_notify_enable"
            style="@style/mystyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />

        </LinearLayout>

        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="訪客錄影"
        android:textColor="#000000"
        android:textSize="36dp" />

        <CheckBox
            android:id="@+id/checkbox_record_enable"
            style="@style/mystyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />

        </LinearLayout>


        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="警報通話"
        android:textColor="#000000"
        android:textSize="36dp" />

        <CheckBox
            android:id="@+id/checkbox_sostalk_enable"
            style="@style/mystyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />

        </LinearLayout>    
        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="管理室強制通話開關"
        android:textColor="#000000"
        android:textSize="36dp" />

        <CheckBox
            android:id="@+id/checkbox_can_force_mgr_enable"
            style="@style/mystyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />

        </LinearLayout>    
        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="BZ"
        android:textColor="#000000"
        android:textSize="36dp" />

	<RadioGroup android:id="@+id/radGroup1"
	        android:orientation="horizontal"
		android:layout_below = "@+id/textView1_1"
		android:layout_marginTop = "20dp"
	        android:layout_width="wrap_content"
		android:layout_height="wrap_content" >

		<RadioButton android:id="@+id/bz_0"
			android:text="低"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/bz_1"
			android:text="正常"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/bz_2"
			android:text="高"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

	</RadioGroup>

        </LinearLayout>    
        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>

      <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="debug"
        android:textColor="#000000"
        android:textSize="36dp" />

        <CheckBox
            android:id="@+id/checkbox_debug_enable"
            style="@style/mystyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />

        </LinearLayout>                        
    <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginTop = "30dp"
		>
    
    <Button
        android:id="@+id/ring_set_button_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "100dp"
        android:textSize="30sp"
        android:text="@string/str_finish" />

    <Button
        android:id="@+id/ring_set_button_esc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "40dp"
        android:textSize="30sp"
        android:text="@string/str_prev" />

    </LinearLayout>


</LinearLayout>
