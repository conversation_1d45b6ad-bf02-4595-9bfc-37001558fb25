<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:orientation="vertical" 
    android:focusableInTouchMode="false">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="50dp"
        android:layout_marginLeft="100dp"
        android:text="@string/str_setting_ring_title"
        android:textColor="#000000"
        android:textSize="36dp" />

        <LinearLayout android:orientation="horizontal"
    		android:layout_height="wrap_content"
    		android:layout_width="wrap_content"
    		android:layout_marginLeft="100dp"
    		android:layout_marginTop="20dp"
    		>
        <CheckBox
            android:id="@+id/checkbox_is_enable"
            style="@style/mystyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />

        </LinearLayout>
    <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginLeft="100dp"
		android:layout_marginTop="20dp"
		>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_ipaddr"
        android:textColor="#000000"
        android:textSize="36dp" />

        <EditText android:layout_width="200dp"
        android:layout_height="50dp"
        android:id="@+id/ring_set_edit_ipaddr"

        android:textSize="24sp"
       />

    </LinearLayout>

    <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginLeft="100dp"
		android:layout_marginTop="20dp"
		>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_phone_ip"
        android:textColor="#000000"
        android:textSize="36dp" />

        <EditText android:layout_width="200dp"
        android:layout_height="50dp"
        android:id="@+id/ring_set_edit_phone_ipaddr"

        android:textSize="24sp"
       />


    </LinearLayout>

   <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginLeft="100dp"
		android:layout_marginTop="20dp"
		>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_ring_set"
        android:textColor="#000000"
        android:textSize="36dp" />

        <EditText android:layout_width="200dp"
        android:layout_height="50dp"
        android:id="@+id/ring_set_edit_number"

        android:textSize="24sp"
       />


    </LinearLayout>

    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:layout_marginRight="5dp"

            android:orientation="vertical"
            android:visibility="visible"
             >
               <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:text="@string/str_speaker_volume"
                android:textColor="@android:color/black"
                android:textSize="15sp"
                android:textStyle="bold" />

       <SeekBar
            android:id="@+id/volume_bar"
            android:layout_width="170dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"

            android:progress="0"
            android:max="4"
            android:progressDrawable="@drawable/progressbar"
            android:secondaryProgress="0" />
       </LinearLayout>


        <LinearLayout
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            android:visibility="visible"
            android:weightSum="10" >

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="1"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="2"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="3"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="4"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="5"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>

    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:layout_marginRight="5dp"

            android:orientation="vertical"
            android:visibility="visible"
             >
               <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:text="@string/str_mic_volume"
                android:textColor="@android:color/black"
                android:textSize="15sp"
                android:textStyle="bold" />

       <SeekBar
            android:id="@+id/volume_bar1"
            android:layout_width="170dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"

            android:progress="0"
            android:max="4"
            android:progressDrawable="@drawable/progressbar"
            android:secondaryProgress="0" />
       </LinearLayout>


        <LinearLayout
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            android:visibility="visible"
            android:weightSum="10" >

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="1"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="2"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="3"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="4"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="center_horizontal"
                android:text="5"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>
      
    <LinearLayout android:orientation="horizontal"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginTop = "30dp"
		>

    <Button
        android:id="@+id/ring_set_button_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "100dp"
        android:textSize="30sp"
        android:text="@string/str_get" />

    <Button
        android:id="@+id/ring_set_button_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "100dp"
        android:textSize="30sp"
        android:text="@string/str_finish" />

    <Button
        android:id="@+id/ring_set_button_esc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "40dp"
        android:textSize="30sp"
        android:text="@string/str_prev" />

    </LinearLayout>


</LinearLayout>
