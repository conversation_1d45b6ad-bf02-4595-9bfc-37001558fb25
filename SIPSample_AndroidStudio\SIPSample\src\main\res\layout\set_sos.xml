<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:orientation="vertical" >

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:layout_marginTop="150dp"
        android:layout_marginLeft="20dp"
        android:layout_marginBottom="20dp"
        android:text="@string/str_setting_sos"
        android:textColor="#000000"
        android:textSize="28dp" />

    <TextView
        android:id="@+id/textView0"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:layout_marginLeft="20dp"
        android:text="@string/str_sos_help"
        android:textColor="#FFA6FF"
        android:textSize="24dp" />

    <RelativeLayout android:orientation="horizontal"
		android:layout_height="match_parent"
		android:layout_width="match_parent"
		android:layout_marginLeft="20dp"
		>


    <RelativeLayout
                android:id="@+id/set_sos_layout1"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content" >

            <TextView
                    android:id="@+id/textView1_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_sos_number_1"
                    android:textColor="#000000"
                    android:textSize="24sp" />


	<RadioGroup android:id="@+id/radGroup1"
	        android:orientation="horizontal"
		android:layout_below = "@+id/textView1_1"
		android:layout_marginTop = "20dp"
	        android:layout_width="wrap_content"
		android:layout_height="wrap_content" >

		<RadioButton android:id="@+id/voip1_1"
			android:text="@string/str_voip1"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip2_1"
			android:text="@string/str_voip2"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip3_1"
			android:text="@string/str_voip3"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

	</RadioGroup>
            <TextView
                    android:id="@+id/textView2_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_dial_number"
                    android:layout_alignBottom="@+id/radGroup1"
		    android:layout_toRightOf = "@+id/radGroup1"
		    android:layout_marginLeft = "10dp"
                    android:textColor="#000000"
                    android:textSize="24sp" />

             <CheckBox
                android:id="@+id/checkB_enable1"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/textView2_1"
                android:layout_above="@+id/textView2_1"

                />


              <EditText android:layout_width="200dp"
              android:layout_height="50dp"
              android:id="@+id/editText1"

              android:layout_alignBottom="@+id/textView2_1"
	      android:layout_toRightOf = "@+id/textView2_1"
	      android:layout_centerHorizontal = "true"
	      android:textColor="#000000"
	      android:textSize="24sp"
	      android:background="@drawable/bg_edittext"
	      android:phoneNumber="true"
	      android:layout_marginLeft = "10dp"/>


    </RelativeLayout>

    <RelativeLayout
                android:id="@+id/set_sos_layout4"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginLeft="20dp"
		android:layout_toRightOf = "@+id/set_sos_layout1"
		android:layout_alignTop = "@+id/set_sos_layout1"
		>


            <TextView
                    android:id="@+id/textView1_4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_sos_number_4"
                    android:textColor="#000000"
                    android:textSize="24sp" />


	<RadioGroup android:id="@+id/radGroup4"
	        android:orientation="horizontal"
		android:layout_below = "@+id/textView1_4"
		android:layout_marginTop = "20dp"
	        android:layout_width="wrap_content"
		android:layout_height="wrap_content" >

		<RadioButton android:id="@+id/voip1_4"
			android:text="@string/str_voip1"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip2_4"
			android:text="@string/str_voip2"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip3_4"
			android:text="@string/str_voip3"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

	</RadioGroup>
            <TextView
                    android:id="@+id/textView2_4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_dial_number"
                    android:layout_alignBottom="@+id/radGroup4"
		    android:layout_toRightOf = "@+id/radGroup4"
		    android:layout_marginLeft = "10dp"
                    android:textColor="#000000"
                    android:textSize="24sp" />

             <CheckBox
                android:id="@+id/checkB_enable4"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/textView2_4"
                android:layout_above="@+id/textView2_4"

                />


              <EditText android:layout_width="200dp"
              android:layout_height="50dp"
              android:id="@+id/editText4"

              android:layout_alignBottom="@+id/textView2_4"
	      android:layout_toRightOf = "@+id/textView2_4"
	      android:layout_centerHorizontal = "true"
	      android:textColor="#000000"
	      android:textSize="24sp"
	      android:background="@drawable/bg_edittext"
	      android:phoneNumber="true"
	      android:layout_marginLeft = "10dp"/>


    </RelativeLayout>


        <RelativeLayout
                android:id="@+id/set_sos_layout2"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_below = "@+id/set_sos_layout1"
		android:layout_alignLeft = "@+id/set_sos_layout1"

		>

            <TextView
                    android:id="@+id/textView1_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_sos_number_2"
                    android:textColor="#000000"
                    android:textSize="24sp" />


	<RadioGroup android:id="@+id/radGroup2"
	        android:orientation="horizontal"
		android:layout_below = "@+id/textView1_2"
		android:layout_marginTop = "20dp"
	        android:layout_width="wrap_content"
		android:layout_height="wrap_content" >

		<RadioButton android:id="@+id/voip1_2"
			android:text="@string/str_voip1"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip2_2"
			android:text="@string/str_voip2"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip3_2"
			android:text="@string/str_voip3"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

	</RadioGroup>
            <TextView
                    android:id="@+id/textView2_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_dial_number"
                    android:layout_alignBottom="@+id/radGroup2"
		    android:layout_toRightOf = "@+id/radGroup2"
		    android:layout_marginLeft = "10dp"
                    android:textColor="#000000"
                    android:textSize="24sp" />

             <CheckBox
                android:id="@+id/checkB_enable2"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/textView2_2"
                android:layout_above="@+id/textView2_2"

                />


              <EditText android:layout_width="200dp"
              android:layout_height="50dp"
              android:id="@+id/editText2"

              android:layout_alignBottom="@+id/textView2_2"
	      android:layout_toRightOf = "@+id/textView2_2"
	      android:layout_centerHorizontal = "true"
	      android:textColor="#000000"
	      android:textSize="24sp"
	      android:background="@drawable/bg_edittext"
	      android:phoneNumber="true"
	      android:layout_marginLeft = "10dp"/>


    </RelativeLayout>

        <RelativeLayout
                android:id="@+id/set_sos_layout5"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_marginLeft="20dp"
		android:layout_toRightOf = "@+id/set_sos_layout2"
		android:layout_alignTop = "@+id/set_sos_layout2"

		>
            <TextView
                    android:id="@+id/textView1_5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_sos_number_3"
                    android:textColor="#000000"
                    android:textSize="24sp" />


	<RadioGroup android:id="@+id/radGroup5"
	        android:orientation="horizontal"
		android:layout_below = "@+id/textView1_5"
		android:layout_marginTop = "20dp"
	        android:layout_width="wrap_content"
		android:layout_height="wrap_content" >

		<RadioButton android:id="@+id/voip1_5"
			android:text="@string/str_voip1"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip2_5"
			android:text="@string/str_voip2"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip3_5"
			android:text="@string/str_voip3"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

	</RadioGroup>
            <TextView
                    android:id="@+id/textView2_5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_dial_number"
                    android:layout_alignBottom="@+id/radGroup5"
		    android:layout_toRightOf = "@+id/radGroup5"
		    android:layout_marginLeft = "10dp"
                    android:textColor="#000000"
                    android:textSize="24sp" />

             <CheckBox
                android:id="@+id/checkB_enable5"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/textView2_5"
                android:layout_above="@+id/textView2_5"

                />


              <EditText android:layout_width="200dp"
              android:layout_height="50dp"
              android:id="@+id/editText5"

              android:layout_alignBottom="@+id/textView2_5"
	      android:layout_toRightOf = "@+id/textView2_5"
	      android:layout_centerHorizontal = "true"
	      android:textColor="#000000"
	      android:textSize="24sp"
	      android:background="@drawable/bg_edittext"
	      android:phoneNumber="true"
	      android:layout_marginLeft = "10dp"/>


    </RelativeLayout>

        <RelativeLayout
                android:id="@+id/set_sos_layout3"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_below = "@+id/set_sos_layout2"
		android:layout_alignLeft = "@+id/set_sos_layout2"

		>
            <TextView
                    android:id="@+id/textView1_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_sos_number_5"
                    android:textColor="#000000"
                    android:textSize="24sp" />


	<RadioGroup android:id="@+id/radGroup3"
	        android:orientation="horizontal"
		android:layout_below = "@+id/textView1_3"
		android:layout_marginTop = "20dp"
	        android:layout_width="wrap_content"
		android:layout_height="wrap_content" >

		<RadioButton android:id="@+id/voip1_3"
			android:text="@string/str_voip1"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip2_3"
			android:text="@string/str_voip2"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

		<RadioButton android:id="@+id/voip3_3"
			android:text="@string/str_voip3"
			android:layout_height="wrap_content"
			android:layout_width="wrap_content"
			android:textColor="#000000"
			android:textSize="24sp" />

	</RadioGroup>
            <TextView
                    android:id="@+id/textView2_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_dial_number"
                    android:layout_alignBottom="@+id/radGroup3"
		    android:layout_toRightOf = "@+id/radGroup3"
		    android:layout_marginLeft = "10dp"
                    android:textColor="#000000"
                    android:textSize="24sp" />

             <CheckBox
                android:id="@+id/checkB_enable3"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@+id/textView2_3"
                android:layout_above="@+id/textView2_3"

                />


              <EditText android:layout_width="200dp"
              android:layout_height="50dp"
              android:id="@+id/editText3"

              android:layout_alignBottom="@+id/textView2_3"
	      android:layout_toRightOf = "@+id/textView2_3"
	      android:layout_centerHorizontal = "true"
	      android:textColor="#000000"
	      android:textSize="24sp"
	      android:background="@drawable/bg_edittext"
	      android:phoneNumber="true"
	      android:layout_marginLeft = "10dp"/>


    </RelativeLayout>

        <RelativeLayout
                android:id="@+id/set_sos_layout6"
		android:layout_height="wrap_content"
		android:layout_width="wrap_content"
		android:layout_below = "@+id/set_sos_layout5"
		android:layout_alignLeft = "@+id/set_sos_layout5"

		>

    <Button
        android:id="@+id/set_sos_button_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginLeft="20dp"
        android:textSize="36sp"
        android:text="@string/str_finish" />

    <Button
        android:id="@+id/set_sos_button_prev"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:layout_toRightOf="@+id/set_sos_button_ok"
        android:layout_alignTop="@+id/set_sos_button_ok"
        android:textSize="36sp"
        android:text="@string/str_prev" />

    </RelativeLayout>

    </RelativeLayout>


</LinearLayout>
