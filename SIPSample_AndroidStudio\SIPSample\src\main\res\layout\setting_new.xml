<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/bg2"
    android:orientation="vertical"
    android:paddingBottom="30dp"
    android:paddingLeft="50dp"
    android:paddingRight="50dp"
    android:paddingTop="30dp" >

    <TextView
        android:id="@+id/setting_text_ip"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/str_warning"
        android:textColor="#ff000000"
        android:textSize="18sp"
        android:clickable="true"
        />


        <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

    <Button
        android:id="@+id/setting_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="5dp"
        android:textSize="18sp"
        android:text="@string/str_prev" />

     <TextView
        android:id="@+id/setting_version"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="VERSION 1.0"
        android:textColor="#ff000000"
        android:textSize="18sp"
        android:clickable="false"
        />

     </LinearLayout>

    <ScrollView
        android:layout_width="fill_parent"
        android:layout_height="fill_parent" >

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:orientation="vertical"
            android:paddingLeft="30dp"
            android:paddingRight="30dp" >

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/set_register"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_register_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_register_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/setting_unlock"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"

                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_opt_unlock_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_opt_unlock_info"
                            android:textSize="18sp" />
                    </LinearLayout>



                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/setting_rel_yuyin"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:clickable="true"
                    android:background="#00ffffff" >

                    <CheckBox
                        android:id="@+id/setting_checkB_yuyin"
                        style="@style/vstyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:clickable = "false"
                        android:layout_marginRight="10dp" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_toLeftOf="@id/setting_checkB_yuyin"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_voice_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_voice_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

               <RelativeLayout
                    android:id="@+id/show_video"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff" 
                    android:clickable="true"
                    >
        
                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"         
                            android:text="@string/str_video_data_title"      
                            android:textColor="#ff000000"
                            android:textSize="20sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_video_data_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>


            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/setting_config"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_config_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_config_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/setting_volume"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"

                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_volume_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_volume_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/setting_rel_yanhou"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff" >

                    <CheckBox
                        android:id="@+id/setting_checkB_baoquan"
                        style="@style/vstyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:clickable = "false"
                        android:layout_margin="10dp" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_toLeftOf="@id/setting_checkB_baoquan"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_delay_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_delay_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/setting_sos"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >


                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"

                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_sos_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_sos_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/setting_rel_tishi"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >

                    <CheckBox
                        android:id="@+id/setting_checkB_xunxi"
                        style="@style/vstyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:clickable = "false"
                        android:layout_margin="10dp" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_toLeftOf="@id/setting_checkB_xunxi"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_msg_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_msg_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/setting_zone"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >


                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"

                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_zone_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_zone_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/setting_ring"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    android:visibility="visible"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_ring_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_ring_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/setting_mdoor"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"

                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_setting_mdoor_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_setting_mdoor_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/setting_video_call"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    android:visibility="visible"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_video_call_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_video_call_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/setting_alarm_msg"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    android:visibility="visible"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_alarm_msg_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_alarm_msg_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/setting_center_ip"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    android:visibility="visible"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_center_ip_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_center_ip_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/setting_other"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    android:visibility="visible"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_other_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_other_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:orientation="horizontal" >

                <RelativeLayout
                    android:id="@+id/setting_network"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="true"
                    android:visibility="visible"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/str_network_title"
                            android:textColor="#ff000000"
                            android:textSize="20sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/str_network_info"
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/setting_other"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:layout_weight="1"
                    android:background="#00ffffff"
                    android:clickable="false"
                    android:visibility="visible"
                    >

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=""
                            android:textColor="#ff000000"
                            android:textSize="20sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text=""
                            android:textSize="18sp" />
                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

</LinearLayout>
