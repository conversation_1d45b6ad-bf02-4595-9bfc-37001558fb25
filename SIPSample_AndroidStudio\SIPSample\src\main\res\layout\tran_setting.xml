<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/bg3"
    android:paddingBottom="20dp"
    android:paddingLeft="5dp"
    android:paddingRight="10dp"
    android:paddingTop="6dp" >

    <RelativeLayout
        android:id="@+id/temp_12"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" >

          <ImageButton
            android:id="@+id/no"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:paddingLeft="2dp"
            android:paddingRight="2dp"

            android:background="@drawable/cancel_3" />

          <ImageButton
            android:id="@+id/yes"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="2dp"
            android:layout_toLeftOf="@id/no"
            android:paddingLeft="2dp"
            android:paddingRight="2dp"

            android:background="@drawable/confirm_3" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginTop="100dp"
        android:layout_above="@id/temp_12"
        android:orientation="vertical" >

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:background="#00ffffff" >

            <CheckBox
                android:id="@+id/checkB_1"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:layout_toRightOf="@id/checkB_1"
                android:text="@string/str_all_forward"
                android:textColor="#ff000000"
                android:textSize="15sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/checkB_1"
                android:layout_marginLeft="4dp"
                android:layout_toRightOf="@id/checkB_1"
                android:orientation="horizontal" >

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" >

                    <CheckBox
                        android:id="@+id/checkC_11"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="@string/str_line1"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />

                    <CheckBox
                        android:id="@+id/checkC_12"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_line2"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />

                    <CheckBox
                        android:id="@+id/checkC_13"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_line_pstn"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="4dp"
                    android:text="@string/str_forward_number"
                    android:textColor="#ff000000"
                    android:textSize="15sp" />

                <EditText
                    android:id="@+id/edit_1"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="2dp"

                    android:inputType="text"
                    android:maxLength="20"
                    android:textColor="#ff000000"
                    android:textSize="15sp" />
            </LinearLayout>
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:background="#00ffffff" >

            <CheckBox
                android:id="@+id/checkB_2"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:layout_toRightOf="@id/checkB_2"
                android:text="@string/str_busy_forward"
                android:textColor="#ff000000"
                android:textSize="15sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/checkB_2"
                android:layout_marginLeft="4dp"
                android:layout_toRightOf="@id/checkB_2"
                android:orientation="horizontal" >

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" >

                    <CheckBox
                        android:id="@+id/checkC_21"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="@string/str_line1"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />

                    <CheckBox
                        android:id="@+id/checkC_22"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_line2"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />

                    <CheckBox
                        android:id="@+id/checkC_23"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_line_pstn"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="4dp"
                    android:text="@string/str_forward_number"
                    android:textColor="#ff000000"
                    android:textSize="15sp" />

                <EditText
                    android:id="@+id/edit_2"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="2dp"

                    android:inputType="text"
                    android:maxLength="20"
                    android:textColor="#ff000000"
                    android:textSize="15sp" />
            </LinearLayout>
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:background="#00ffffff" >

            <CheckBox
                android:id="@+id/checkB_3"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:layout_toRightOf="@id/checkB_3"
                android:text="@string/str_timeout_forward"
                android:textColor="#ff000000"
                android:textSize="15sp" />

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/checkB_3"
                android:layout_marginLeft="4dp"
                android:layout_toRightOf="@id/checkB_3"
                android:orientation="horizontal" >

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" >

                    <CheckBox
                        android:id="@+id/checkC_31"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="@string/str_line1"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />

                    <CheckBox
                        android:id="@+id/checkC_32"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_line2"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />

                    <CheckBox
                        android:id="@+id/checkC_33"
                        style="@style/mystyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/str_line_pstn"
                        android:textColor="#ff000000"
                        android:textSize="13sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="4dp"
                    android:text="@string/str_forward_number"
                    android:textColor="#ff000000"
                    android:textSize="15sp" />

                <EditText
                    android:id="@+id/edit_3"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="2dp"

                    android:inputType="text"
                    android:maxLength="20"
                    android:textColor="#ff000000"
                    android:textSize="15sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="4dp"
                    android:text="@string/str_timeout_second"
                    android:textColor="#ff000000"
                    android:textSize="15sp" />

                <EditText
                    android:id="@+id/edit_timeout"
                    android:layout_width="50dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="2dp"
                    android:digits="0123456789"
                    android:inputType="phone"
                    android:maxLength="5"
                    android:textColor="#ff000000"
                    android:textSize="15sp" />

            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>

</RelativeLayout>
