<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="#ffDCDCDC"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical" >

    <ImageView
        android:id="@+id/zhuangtaiIMG"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="5dp"
        android:src="@drawable/icon" />

    <LinearLayout
        android:layout_toRightOf="@id/zhuangtaiIMG"
        android:layout_marginLeft="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
   		android:orientation="vertical"
        android:layout_marginRight="5dp"
        >

    <TextView
        android:id="@+id/haoma"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="13891231231"
        android:textStyle="bold"
        android:textColor="#ff000000"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="加标示"
        android:textColor="#ff696969"
        android:textSize="9sp" />

</LinearLayout>
    <LinearLayout
        android:layout_alignParentRight="true"
        android:layout_marginRight="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
   		android:orientation="vertical"
        >

    <TextView
        android:id="@+id/riqi"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="8月23日"
        android:textColor="#ff696969"
        android:textSize="9sp" />

    <TextView
        android:id="@+id/shijian"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="20:22"
        android:textColor="#ff696969"
        android:textSize="9sp" />

</LinearLayout>

</RelativeLayout>
