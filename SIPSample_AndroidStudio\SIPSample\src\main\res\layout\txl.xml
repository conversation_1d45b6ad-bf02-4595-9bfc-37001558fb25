<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/bg2"
    android:paddingBottom="20dp"
    android:paddingLeft="100dp"
    android:paddingRight="100dp"
    android:paddingTop="100dp" >

    <RelativeLayout
        android:id="@+id/temp_12"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" >

        <Button
            android:id="@+id/no"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:text="@string/str_cancel"
            android:textSize="34sp" />

        <Button
            android:id="@+id/yes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:layout_toLeftOf="@id/no"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:text="@string/str_add_new"
            android:textSize="34sp" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_above="@id/temp_12"
        android:orientation="vertical" >

        <com.weema.sipsample.ui.ClearEditText
            android:id="@+id/filter_edit"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dip"
            android:background="@drawable/search_bar_edit_selector"
            android:drawableLeft="@drawable/search_bar_icon_normal"
            android:hint="@string/str_input_keyword"
            android:singleLine="true"
            android:textSize="28sp" />

        <FrameLayout
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:layout_margin="8dp" >

            <ListView
                android:id="@+id/txllistView"
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:layout_margin="20dp" >
            </ListView>

            <TextView
                android:id="@+id/dialog"
                android:layout_width="200dp"
                android:layout_height="120dp"
                android:layout_gravity="center"
                android:background="#88000000"
                android:gravity="center"
                android:textColor="#ffffffff"
                android:textSize="40sp"
                android:visibility="invisible" />

            <com.weema.sipsample.util.SideBar
                android:id="@+id/sidrbar"
                android:layout_width="40dp"
                android:layout_height="fill_parent"
                android:layout_gravity="right|center" />
        </FrameLayout>
    </LinearLayout>

</RelativeLayout>
