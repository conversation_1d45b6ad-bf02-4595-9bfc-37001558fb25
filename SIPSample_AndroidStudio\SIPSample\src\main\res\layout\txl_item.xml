<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="#ffDCDCDC"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical" >

    <TextView
        android:id="@+id/catalog"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="#E0E0E0"
        android:paddingBottom="5dip"
        android:paddingLeft="5dip"
        android:paddingTop="5dip"
        android:textColor="#454545"
        android:textSize="24sp" />

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="#00000000"
        android:descendantFocusability="blocksDescendants"
        android:orientation="horizontal" >

        <ImageView
            android:id="@+id/touxiang"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:src="@drawable/icon"
            android:visibility="gone" />

        <TextView
            android:id="@+id/xingming"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="10dp"
            android:layout_weight="1"
            android:text="张三"
            android:textColor="#ff000000"
            android:textSize="30sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/haoma"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="10dp"
            android:layout_weight="1"
            android:text="13891231231"
            android:textColor="#ff000000"
            android:textSize="30sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/beizhu"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="10dp"
            android:layout_weight="1"
            android:text="備註"
            android:textColor="#ff696969"
            android:textSize="30sp" />
    </LinearLayout>

</LinearLayout>