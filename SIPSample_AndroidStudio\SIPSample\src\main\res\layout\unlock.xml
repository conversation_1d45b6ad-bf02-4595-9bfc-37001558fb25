<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:padding="10dp" >

    <LinearLayout
        android:id="@+id/unlock_dialArea1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop = "50dp"
        android:layout_marginLeft = "400dp"
        android:orientation="horizontal" >

        <LinearLayout
            android:id="@+id/unlock_dialArea011"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30dp"
            android:orientation="vertical" >

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >

                <EditText
                    android:id="@+id/unlock_new_editText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginTop="30dp"
                    android:background="@drawable/dialtextview"
                    android:focusable="false"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:textSize="30sp"
                    android:text="@string/str_new_passwd"
                    android:textColor="#ffffffff"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/unlock_dialButton_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/number_1" />

                <ImageButton
                    android:id="@+id/unlock_dialButton_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/number_2" />

                <ImageButton
                    android:id="@+id/unlock_dialButton_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/number_3" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/unlock_dialButton_4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/number_4" />

                <ImageButton
                    android:id="@+id/unlock_dialButton_5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/number_5" />

                <ImageButton
                    android:id="@+id/unlock_dialButton_6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/number_6" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/unlock_dialButton_7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/number_7" />

                <ImageButton
                    android:id="@+id/unlock_dialButton_8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/number_8" />

                <ImageButton
                    android:id="@+id/unlock_dialButton_9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/number_9" />
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="horizontal" >

                <ImageButton
                    android:id="@+id/unlock_dialButton_esc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/number_esc" />

                <ImageButton
                    android:id="@+id/unlock_dialButton_0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@drawable/number_0" />

                <ImageButton
                    android:id="@+id/unlock_dialButton_ok"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/number_ok" />
            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
