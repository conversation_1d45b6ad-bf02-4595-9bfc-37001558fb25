<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/bg1"
    android:paddingTop = "100dp"
    android:orientation="vertical" >
        
    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="250dp"
            android:orientation="vertical"
            android:visibility="visible"
             >
            
       <TextView
                android:id="@+id/video_ver"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"              
                android:text="ver"
                android:textColor="@android:color/black"
                android:textSize="24sp"
                android:textStyle="bold" />
                

            </LinearLayout>

            
    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="250dp"
            android:orientation="horizontal"
            android:visibility="visible"
             >
            
       <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_ip_path"
                android:textColor="@android:color/black"
                android:textSize="24sp"
                android:textStyle="bold" />
                

                <EditText android:id="@+id/editVideoIP"
				android:layout_width="fill_parent"
				android:layout_height="wrap_content"
				android:layout_marginRight="10dip" 
				android:layout_marginLeft="10dip"				
				android:textColor="#000000"
				android:textSize="24sp"
				android:text="*************" />
 
            </LinearLayout>

    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            android:layout_marginLeft="250dp"
            android:orientation="horizontal"
            android:visibility="visible"
             >
            
        <LinearLayout
            android:id="@+id/textVideo_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            
            android:visibility="visible"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            >
 
            <TextView
                android:id="@+id/textVideo"
                android:layout_width="600dp"
                android:layout_height="wrap_content"
                android:text="@string/str_no_data"
                android:maxLines="1000"
                android:scrollbars="vertical"
                android:textColor="@android:color/black"
                android:textSize="24sp"
                android:textStyle="bold" />
 
 
        </LinearLayout>    
        
    <LinearLayout
            android:id="@+id/video_button"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent"
            
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="10dp"
            android:layout_marginRight="10dp"
            
            android:orientation="vertical"
            android:visibility="visible"
             > 
    <Button
        android:id="@+id/video_button_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="20dp"
        android:textSize="30sp"
        android:layout_alignTop="@id/video_button"
        android:text="@string/str_download" />

    <Button
        android:id="@+id/video_button_exit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="20dp"
        android:textSize="30sp"
        android:layout_alignBottom="@id/video_button"
        android:text="@string/str_exit" />

        </LinearLayout>
         
        </LinearLayout>
      
       
        
</LinearLayout>
