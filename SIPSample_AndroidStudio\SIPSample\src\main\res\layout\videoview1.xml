<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/top_view"
    android:background="@drawable/bg2"
    android:orientation="vertical" >

    <RelativeLayout
        android:id="@+id/top_title"
         android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"

        android:layout_marginLeft="30dp"
        android:layout_marginRight="200dp"
        android:layout_marginTop="5dp" >

        <TextView
            android:id="@+id/volume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:text="311,533333"
            android:textSize="18sp" />

         <ImageButton
            android:id="@+id/bt_vol_jia"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/volume"

            android:layout_marginRight="20dp"
            android:background="@drawable/vol_jia" />

        <ImageButton
            android:id="@+id/bt_mic_jia"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:layout_toLeftOf="@id/bt_vol_jia"
            android:background="@drawable/mic_jia" />

        <TextView
            android:id="@+id/tv_call_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:layout_toLeftOf="@id/bt_mic_jia"
            android:background="@drawable/dialtextview"
            android:gravity="center"
            android:text="来电号码：13510835545"
            android:textColor="#ffffffff"
            android:textSize="18sp" />

        <ImageButton
            android:id="@+id/bt_mic_jian"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:layout_toLeftOf="@id/tv_call_number"
            android:background="@drawable/mic_jian" />

        <ImageButton
            android:id="@+id/bt_vol_jian"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:layout_toLeftOf="@id/bt_mic_jian"
            android:background="@drawable/vol_jian" />
    </RelativeLayout>

    <RelativeLayout
      android:id="@+id/video_title"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:gravity="center"
      android:layout_below="@id/top_title"
      android:orientation="vertical" >

    <LinearLayout
        android:id="@+id/llRemoteView"
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:gravity="center"
        android:background="@color/black"
        android:orientation="vertical" >
        <com.portsip.PortSIPVideoRenderer
            android:id="@+id/remote_video_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:orientation="vertical" >
        <ImageButton
            android:id="@+id/ibscale"
            android:layout_marginTop="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/aspect_fit" />
        <ImageButton
            android:id="@+id/ibcamera"
            android:layout_marginTop="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/switch_video" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llLocalView"
        android:layout_height="176dp"
        android:layout_width="144dp"
        android:gravity="center"
        android:layout_marginTop="5dp"
        android:layout_marginRight="5dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true">
        <com.portsip.PortSIPVideoRenderer
            android:id="@+id/local_video_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>
  </RelativeLayout>

  <LinearLayout
      android:id="@+id/left_title"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"

      android:layout_alignParentBottom="true"
      android:layout_margin="5dp"
       >

      <ImageButton
          android:id="@+id/bt_video"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:src="@drawable/video_1" />

      <ImageButton
          android:id="@+id/bt_audio"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:src="@drawable/audio_1" />


      <ImageButton
          android:id="@+id/bt_offoo"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:src="@drawable/off" />

      <ImageButton
          android:id="@+id/bt_hold"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:src="@drawable/hold" />

      <ImageButton
          android:id="@+id/bt_tran"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:visibility="visible"
          android:src="@drawable/tran" />

      <ImageButton
          android:id="@+id/bt_conf"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:src="@drawable/conf" />

      <ImageButton
          android:id="@+id/bt_open"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:src="@drawable/open" />

      <ImageButton
          android:id="@+id/bt_dial"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:src="@drawable/dial" />

      <ImageButton
          android:id="@+id/bt_door_talk"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:visibility="gone"
          android:src="@drawable/door_talk" />

      <ImageButton
          android:id="@+id/bt_answer"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginTop="10dp"
          android:background="#00000000"
          android:src="@drawable/fanswer" />

  </LinearLayout>

</RelativeLayout>

