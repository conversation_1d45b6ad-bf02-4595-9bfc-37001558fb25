<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@drawable/bg1"
    android:paddingTop = "60dp"
    android:orientation="vertical" >
    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:layout_marginRight="5dp"

            android:orientation="vertical"
            android:visibility="visible"
             >
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:text="@string/str_speaker_volume"
                android:textColor="@android:color/black"
                android:textSize="15sp"
                android:textStyle="bold" />
        <SeekBar
            android:id="@+id/volume_bar"
            android:layout_width="400dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"

            android:progress="0"
            android:max="10"
            android:progressDrawable="@drawable/progressbar"
            android:secondaryProgress="0" />
    </LinearLayout>
    <LinearLayout
            android:layout_width="400dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            android:visibility="visible"
            android:weightSum="11" >
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="0"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="1"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="2"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="3"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="4"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="5"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="6"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="7"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="8"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="9"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="10"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
    </LinearLayout>
    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:layout_marginRight="5dp"

            android:orientation="vertical"
            android:visibility="visible"
             >
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:text="@string/str_speaker_volume_hw"
                android:textColor="@android:color/black"
                android:textSize="15sp"
                android:textStyle="bold" />
        <SeekBar
            android:id="@+id/volume_hw_bar"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"

            android:progress="0"
            android:max="5"
            android:progressDrawable="@drawable/progressbar"
            android:secondaryProgress="0" />
    </LinearLayout>
    <LinearLayout
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            android:visibility="visible"
            android:weightSum="6" >
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="0"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="1"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="2"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="3"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="4"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="5"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
    </LinearLayout>
    
    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:layout_marginRight="5dp"

            android:orientation="vertical"
            android:visibility="visible"
             >
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:text="@string/str_mic_volume"
                android:textColor="@android:color/black"
                android:textSize="15sp"
                android:textStyle="bold" />
        <SeekBar
            android:id="@+id/volume_mic_bar"
            android:layout_width="400dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"

            android:progress="0"
            android:max="10"
            android:progressDrawable="@drawable/progressbar"
            android:secondaryProgress="0" />
    </LinearLayout>
    <LinearLayout
            android:layout_width="400dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            android:visibility="visible"
            android:weightSum="11" >
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="0"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="1"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="2"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="3"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="4"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="5"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="6"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="7"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="8"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="9"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:text="10"
            android:textColor="@android:color/black"
            android:textSize="10sp"
            android:textStyle="bold" />
    </LinearLayout>
    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="5dp"
            android:layout_marginRight="5dp"

            android:orientation="vertical"
            android:visibility="visible"
             >
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:text="@string/str_ring_volume"
                android:textColor="@android:color/black"
                android:textSize="15sp"
                android:textStyle="bold" />
        <SeekBar
            android:id="@+id/volume_ring_bar"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"

            android:progress="0"
            android:max="5"
            android:progressDrawable="@drawable/progressbar"
            android:secondaryProgress="0" />
    </LinearLayout>
    <LinearLayout
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="100dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            android:visibility="visible"
            android:weightSum="6" >
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="0"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="1"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="2"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="3"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="4"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
        <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="5"
                android:textColor="@android:color/black"
                android:textSize="10sp"
                android:textStyle="bold" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="100dp"
        android:orientation="horizontal"
        android:visibility="visible">
        <ImageButton
            android:id="@+id/volume_button_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/confirm_3"
            />
        <ImageButton
            android:id="@+id/volume_button_prev"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/back_3"
            />
    </LinearLayout>
</LinearLayout>