<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg1"
    android:orientation="vertical" >
    
    <TextView
        android:id="@+id/zone_textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="150dp"
        android:layout_marginLeft="400dp"
        android:text="@string/str_zone_title"
        android:textColor="#000000"
        android:textSize="36dp" />
     
    <LinearLayout android:orientation="horizontal" 
		android:layout_height="wrap_content" 
		android:layout_width="wrap_content" 
		android:layout_marginLeft="400dp"
		android:layout_marginTop="20dp"
		>
		
    <TextView
        android:id="@+id/zone_textView1"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:text="@string/str_zone1"
        android:textColor="#000000"
        android:textSize="36dp" />

            <CheckBox
                android:id="@+id/zone_checkB_1"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true" />
	    
    </LinearLayout>

    <LinearLayout android:orientation="horizontal" 
		android:layout_height="wrap_content" 
		android:layout_width="wrap_content" 
		android:layout_marginLeft="400dp"
		android:layout_marginTop="20dp"
		>
		
    <TextView
        android:id="@+id/zone_textView2"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:text="@string/str_zone2"
        android:textColor="#000000"
        android:textSize="36dp" />
        
            <CheckBox
                android:id="@+id/zone_checkB_2"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true" />
		
    </LinearLayout>

    <LinearLayout android:orientation="horizontal" 
		android:layout_height="wrap_content" 
		android:layout_width="wrap_content" 
		android:layout_marginLeft="400dp"
		android:layout_marginTop="20dp"
		>
		
    <TextView
        android:id="@+id/zone_textView3"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:text="@string/str_zone3"
        android:textColor="#000000"
        android:textSize="36dp" />

            <CheckBox
                android:id="@+id/zone_checkB_3"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true" />
        
    </LinearLayout>

    <LinearLayout android:orientation="horizontal" 
		android:layout_height="wrap_content" 
		android:layout_width="wrap_content" 
		android:layout_marginLeft="400dp"
		android:layout_marginTop="20dp"
		>
		
    <TextView
        android:id="@+id/zone_textView4"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:text="@string/str_zone4"
        android:textColor="#000000"
        android:textSize="36dp" />

            <CheckBox
                android:id="@+id/zone_checkB_4"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true" />
        
    </LinearLayout>

    <LinearLayout android:orientation="horizontal" 
		android:layout_height="wrap_content" 
		android:layout_width="wrap_content" 
		android:paddingLeft="400dp"
		android:layout_marginTop="20dp"
		>
		
    <TextView
        android:id="@+id/zone_textView5"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:text="@string/str_zone5"
        android:textColor="#000000"
        android:textSize="36dp" />

            <CheckBox
                android:id="@+id/zone_checkB_5"
                style="@style/mystyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true" />
        
    </LinearLayout>

    <LinearLayout android:orientation="horizontal" 
		android:layout_height="wrap_content" 
		android:layout_width="wrap_content" 
		android:layout_marginTop = "30dp"
		>
		
    <Button
        android:id="@+id/zone_button_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "400dp"
        android:textSize="30sp"
        android:text="@string/str_finish" />

    <Button
        android:id="@+id/zone_button_esc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft = "40dp"
        android:textSize="30sp"
        android:text="@string/str_prev" />
        
    </LinearLayout>
    
    
</LinearLayout>
