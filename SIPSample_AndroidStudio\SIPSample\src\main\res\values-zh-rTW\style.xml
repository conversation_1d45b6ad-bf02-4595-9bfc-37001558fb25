<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="portcheckbox">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">#008</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
    </style>

  <style name="radio_tab">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:layout_marginRight">1dp</item>
    <item name="android:layout_weight">1</item>
    <item name="android:background">@drawable/tabbuttonbg</item>
    <item name="android:button">@null</item>
    <item name="android:gravity">center_horizontal</item>
    <item name="android:paddingTop">10dp</item>
  </style>

  <style name="mystyle" parent="@android:style/Widget.CompoundButton.CheckBox">
      <item name="android:button">@drawable/mycheck_selection</item>
      <item name="android:paddingLeft">20dp</item>
  </style>

  <style name="vstyle" parent="@android:style/Widget.CompoundButton.CheckBox">
      <item name="android:button">@drawable/vcheck_selection</item>
      <item name="android:paddingLeft">120dp</item>
  </style>
    
  <style name="MyDialog" parent="@android:Theme.Dialog">
   <item name="android:windowFrame">@null</item>
   <item name="android:windowNoTitle">true</item>
   <item name="android:windowIsFloating">true</item>
   <item name="android:windowContentOverlay">@null</item>
</style>
</resources>
