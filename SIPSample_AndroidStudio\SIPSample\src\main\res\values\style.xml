<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="portcheckbox">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">#008</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
    </style>

  <style name="radio_tab">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:layout_marginRight">1dp</item>
    <item name="android:layout_weight">1</item>
    <item name="android:background">@drawable/tabbuttonbg</item>
    <item name="android:button">@null</item>
    <item name="android:gravity">center_horizontal</item>
    <item name="android:paddingTop">10dp</item>
  </style>

  <style name="mystyle" parent="@android:style/Widget.CompoundButton.CheckBox">
      <item name="android:button">@drawable/mycheck_selection</item>
      <item name="android:paddingLeft">20dp</item>
  </style>

  <style name="vstyle" parent="@android:style/Widget.CompoundButton.CheckBox">
      <item name="android:button">@drawable/vcheck_selection</item>
      <item name="android:paddingLeft">120dp</item>
  </style>
    
  <style name="MyDialog" parent="@android:Theme.Dialog">
   <item name="android:windowFrame">@null</item>
   <item name="android:windowNoTitle">true</item>
   <item name="android:windowIsFloating">true</item>
   <item name="android:windowContentOverlay">@null</item>
</style>

    <style name="activity_login_img_delete">
        <item name="android:layout_alignParentRight">true</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">10dip</item>
        <item name="android:visibility">invisible</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:src">@drawable/login_del_ico_def</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:layout_marginRight">5dip</item>
    </style>

    <style name="activity_login_edittext" parent="blueCursorEditText">
        <item name="android:textColor">@color/portgo_color_black</item>
        <item name="android:textColorHint">@color/portgo_color_darkgray</item>
        <item name="android:textSize">@dimen/activity_login_text_size</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_alignParentLeft">true</item>
        <item name="android:layout_height">@dimen/activity_login_edittext_height</item>
        <item name="android:paddingLeft">10dip</item>
        <item name="android:paddingRight">10dip</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">middle</item>
        <item name="android:layout_marginBottom">2dip</item>
        <item name="android:drawableBottom">@null</item>
        <item name="android:background">@null</item>
        <item name="android:gravity">bottom</item>
    </style>

    <style name="blueCursorEditText" parent="android:Widget.EditText">
        <item name="android:textCursorDrawable">@drawable/port_edittext_cursor</item>
    </style>

    <style name="PortActionBarTheme" parent="android:Widget.Toolbar">
       
       
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
      
    </style>

    <style name="ToolBarTextAppearance" parent="android:TextAppearance.DeviceDefault.Widget.ActionBar.Title">
        <item name="android:textColor">@color/portgo_tool_color_blue</item>
        <item name="android:textSize">@dimen/toolbar_title_size</item>
    </style>  

    <style name="activity_main_tab">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@android:color/white</item>
        <item name="android:textColor">@drawable/activity_main_tab_text_selector</item>
        <item name="android:textSize">@dimen/activity_main_tab_textsize</item>
        <item name="android:button">@null</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
        <item name="android:orientation">vertical</item>
        <item name="android:paddingTop">@dimen/tab_paddingTop</item>
    </style>

    <style name="activity_main_tab_message">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@android:color/white</item>
        <item name="android:textColor">@drawable/activity_main_tab_text_selector</item>
        <item name="android:textSize">@dimen/activity_main_tab_textsize</item>
        <item name="android:button">@null</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
        <item name="android:orientation">vertical</item>
    </style>

</resources>
