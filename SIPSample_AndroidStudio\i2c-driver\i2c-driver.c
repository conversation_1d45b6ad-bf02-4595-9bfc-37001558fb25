/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
#include <stdio.h>
#include <string.h>
#include <jni.h>
#include "i2c.h"

/* This is a trivial JNI example where we use a native method
 * to return a new VM String. See the corresponding Java source
 * file located at:
 *
 *   hello-jni/app/src/main/java/com/example/hellojni/HelloJni.java
 */
JNIEXPORT jstring JNICALL
Java_com_weema_sipsample_service_i2cDriver_stringFromJNI( JNIEnv* env, jobject thiz )
{
#if defined(__arm__)
    #if defined(__ARM_ARCH_7A__)
    #if defined(__ARM_NEON__)
      #if defined(__ARM_PCS_VFP)
        #define ABI "armeabi-v7a/NEON (hard-float)"
      #else
        #define ABI "armeabi-v7a/NEON"
      #endif
    #else
      #if defined(__ARM_PCS_VFP)
        #define ABI "armeabi-v7a (hard-float)"
      #else
        #define ABI "armeabi-v7a"
      #endif
    #endif
  #else
   #define ABI "armeabi"
  #endif
#elif defined(__i386__)
#define ABI "x86"
#elif defined(__x86_64__)
#define ABI "x86_64"
#elif defined(__mips64)  /* mips64el-* toolchain defines __mips__ too */
#define ABI "mips64"
#elif defined(__mips__)
#define ABI "mips"
#elif defined(__aarch64__)
#define ABI "arm64-v8a"
#else
#define ABI "unknown"
#endif


    char outbuf[100];

    sprintf(outbuf,"Hello from JNI !  Compiled with ABI  %s . ",ABI);
    return (*env)->NewStringUTF(env, outbuf);
}

void JNIEXPORT
Java_com_weema_sipsample_service_i2cDriver_init( JNIEnv* env, jobject thiz )
{
  unsigned char buf[1] = {0x0};
  unsigned char ret;

  LOGD("Java_com_weema_sipsample_service_i2cDriver_init");
  ret = i2c_write(0,0x00,buf,1);
  ret = i2c_write(0,0x01,buf,1);
  buf[0] = 0xFF;
  ret = i2c_write(0,0x0C,buf,1);
  ret = i2c_write(0,0x0D,buf,1);

  i2c_write(0,0x12,buf,1);
  buf[0] = 0;
  i2c_write(0,0x13,buf,1);

}
void JNIEXPORT
Java_com_weema_sipsample_service_i2cDriver_close( JNIEnv* env, jobject thiz )
{
   i2c_close();
}

JNIEXPORT jbyteArray JNICALL
Java_com_weema_sipsample_service_i2cDriver_write
(JNIEnv *env, jclass object, jbyteArray j_array){
    //1. 獲取陣列指標和長度
    jbyte *c_array = (*env)->GetByteArrayElements(env,j_array, 0);
    int len_arr = (*env)->GetArrayLength(env,j_array);
    //2. 具體處理
    jbyteArray c_result = (*env)->NewByteArray(env,len_arr);
    jbyte jbuf[len_arr];

    unsigned char buf[1];
    buf[0] = c_array[0];
    i2c_write(0,0x12,buf,1);
    i2c_write(0,0x13,buf,1);
    //3. 釋放記憶體
    (*env)->ReleaseByteArrayElements(env,j_array, c_array, 0);
    //4. 賦值
    (*env)->SetByteArrayRegion(env,c_result, 0, len_arr, jbuf);
    return c_result;
}
JNIEXPORT jbyteArray JNICALL
Java_com_weema_sipsample_service_i2cDriver_read
(JNIEnv *env, jclass object, jbyteArray j_array){
  //1. 獲取陣列指標和長度
  //jbyte *c_array = env->GetByteArrayElements(j_array, 0);
  int len_arr = 2;//env->GetArrayLength(j_array);
  //2. 具體處理
  jbyteArray c_result = (*env)->NewByteArray(env,len_arr);
  jbyte jbuf[len_arr];
  unsigned char buf[len_arr];

  i2c_read(0,0x12,buf,1);
  i2c_read(0,0x13,&buf[1],1);

  for(int i=0; i<len_arr; i ++ ){
      jbuf[i] = buf[i];
  }
  //3. 釋放記憶體
  //env->ReleaseByteArrayElements(j_array, c_array, 0);
  //4. 賦值
  (*env)->SetByteArrayRegion(env,c_result, 0, len_arr, jbuf);
  return c_result;
}
