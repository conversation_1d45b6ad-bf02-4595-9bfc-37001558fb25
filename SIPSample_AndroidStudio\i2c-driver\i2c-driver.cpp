/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
#include <stdio.h>
#include <string.h>
#include <jni.h>
#include <thread>
#include <unistd.h>
//#include <pthread.h>
#include "i2c.h"

/* This is a trivial JNI example where we use a native method
 * to return a new VM String. See the corresponding Java source
 * file located at:
 *
 *   hello-jni/app/src/main/java/com/example/hellojni/HelloJni.java
 */
 extern "C"
 {
   JNIEXPORT jstring JNICALL
   Java_com_weema_sipsample_service_i2cDriver_stringFromJNI( JNIEnv* env, jobject thiz );

   JNIEXPORT jint JNICALL
   Java_com_weema_sipsample_service_i2cDriver_init( JNIEnv* env, jclass object, jbyteArray j_index_array,
   jbyteArray j_dir_array,jbyteArray j_init_array,jbyteArray j_count_array,jbyteArray j_mode_array );


   void JNIEXPORT
   Java_com_weema_sipsample_service_i2cDriver_close( JNIEnv* env, jobject thiz );

   JNIEXPORT jbyteArray JNICALL
   Java_com_weema_sipsample_service_i2cDriver_read
   (JNIEnv *env, jclass object, jbyteArray j_array);

   JNIEXPORT jbyteArray JNICALL
   Java_com_weema_sipsample_service_i2cDriver_write
   (JNIEnv *env, jclass object, jbyteArray j_array);

   //static void *doi2cWork( void* );
 };

#define MAX_GPIO_AB 2
#define MAX_I2C_DEV 8
#define MAX_RW_LEN 2
#define MAX_GPIO_PORT 16
#define MAX_ACTIVE_COUNT 10

#define MAX_BZ_TIME 2
static int m_is_run = 1;
static int m_is_sos_mode = 1;

static int m_is_write[MAX_I2C_DEV];

enum {

  GPIO_CLOSE=0,
  GPIO_OPEN=1,


};

enum {

  BZ_CMD=1,
  LED_CMD,
  BZ_LONG_CMD,
  BZ_SHORT_CMD,
  OPEN_DOOR_CMD,
  CTRL_GAS_CMD,
  CTRL_BZ_CMD,

};

typedef struct i2c_state_struct{
  int index;
  int state;
  int count;
  int max_count;
  int open_event;
  int close_event;

}I2C_STATE_STRUCT;

typedef struct i2c_dev_struct
{
  int addr;
  unsigned char dir[MAX_GPIO_AB];
  unsigned char gpio[MAX_GPIO_AB];
  int enable;

  char index;
  unsigned char buf[MAX_RW_LEN];
  int is_write;
  int is_init;
  int is_start;

  int is_bz;
  int is_bz_long;
  int is_bz_short;

  int bz_addr;
  int bz_val;

  int is_led;
  int led_addr;
  int led_val;

  int is_open_door;
  int open_door_addr;
  int open_door_val;

  int is_ctrl_gas;
  int ctrl_gas_addr;
  int ctrl_gas_val;

  int is_ctrl_bz;
  int ctrl_bz_addr;
  int ctrl_bz_val;  

  I2C_STATE_STRUCT keep[MAX_GPIO_PORT];

  //int wr_addr;
  //int wr_val;
}I2C_DEV_STRUCT;


static I2C_DEV_STRUCT m_i2c_dev[MAX_I2C_DEV];
static I2C_DEV_STRUCT m_i2c_keyboard_dev[1];

static void loop();
static void keyboard_loop();
static void thread_init();
static void do_keyboard_lookup_io(int addr,int index,int value);
static void keyboard_lookup_io(int addr,int index);
static void lookup_io(int addr,int index);
static void do_lookup_io(I2C_STATE_STRUCT *p_keep,int value);
static void bz_long_cmd(int index);
static void bz_short_cmd(int index);
static void bz_write_cmd(int index);
static void led_write_cmd(int index);
static void write_cmd(int index,int io_addr,int io_wr);
static void medicine(int index);

static void lookup_medicine_io(int addr,int index);
static void do_lookup_medicine_io(int addr,I2C_STATE_STRUCT *p_keep,int value);
static void do_i2c_init(I2C_DEV_STRUCT* p_dev);
static void do_i2c_cmd(int i);
static int m_total=2;

void open_door_cmd(int index)
{
  write_cmd(index,m_i2c_dev[index].open_door_addr,1);

  sleep(1);

  write_cmd(index,m_i2c_dev[index].open_door_addr,0);

  m_i2c_dev[index].is_open_door = 0;
}
void ctrl_gas_cmd(int index)
{
  write_cmd(index,m_i2c_dev[index].ctrl_gas_addr,m_i2c_dev[index].ctrl_gas_val);

  m_i2c_dev[index].is_ctrl_gas = 0;
}

void ctrl_bz_cmd(int index)
{
  unsigned char gpio_val;
  int val = m_i2c_dev[index].ctrl_bz_val;

  if(val == 0 || val == 2)
  {
    if(val == 0)
      gpio_val = 0;
    else 
      gpio_val = 1;  

    write_cmd(index,m_i2c_dev[index].ctrl_bz_addr,gpio_val);  

    m_i2c_dev[index].dir[1] = m_i2c_dev[index].dir[1] & 0xEF;
  }
  else 
  {
    m_i2c_dev[index].dir[1] = m_i2c_dev[index].dir[1] | 0x10;
  }
  
  i2c_write(m_i2c_dev[index].addr,0x01,&m_i2c_dev[index].dir[1],1);
  m_i2c_dev[index].is_ctrl_bz = 0;
}

void bz_long_cmd(int index)
{
  write_cmd(index,m_i2c_dev[index].bz_addr,1);

  sleep(MAX_BZ_TIME);

  write_cmd(index,m_i2c_dev[index].bz_addr,0);

  m_i2c_dev[index].is_bz_long = 0;
}
void bz_short_cmd(int index)
{
  write_cmd(index,m_i2c_dev[index].bz_addr,1);

  usleep(1000*500);
  write_cmd(index,m_i2c_dev[index].bz_addr,0);

  usleep(1000*500);

  write_cmd(index,m_i2c_dev[index].bz_addr,1);

  usleep(1000*500);

  write_cmd(index,m_i2c_dev[index].bz_addr,0);

  m_i2c_dev[index].is_bz_short = 0;
}
void led_write_cmd(int index)
{
  write_cmd(index,m_i2c_dev[index].led_addr,m_i2c_dev[index].led_val);

  m_i2c_dev[index].is_led = 0;
}
void bz_write_cmd(int index)
{
  write_cmd(index,m_i2c_dev[index].bz_addr,m_i2c_dev[index].bz_val);

  m_i2c_dev[index].is_bz = 0;
}
void write_cmd(int index,int io_addr,int val)
{
  int io_index;
  int io_wr;

  io_index = 0;

  io_wr = 0x12;
  if(io_addr >= 8)
  {
    io_index = 1;
    io_addr = io_addr-8;
    io_wr = 0x13;
  }

  unsigned char gpio_val;

  gpio_val = m_i2c_dev[index].gpio[io_index];
  if(val)
  {
    gpio_val = gpio_val|(0x01<<io_addr);
  }
  else
  {
    gpio_val = gpio_val&~(0x01<<io_addr);
  }

  m_i2c_dev[index].gpio[io_index] = gpio_val;

  i2c_write(m_i2c_dev[index].addr,io_wr,&m_i2c_dev[index].gpio[io_index],1);

}

void medicine(int index)
{
      unsigned char buf[1];

     buf[0] = m_i2c_dev[index].gpio[0];

     unsigned char ret;

     ret = i2c_read(m_i2c_dev[index].addr,0x12,&buf[0],1);
     if(ret != 0)    return;

      m_i2c_dev[index].gpio[0] = buf[0];
#if 0
      buf[0] = ~buf[0];
      i2c_write(m_i2c_dev[index].addr,0x13,buf,1);
#endif
      lookup_medicine_io(index,0);

}
void loop()
{
  int i;

   m_total ++;
  //LOGD("loop ...");
  for(i=0;i<MAX_I2C_DEV;i++)
  {
      if(m_i2c_dev[i].enable == 0)    continue;

      unsigned char buf[2];
      unsigned char ret;

      do_i2c_init(&m_i2c_dev[i]);

      if(i > 0)
      {
          medicine(i);
          continue;
      }
      //continue;
      do_i2c_cmd(i);
   
#if 0
     
#else
      if(m_i2c_dev[i].is_start == 0) continue;

      buf[0] = m_i2c_dev[i].gpio[0];
      buf[1] = m_i2c_dev[i].gpio[1];

      ret = i2c_read(m_i2c_dev[i].addr,0x12,&buf[0],1);
      if(ret != 0)    continue;

      ret = i2c_read(m_i2c_dev[i].addr,0x13,&buf[1],1);
      if(ret != 0)    continue;

      if(m_i2c_dev[i].gpio[0] != buf[0])
      {
          m_i2c_dev[i].gpio[0] = buf[0];
          //i2c_write(m_i2c_dev[i].addr,0x13,buf,1);

      }

      lookup_io(i,0);
      //LOGD("i2c loop 1");

#if 10

      if(m_i2c_dev[i].gpio[1] != buf[1])
      {
          m_i2c_dev[i].gpio[1] = buf[1];
      }

     lookup_io(i,1);
#endif
#endif
  }
}
void do_i2c_init(I2C_DEV_STRUCT* p_dev)
{
  unsigned char buf[2];
  unsigned char ret;

  if(p_dev->is_init)
  {
    LOGD("i2c init ...");
    p_dev->is_init = 0;
    p_dev->is_start = 1;

    p_dev->is_open_door = 0;
    p_dev->is_bz = 0;
    p_dev->is_bz_long = 0;
    p_dev->is_bz_short = 0;
    p_dev->is_ctrl_gas = 0;
    p_dev->is_ctrl_bz = 0;


    buf[0] = p_dev->dir[0];
    ret = i2c_write(p_dev->addr,0x00,&buf[0],1);
    //ret = i2c_write(m_i2c_dev[i].addr,0x01,&m_i2c_dev[i].dir[1],1);
    //m_i2c_dev[i].dir[1] = 0;
    buf[1] = p_dev->dir[1];
    ret = i2c_write(p_dev->addr,0x01,&buf[1],1);
    buf[0] = 0xFF;
    ret = i2c_write(p_dev->addr,0x0C,buf,1);
    ret = i2c_write(p_dev->addr,0x0D,buf,1);

    buf[0] = p_dev->gpio[0];
    buf[1] = p_dev->gpio[1];
    ret = i2c_write(p_dev->addr,0x12,&buf[0],1);
    ret = i2c_write(p_dev->addr,0x13,&buf[1],1);

  }  
}
void do_i2c_cmd(int i)
{
    while(m_i2c_dev[i].is_write)
    {
      if(m_i2c_dev[i].is_open_door)
      {
        open_door_cmd(i);
      }
      else if(m_i2c_dev[i].is_ctrl_gas)
      {
        ctrl_gas_cmd(i);
      }   
      else if(m_i2c_dev[i].is_ctrl_bz)
      {
          ctrl_bz_cmd(i);
      }                        
      else if(m_i2c_dev[i].is_bz_long)
      {
        bz_long_cmd(i);
      }
      else if(m_i2c_dev[i].is_bz_short)
      {
        bz_short_cmd(i);
      }
      else if(m_i2c_dev[i].is_bz)
      {
        bz_write_cmd(i);
      }
      else if(m_i2c_dev[i].is_led)
      {
        led_write_cmd(i);
      }
      else
      {
        m_i2c_dev[i].is_write = 0;
      }
    }
}
void keyboard_loop()
{
  int i;

   m_total ++;
  //LOGD("loop ...");
  for(i=0;i<1;i++)
  {
      unsigned char buf[2];
      unsigned char ret;

      do_i2c_init(&m_i2c_dev[i]);
 
      do_i2c_init(&m_i2c_keyboard_dev[i]);

      do_i2c_cmd(i);

      buf[1] = m_i2c_dev[i].gpio[1];

      ret = i2c_read(m_i2c_dev[i].addr,0x13,&buf[1],1);
      if(ret != 0)    continue;

      if(m_i2c_dev[i].gpio[1] != buf[1])
      {
          m_i2c_dev[i].gpio[1] = buf[1];
      }

     lookup_io(i,1);

      const unsigned char scan[] = {0xFE,0xFD,0xFB,0xF7};
      for(int j=0;j<4;j++)
      {
        m_i2c_keyboard_dev[i].gpio[0] = scan[j];
        ret = i2c_write(m_i2c_keyboard_dev[i].addr,0x12,&m_i2c_keyboard_dev[i].gpio[0],1);

        buf[0] = m_i2c_keyboard_dev[i].gpio[0];
    
        ret = i2c_read(m_i2c_keyboard_dev[i].addr,0x12,&buf[0],1);
        if(ret != 0)    continue;

        if(m_i2c_keyboard_dev[i].gpio[0] != buf[0])
        {
          m_i2c_keyboard_dev[i].gpio[0] = buf[0];
          //i2c_write(m_i2c_dev[i].addr,0x13,buf,1);

        }

        keyboard_lookup_io(i,j);
      }
    
  }

}
void do_keyboard_lookup_io(int addr,int index,int value)
{
    I2C_STATE_STRUCT *p_keep = &m_i2c_keyboard_dev[addr].keep[index];

     if(value)
     {
         if(p_keep->state == GPIO_CLOSE)
         {
             p_keep->count++;
             if(p_keep->max_count<p_keep->count)
             {
                 p_keep->count = 0;

                 p_keep->state = GPIO_OPEN;

                 p_keep->open_event += 1;
                
             }

         }
         else
         {
           p_keep->count = 0;
         }
     }
     else
     {
           if(p_keep->state == GPIO_OPEN)
           {
               p_keep->count++;
               if(p_keep->max_count<p_keep->count)
               {
                   p_keep->count = 0;

                   p_keep->state = GPIO_CLOSE;

                   p_keep->close_event += 1;

               }

           }
           else
           {
             p_keep->count = 0;
           }
      }
}
void do_lookup_io(I2C_STATE_STRUCT *p_keep,int value)
{
     if(value)
     {
         if(p_keep->state == GPIO_CLOSE)
         {
             p_keep->count++;
             if(p_keep->max_count<p_keep->count)
             {
                 p_keep->count = 0;

                 p_keep->state = GPIO_OPEN;

                 p_keep->open_event = 1;

                 if(p_keep->index == 8)
                 {
                   m_i2c_dev[0].gpio[1] = m_i2c_dev[0].gpio[1]|0x08;
                   i2c_write(m_i2c_dev[0].addr,0x13,&m_i2c_dev[0].gpio[1],1);
                 }
             }

         }
         else
         {
           p_keep->count = 0;
         }
     }
     else
     {
           if(p_keep->state == GPIO_OPEN)
           {
               p_keep->count++;
               if(p_keep->max_count<p_keep->count)
               {
                   p_keep->count = 0;

                   p_keep->state = GPIO_CLOSE;

                   p_keep->close_event = 1;
                   if(p_keep->index == 8)
                   {
                     m_i2c_dev[0].gpio[1] = m_i2c_dev[0].gpio[1]&~0x08;
                     i2c_write(m_i2c_dev[0].addr,0x13,&m_i2c_dev[0].gpio[1],1);
                   }

               }

           }
           else
           {
             p_keep->count = 0;
           }
      }
}
void do_lookup_medicine_io(int addr,I2C_STATE_STRUCT *p_keep,int value)
{
     int index;

     if(value)
     {
         if(p_keep->state == GPIO_CLOSE)
         {
             p_keep->count++;
             if(p_keep->max_count<p_keep->count)
             {
                 p_keep->count = 0;

                 p_keep->state = GPIO_OPEN;

                 p_keep->open_event = 1;
                 index = p_keep->index;
                 m_i2c_dev[addr].gpio[1] = m_i2c_dev[addr].gpio[1]&(~(0x01<<index));
                 i2c_write(m_i2c_dev[addr].addr,0x13,&m_i2c_dev[addr].gpio[1],1);

             }

         }
         else
         {
           p_keep->count = 0;
         }
     }
     else
     {
           if(p_keep->state == GPIO_OPEN)
           {
               p_keep->count++;
               if(p_keep->max_count<p_keep->count)
               {
                   p_keep->count = 0;

                   p_keep->state = GPIO_CLOSE;

                   p_keep->close_event = 1;
                   index = p_keep->index;
                    m_i2c_dev[addr].gpio[1] = m_i2c_dev[addr].gpio[1]|(0x01<<index);
                    i2c_write(m_i2c_dev[addr].addr,0x13,&m_i2c_dev[addr].gpio[1],1);

               }

           }
           else
           {
             p_keep->count = 0;
           }
      }
}
void lookup_medicine_io(int addr,int index)
{
      for(int i=0;i<8;i++)
      {
          do_lookup_medicine_io(addr,&m_i2c_dev[addr].keep[i+index*8],m_i2c_dev[addr].gpio[index] & (0x01<<i));

      }
}
void keyboard_lookup_io(int addr,int index)
{
      for(int i=4;i<8;i++)
      {
          do_keyboard_lookup_io(addr,(i-4)+index*4,m_i2c_keyboard_dev[addr].gpio[0] & (0x01<<i));

      }
}
void lookup_io(int addr,int index)
{
      for(int i=0;i<8;i++)
      {
          do_lookup_io(&m_i2c_dev[addr].keep[i+index*8],m_i2c_dev[addr].gpio[index] & (0x01<<i));

      }
}

void thread_init()
{
    int i,j;

    for(i=0;i<MAX_I2C_DEV;i++)
    {
      m_i2c_dev[i].addr = i;
      m_i2c_dev[i].is_init = 0;

      m_i2c_dev[i].is_start = 0;
      m_i2c_dev[i].gpio[1] = 0x55;

      for(j=0;j<MAX_GPIO_PORT;j++)
      {
          m_i2c_dev[i].keep[j].index = j;
          m_i2c_dev[i].keep[j].state = GPIO_OPEN;
          m_i2c_dev[i].keep[j].count = 0;
          m_i2c_dev[i].keep[j].max_count = MAX_ACTIVE_COUNT;
          m_i2c_dev[i].keep[j].open_event = 0;
          m_i2c_dev[i].keep[j].close_event = 0;

      }
    }

    //m_i2c_dev[0].enable = 0;
}
void doi2cWork( void )
{
    //data = data;
    LOGD("hello from thread...");
    m_is_run = 1;

    while(m_is_run)
    {
      if(m_is_sos_mode)
        loop();
      else 
        keyboard_loop();  

        usleep(10000);  
    }


    return ;
}


//static pthread_t m_thread;

JNIEXPORT jstring JNICALL
Java_com_weema_sipsample_service_i2cDriver_stringFromJNI( JNIEnv* env, jobject thiz )
{
#if defined(__arm__)
    #if defined(__ARM_ARCH_7A__)
    #if defined(__ARM_NEON__)
      #if defined(__ARM_PCS_VFP)
        #define ABI "armeabi-v7a/NEON (hard-float)"
      #else
        #define ABI "armeabi-v7a/NEON"
      #endif
    #else
      #if defined(__ARM_PCS_VFP)
        #define ABI "armeabi-v7a (hard-float)"
      #else
        #define ABI "armeabi-v7a"
      #endif
    #endif
  #else
   #define ABI "armeabi"
  #endif
#elif defined(__i386__)
#define ABI "x86"
#elif defined(__x86_64__)
#define ABI "x86_64"
#elif defined(__mips64)  /* mips64el-* toolchain defines __mips__ too */
#define ABI "mips64"
#elif defined(__mips__)
#define ABI "mips"
#elif defined(__aarch64__)
#define ABI "arm64-v8a"
#else
#define ABI "unknown"
#endif

    char outbuf[100];

    sprintf(outbuf,"Hello from JNI !  Compiled with ABI  %s . ",ABI);
    return env->NewStringUTF(outbuf);
}

static std::thread* m_p_thread;
JNIEXPORT jint JNICALL
Java_com_weema_sipsample_service_i2cDriver_init( JNIEnv* env, jclass object, jbyteArray j_index_array,
jbyteArray j_dir_array,jbyteArray j_init_array,jbyteArray j_count_array,jbyteArray j_mode_array)
{
    if(!m_p_thread)
    {
        thread_init();

    }
   //std::thread m_thread( doi2cWork ).detach();

   //m_thread.detach();
  //pthread_create(&m_thread, NULL, doi2cWork, NULL); // 建立子執行緒

  jbyte *c_index_array = env->GetByteArrayElements(j_index_array, 0);
  jbyte *c_dir_array = env->GetByteArrayElements(j_dir_array, 0);
  jbyte *c_init_array = env->GetByteArrayElements(j_init_array, 0);
  jbyte *c_count_array = env->GetByteArrayElements(j_count_array, 0);
  jbyte *c_mode_array = env->GetByteArrayElements(j_mode_array, 0);

  int len_arr = env->GetArrayLength(j_dir_array);
  //2. 具體處理
  //LOGD("i2c read  ... %d ",(int)addr);
  int index = c_index_array[0];

  for(int i=0; i<len_arr; i ++ ){
      int io_addr;
      int j;
      if(i<8)
      {
        io_addr = 0;
        j=i;
      }
      else
      {
         io_addr = 1;
         j=i-8;
      }

      if(c_dir_array[i] > 0)
          m_i2c_dev[index].dir[io_addr] = m_i2c_dev[index].dir[io_addr] | (0x01<<j);
      else
          m_i2c_dev[index].dir[io_addr] = m_i2c_dev[index].dir[io_addr] & ~(0x01<<j);

      if(index == 0)
      {
        if(c_dir_array[i] > 0)
          m_i2c_keyboard_dev[index].dir[io_addr] = m_i2c_keyboard_dev[index].dir[io_addr] | (0x01<<j);
        else
          m_i2c_keyboard_dev[index].dir[io_addr] = m_i2c_keyboard_dev[index].dir[io_addr] & ~(0x01<<j);
      }
  }

  len_arr = env->GetArrayLength(j_init_array);

  for(int i=0; i<len_arr; i ++ ){

      m_i2c_dev[index].keep[i].state = c_init_array[i];

      if(index == 0)
      {
        m_i2c_keyboard_dev[index].keep[i].state = c_init_array[i];
      }
  }

  for(int i=0; i<len_arr; i ++ ){
      int io_addr;
      int j;
      if(i<8)
      {
        io_addr = 0;
        j=i;
      }
      else
      {
         io_addr = 1;
         j=i-8;
      }

      if(c_init_array[i] > 0)
          m_i2c_dev[index].gpio[io_addr] = m_i2c_dev[index].gpio[io_addr] | (0x01<<j);
      else
          m_i2c_dev[index].gpio[io_addr] = m_i2c_dev[index].gpio[io_addr] & ~(0x01<<j);

      if(index == 0)
      {
       if(c_init_array[i] > 0)
          m_i2c_keyboard_dev[index].gpio[io_addr] = m_i2c_keyboard_dev[index].gpio[io_addr] | (0x01<<j);
        else
          m_i2c_keyboard_dev[index].gpio[io_addr] = m_i2c_keyboard_dev[index].gpio[io_addr] & ~(0x01<<j);
       
      }
  }

  for(int i=0; i<len_arr; i ++ ){

      m_i2c_dev[index].keep[i].max_count = c_count_array[i];

      if(index == 0)
      {
        m_i2c_keyboard_dev[index].keep[i].max_count = c_count_array[i];
      
      }
  }
  len_arr = env->GetArrayLength(j_mode_array);

  if(index == 0)
  {
    for(int i=0; i<len_arr; i ++ ){

      if(i==0)
        m_is_sos_mode = c_mode_array[i];

    }  
  }
  //3. 釋放記憶體
  env->ReleaseByteArrayElements(j_index_array, c_index_array, 0);
  //4. 賦值
  env->ReleaseByteArrayElements(j_dir_array, c_dir_array, 0);
  env->ReleaseByteArrayElements(j_init_array, c_init_array, 0);
  env->ReleaseByteArrayElements(j_count_array, c_count_array, 0);
  env->ReleaseByteArrayElements(j_mode_array, c_mode_array, 0);

  m_i2c_dev[index].enable = 1;
  m_i2c_dev[index].is_init = 1;

  if(index == 0)
  {
      m_i2c_keyboard_dev[index].enable = 1;
      m_i2c_keyboard_dev[index].is_init = 1;

  }
  unsigned char buf[1] = {0x0};
  unsigned char ret;

  LOGD("Java_com_weema_sipsample_service_i2cDriver_init %d",index);
  //m_i2c_dev[0].dir[0] = 0xFF;
  //m_i2c_dev[0].dir[1] = 0xFF;
#if 0
  m_i2c_dev[0].dir[0] = 0xFF;
  m_i2c_dev[0].dir[1] = 0xFF;

  m_i2c_dev[0].is_init = 1;
#endif
#if 0
  ret = i2c_write(0,0x00,buf,1);
  ret = i2c_write(0,0x01,buf,1);
  buf[0] = 0xFF;
  ret = i2c_write(0,0x0C,buf,1);
  ret = i2c_write(0,0x0D,buf,1);

  i2c_write(0,0x12,buf,1);
  buf[0] = 0;
  i2c_write(0,0x13,buf,1);
#endif

    jint jtotal=m_total;
    //doi2cWork(0);

    if(!m_p_thread)
    {
        m_p_thread = new std::thread(doi2cWork);

    }    
    return jtotal;
}
void JNIEXPORT
Java_com_weema_sipsample_service_i2cDriver_close( JNIEnv* env, jobject thiz )
{
   i2c_close();
   m_is_run = 0;
   if(m_p_thread)
   {
       m_p_thread->join();
       delete m_p_thread;

       m_p_thread = 0;
   }
}

JNIEXPORT jbyteArray JNICALL
Java_com_weema_sipsample_service_i2cDriver_write
(JNIEnv *env, jclass object, jbyteArray j_array){
    //1. 獲取陣列指標和長度
    jbyte *c_array = env->GetByteArrayElements(j_array, 0);
    int len_arr = env->GetArrayLength(j_array);
    //2. 具體處理
    jbyteArray c_result = env->NewByteArray(len_arr);
    jbyte jbuf[len_arr];

    unsigned char buf[1];
    int i;

    i = c_array[0];

    switch(c_array[1])
    {
      case BZ_CMD:

      m_i2c_dev[i].bz_addr = c_array[2];
      m_i2c_dev[i].bz_val = c_array[3];
      m_i2c_dev[i].is_bz = 1;
      break;
      case LED_CMD:

      m_i2c_dev[i].led_addr = c_array[2];
      m_i2c_dev[i].led_val = c_array[3];
      m_i2c_dev[i].is_led = 1;
      break;
      case BZ_LONG_CMD:

      m_i2c_dev[i].bz_addr = c_array[2];
      m_i2c_dev[i].bz_val = c_array[3];
      m_i2c_dev[i].is_bz_long = 1;
      break;
      case BZ_SHORT_CMD:

      m_i2c_dev[i].bz_addr = c_array[2];
      m_i2c_dev[i].bz_val = c_array[3];
      m_i2c_dev[i].is_bz_short = 1;
      break;
      case OPEN_DOOR_CMD:

      m_i2c_dev[i].open_door_addr = c_array[2];
      m_i2c_dev[i].open_door_val = c_array[3];
      m_i2c_dev[i].is_open_door = 1;
      break;
      case CTRL_GAS_CMD:

      m_i2c_dev[i].ctrl_gas_addr = c_array[2];
      m_i2c_dev[i].ctrl_gas_val = c_array[3];
      m_i2c_dev[i].is_ctrl_gas = 1;
      break;
      case CTRL_BZ_CMD:

      m_i2c_dev[i].ctrl_bz_addr = c_array[2];
      m_i2c_dev[i].ctrl_bz_val = c_array[3];
      m_i2c_dev[i].is_ctrl_bz = 1;
      break;      
      default:
      break;
    }

    m_i2c_dev[i].is_write = 1;

    //i2c_write(0,0x12,buf,1);
    //i2c_write(0,0x13,buf,1);
    //3. 釋放記憶體
    env->ReleaseByteArrayElements(j_array, c_array, 0);
    //4. 賦值
    env->SetByteArrayRegion(c_result, 0, len_arr, jbuf);
    return c_result;
}
JNIEXPORT jbyteArray JNICALL
Java_com_weema_sipsample_service_i2cDriver_read
(JNIEnv *env, jclass object, jbyteArray j_array){
  //1. 獲取陣列指標和長度
  jbyte *c_array = env->GetByteArrayElements(j_array, 0);
  int len_arr = env->GetArrayLength(j_array);
  //2. 具體處理
  jbyteArray c_result = env->NewByteArray(MAX_GPIO_PORT*3);
  jbyte jbuf[MAX_GPIO_PORT*3];
  unsigned char addr;

  addr = c_array[0];
  //LOGD("i2c read  ... %d ",(int)addr);

  for(int i=0; i<MAX_GPIO_PORT; i ++ ){
      jbuf[i] = m_i2c_dev[addr].keep[i].state;

      if(m_is_sos_mode == 0)    continue;

      jbuf[MAX_GPIO_PORT+i] = m_i2c_dev[addr].keep[i].close_event;
      if(jbuf[MAX_GPIO_PORT+i] > 0)
        m_i2c_dev[addr].keep[i].close_event--;
      else 
        m_i2c_dev[addr].keep[i].close_event = 0;

      jbuf[MAX_GPIO_PORT*2+i] = m_i2c_dev[addr].keep[i].open_event;
      if(jbuf[MAX_GPIO_PORT*2+i] > 0)
        m_i2c_dev[addr].keep[i].open_event --;
      else 
        m_i2c_dev[addr].keep[i].open_event =0;
  }

  if(addr == 0 && m_is_sos_mode == 0)
  {
    for(int i=0; i<MAX_GPIO_PORT; i ++ ){
     
      jbuf[MAX_GPIO_PORT+i] = m_i2c_keyboard_dev[addr].keep[i].close_event;
      if(jbuf[MAX_GPIO_PORT+i] > 0)
        m_i2c_keyboard_dev[addr].keep[i].close_event--;
      else 
        m_i2c_keyboard_dev[addr].keep[i].close_event = 0;

      jbuf[MAX_GPIO_PORT*2+i] = m_i2c_keyboard_dev[addr].keep[i].open_event;
      if(jbuf[MAX_GPIO_PORT*2+i] > 0)
        m_i2c_keyboard_dev[addr].keep[i].open_event --;
      else 
        m_i2c_keyboard_dev[addr].keep[i].open_event =0;
    }
  }

  //3. 釋放記憶體
  env->ReleaseByteArrayElements(j_array, c_array, 0);
  //4. 賦值
  env->SetByteArrayRegion(c_result, 0, MAX_GPIO_PORT*3, jbuf);
  return c_result;
}
