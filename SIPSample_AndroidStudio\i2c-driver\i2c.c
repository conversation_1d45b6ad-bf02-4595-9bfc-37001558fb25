#include <stdio.h>
#include <linux/types.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ioctl.h>
#include <errno.h>
#include <string.h>
#include "i2c.h"

#define I2C_RETRIES 0x0701
#define I2C_TIMEOUT 0x0702
#define I2C_RDWR 0x0707

#define DEV_NAME "/dev/i2c-1"

static void _alpu_delay_ms(unsigned int i);

struct i2c_msg {
          __u16 addr; /* slave address */
          __u16 flags; /* 標志（讀、寫） */
          __u16 len; /* msg length */
          __u8 *buf; /* pointer to msg data */
      };

struct i2c_rdwr_ioctl_data {
          struct i2c_msg __user *msgs; /* pointers to i2c_msgs */
          __u32 nmsgs; /* number of i2c_msgs */
      };

static int m_i2c_fd;

//static char m_read_buf[2 *sizeof(struct i2c_msg)];
static char m_write_buf[1 *sizeof(struct i2c_msg)];
static char * m_read_buf = m_write_buf;

/*****************************************************************************
  i2c读函数，参数1：从设备地址，参数2：寄存器地址，参数3：读取数据缓冲区，参数4：读取数据大小
******************************************************************************/
unsigned char i2c_read(unsigned char device_addr, unsigned char sub_addr, unsigned char *buff, int ByteNo)
{
    int fd, ret;
    unsigned char buftmp[32];
    struct i2c_rdwr_ioctl_data i2c_data;
    const char      *i2c_dev = DEV_NAME;
    //----------------------------------

    device_addr  = device_addr | 0x20;

    //init
    if(m_i2c_fd <= 0)
    {
        m_i2c_fd = open(i2c_dev, O_RDWR);
        if (m_i2c_fd<0)
        {
            //LOGD("not have /dev/i2c-0 t\r\n");
            return -1;
        }
    }

    i2c_data.nmsgs = 1;
    //i2c_data.msgs = (struct i2c_msg *)malloc(i2c_data.nmsgs *sizeof(struct i2c_msg));
    i2c_data.msgs = (struct i2c_msg *)m_read_buf;
    if (i2c_data.msgs == NULL)
    {
        LOGD("malloc error");
        close(m_i2c_fd);
        m_i2c_fd = 0;
        return -2;
    }

    ioctl(m_i2c_fd, I2C_TIMEOUT, 2);
    ioctl(m_i2c_fd, I2C_RETRIES, 2);

    //write reg
    memset(buftmp, 0, 32);
    buftmp[0] = sub_addr;
    i2c_data.msgs[0].len = 1;
    i2c_data.msgs[0].addr = device_addr;
    i2c_data.msgs[0].flags = 0;     // 0: write 1:read
    i2c_data.msgs[0].buf = buftmp;

    ret = ioctl(m_i2c_fd, I2C_RDWR, (unsigned long)&i2c_data);
    usleep(10);

    //read data
    i2c_data.msgs[0].len = ByteNo;
    i2c_data.msgs[0].addr = device_addr;
    i2c_data.msgs[0].flags = 1;     // 0: write 1:read
    i2c_data.msgs[0].buf = buff;

    ret = ioctl(m_i2c_fd, I2C_RDWR, (unsigned long)&i2c_data);
    if (ret < 0)
    {
        LOGD("read data %x %x error\r\n", device_addr, sub_addr);
        //close(m_i2c_fd);
        //m_i2c_fd = 0;
        //free(i2c_data.msgs);
        return 1;
    }
    //free(i2c_data.msgs);


#if 0
    int i;
    LOGD("i2c__read 0x%02x:",buftmp[0]);
    for (i = 0; i < ByteNo; i++)
    {
    LOGD(" 0x%02x",buff[i]);
    }
    LOGD("\n");
#endif
    //_alpu_delay_ms(1);

    return 0;
}

/*****************************************************************************
  i2c写函数，参数1：从设备地址，参数2：寄存器地址，参数3：要写入的数据缓冲区，参数4：写入数据大小
******************************************************************************/
unsigned char i2c_write(unsigned char device_addr, unsigned char sub_addr, unsigned char *buff, int ByteNo)
{
    int fd, ret;
    unsigned char buftmp[32];
    struct i2c_rdwr_ioctl_data i2c_data;
    const char      *i2c_dev = DEV_NAME;
    //----------------------------------

    device_addr  = device_addr | 0x20;
    //init

    if(m_i2c_fd <= 0)
        m_i2c_fd = open(i2c_dev, O_RDWR);
    if (m_i2c_fd <= 0)
    {
        //LOGD("not have /dev/i2c-0\r\n");
        return -1;
    }

    i2c_data.nmsgs = 1;
    //i2c_data.msgs = (struct i2c_msg *)malloc(i2c_data.nmsgs *sizeof(struct i2c_msg));
    i2c_data.msgs = (struct i2c_msg *)m_write_buf;

    if (i2c_data.msgs == NULL)
    {
        LOGD("malloc error");
        close(m_i2c_fd);
        m_i2c_fd = 0;
        return -1;
    }

    ioctl(m_i2c_fd, I2C_TIMEOUT, 1);
    ioctl(m_i2c_fd, I2C_RETRIES, 2);

    memset(buftmp, 0, 32);
    buftmp[0] = sub_addr;
    memcpy(buftmp + 1, buff, ByteNo);
    i2c_data.msgs[0].len = ByteNo + 1;
    i2c_data.msgs[0].addr = device_addr;
    i2c_data.msgs[0].flags = 0;     // 0: write 1:read
    i2c_data.msgs[0].buf = buftmp;
    ret = ioctl(m_i2c_fd, I2C_RDWR, (unsigned long)&i2c_data);
    if (ret < 0)
    {
        LOGD("write reg %x %x error\r\n", device_addr, sub_addr);
        close(m_i2c_fd);
        m_i2c_fd = 0;
        //free(i2c_data.msgs);
        return 1;
    }
    //free(i2c_data.msgs);

#if 0
    int i;
    LOGD("i2c_write 0x%02x:",buftmp[0]);
    for(i=0; i<ByteNo; i++)
    {
    LOGD(" 0x%02x",buftmp[1+i]);
    }
    LOGD("\n");
#endif
    _alpu_delay_ms(1);
    return 0;
}

void i2c_open()
{

}

void i2c_close()
{
  if(m_i2c_fd > 0)
  {
      close(m_i2c_fd);
      m_i2c_fd = 0;
  }
}
/*****************************************************************************
  延时函数（ms）
******************************************************************************/
void _alpu_delay_ms(unsigned int i)
{
    usleep(2000 * i);
}
