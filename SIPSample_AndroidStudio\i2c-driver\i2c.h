#ifndef __I2C_H__
#define __I2C_H__

#include <android/log.h>

#define  LOG_TAG    "your-log-tag"

#define  LOGD(...)  __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

#ifdef __cplusplus
extern "C" {
#endif
unsigned char i2c_read(unsigned char device_addr, unsigned char sub_addr, unsigned char *buff, int ByteNo);
unsigned char i2c_write(unsigned char device_addr, unsigned char sub_addr, unsigned char *buff, int ByteNo);


void i2c_open();
void i2c_close();
#ifdef __cplusplus
}
#endif
#endif
