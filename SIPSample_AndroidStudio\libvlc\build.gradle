apply plugin: 'com.android.library'
apply plugin: 'com.github.dcendents.android-maven'
group = 'com.github.pedroSG94'

android {
  compileSdkVersion 27
  buildToolsVersion "28.0.3"

  defaultConfig {
    minSdkVersion 16
    targetSdkVersion 28
    versionCode 1
    versionName "2.5.14"

    sourceSets.main {
      jni.srcDirs = []
      jniLibs.srcDir "src/main/jniLibs"
    }
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }
  }

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_7
    targetCompatibility JavaVersion.VERSION_1_7
  }
}

dependencies {
  implementation 'com.android.support:support-annotations:27.1.1'
}
