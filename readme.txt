2025-04-21
調整 mic、sp 音量 10 格
開啟降噪功能
修正 ptool 喇叭音量沒有設定成功的問題
航空城更改為新 sip server freepbx

********0.20.1
1 調整音量的設定方式

********0.20
1 SOS 會一直call下去
2 SOS 會按照順序call

********0.19
1 保全解發，如果沒有接會依序call下去
2 室內機解除保全 門口機會跟著連動

********0.18
1 app設定/解除保全 門口機會跟著連動

********0.17
1 配合手機APP保全功能修改一版
2 設定頁裡的中繼IP頁可以設定APP number

********0.16
1 fix conf 通話會閃屏問題，目前10分鐘沒有閃屏

********0.15
1 改成警報通話結束後，如果沒有解除會再顯示警報訊息。

********0.14
1 fix 未接來電的bug
2 fix some bug

********0.13
1 fix 未接來電的bug

********0.12
1 fix 強制通話的bug
2 remove some tip

********0.11
1 可設定要不要接管理室的強制通話
2 fix null bug

********0.10
1 remove debug message

********0.9
1 增加通話中顯示門的狀態

********0.1 
1 fix open door bug
2 套件更新

********0
1 啟動時會執行watchdog app

從1.99.21改過來
2 新增來電和警報喚醒
3 可使系統進入休眠狀態

********
1 門鈴要按時秒
2 如果沒有註冊就不會撥打電話

********
1 增加可馬上啟動更新機制
2 警報聲音調大
3 ptool 1.11

1.999.3.3
解決撥打電話沒有 timeout的問題

********
add network setting , ptool 1.10

******** 
fixed some bugs

1.99.4
remote close add call handup
1.99.3
大門口機每次打都是用影像打

1.99.2
SOS call add ringback tone

1.99.1
fix app6 bug

1.99.128
當有出現當機，會在一段時間回到主劃面

1.98.128
fix use findSessionBySessionID bug

1.97.128
增加保全delay 警報
1.96.128
當機時不顥示dialog

1.95.128
解一些會當機的問題

1.94.128
sos mode 有變化需重闕機
sos 30秒關掉 bz
增加保護

1.93.128
解決sos key的問題

1.89.128
可測試GB1 input pin

1.84.128
新增bz 聲音大小
在設定頁裡其他設定
ptool 1.7

1.82.128
新加大門口機操作

1.81.128
撥號中按了spk表示開視訊
next key 為清除鍵
通話中spk 功能為接電話和掛電話

1.80.128
擴音可接電話
remove some i2c message

1.79.129
管理室為129

1.79.128
可經由外部設定SOS 或keyboard mode

1.78.128
可接按鍵操作，這一版預設接按鍵

1.72.129
管理室為129

1.72.128
修正一些問題
當程式出錯時如果在啟動10秒後APP會重啟動

1.71.129
管理室為129
1.71.128
增加ringbacktone
管理室號碼由version最後號碼決定

1.70.25
修正AGC預設值的問題
1.70.24
電腦增加AGC 設定,
AGC值以ptool 1.4為主
AGC default off
收到connect 才調整mic音量

1.70.23
outgoing call 才調整mic音量

1.70.22
接通後慢慢調整mic音量
整理timeout
處理onhook完善

1.70.21
解決通訊錄當機的問題
解決通話中影像的問題

1.70.20
接通後才調整大小聲

1.70.19
檢查CallManager.Instance().getCurrentSession()的傳回值
調整mic phone的音量

1.70.18
playSOSAlarmWav 如沒有警報就不做playAudioFileToRemote

1.70.17
不調整mic phone的音量


1.70.16
增加一些保護，避免當機(Cont.)


1.70.15
增加一些保護，避免當機

1.70.14
每次啟動都會 restart network

1.70.13
停止網路連線

1.70.12
增加警報通話語音播放

1.70.11
fix app6 bugs

1.70.10
解除瓦斯警報，斷漏器得原
1.70.9
增加瓦斯斷漏器設定

1.70.8
增加警報通話設定
每次都取得IP

1.70.7
如果不在主畫面下，5分鐘後會顯示主畫面

1.70.6
base 1.70.4 
add 警報通知設定
add 來電時就調mic
根據center調整時間
fix some bugs

1.70.5
修正切換影像的問題

1.70.4
針對可能造成當機的地方做修改

1.70.3
增加訪客錄影功能

1.70.2 
修改通話中會當機的問題
增加基本的鈴聲

1.70.1
增加ipcam 功能

1.70
以1.49鈴聲和影像修改一版

